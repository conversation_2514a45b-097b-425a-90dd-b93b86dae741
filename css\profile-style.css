:root {
    --profile-bg: #ffffff;
    --profile-text: #333333;
    --profile-card-bg: #f8f9fa;
    --profile-border: #dee2e6;
    --profile-tab-active: #007bff;
    --profile-modal-bg: #ffffff;
    --profile-input-bg: #ffffff;
    --profile-input-text: #333333;
}

[data-theme="dark"] {
    --profile-bg: #1a1a1a;
    --profile-text: #e4e4e4;
    --profile-card-bg: #2d2d2d;
    --profile-border: #404040;
    --profile-tab-active: #0056b3;
    --profile-modal-bg: #2d2d2d;
    --profile-input-bg: #333333;
    --profile-input-text: #e4e4e4;
}

/* تحديث المتغيرات لتتوافق مع نظام Bootstrap */
[data-bs-theme="light"] {
    --profile-bg: #ffffff;
    --profile-text: #333333;
    --profile-card-bg: #f8f9fa;
    --profile-border: #dee2e6;
}

[data-bs-theme="dark"] {
    --profile-bg: #1a1a1a;
    --profile-text: #e4e4e4;
    --profile-card-bg: #2d2d2d;
    --profile-border: #404040;
}

/* تحديث الخصائص الرئيسية */
body {
    background-color: var(--profile-bg);
    color: var(--profile-text);
}

.profile-container {
    max-width: 1200px;
    margin: 2rem auto;
    background: var(--profile-bg);
    color: var(--profile-text);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    background-color: var(--profile-bg);
    color: var(--profile-text);
}

.profile-header {
    position: relative;
    padding: 2rem;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-border);
    border-radius: 8px 8px 0 0;
    text-align: center;
    background-color: var(--profile-card-bg);
    color: var(--profile-text);
    border-color: var(--profile-border);
}

.profile-avatar {
    margin: -75px auto 1rem;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 4px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    margin-bottom: 2rem;
}

.profile-info h1 {
    color: var(--profile-text);
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    margin: 0.5rem;
}

.badge i {
    margin-right: 0.5rem;
    margin-left: 0.5rem;
}

.admin-badge {
    background-color: #ffd700;
    color: #000;
}

.translator-badge {
    background-color: #20c997;
    color: #fff;
}

.reader-badge {
    background-color: #6c757d;
    color: #fff;
}

.profile-tabs {
    padding: 2rem;
}

.profile-tabs nav ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0 0 2rem;
    border-bottom: 2px solid var(--profile-border);
}

.profile-tabs nav li {
    margin-left: 2rem;
}

.profile-tabs nav a {
    display: block;
    padding: 0.5rem 0;
    color: var(--profile-text);
    text-decoration: none;
    position: relative;
}

.profile-tabs nav li.active a {
    color: var(--profile-tab-active);
}

.profile-tabs nav li.active a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--profile-tab-active);
}

.tab-content {
    background: var(--profile-card-bg);
    padding: 1rem;
    border: 1px solid var(--profile-border);
    background-color: var(--profile-card-bg);
    color: var(--profile-text);
    border-color: var(--profile-border);
}

.detail-item {
    margin-bottom: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--profile-border);
}

.detail-item .label {
    font-weight: bold;
    color: var(--profile-text);
    margin-left: 1rem;
}

.edit-profile-btn {
    display: inline-block;
    padding: 0.5rem 1.5rem;
    background: var(--profile-tab-active);
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    margin-top: 1rem;
}

.edit-profile-btn:hover {
    background: #0056b3;
    color: #fff;
}

.user-post {
    padding: 1rem;
    border-bottom: 1px solid var(--profile-border);
    border-color: var(--profile-border);
}

.user-post:last-child {
    border-bottom: none;
}

.user-post h3 {
    margin: 0 0 0.5rem;
}

.post-meta {
    color: var(--profile-text);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.user-post a {
    color: var(--profile-tab-active);
}

.user-post a:hover {
    color: var(--profile-text);
}

@media (max-width: 768px) {
    .profile-container {
        margin: 1rem;
    }
    
    .profile-tabs nav ul {
        flex-direction: column;
    }
    
    .profile-tabs nav li {
        margin: 0.5rem 0;
    }
    
    .profile-info h1 {
        font-size: 1.5rem;
    }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: var(--profile-modal-bg);
    color: var(--profile-text);
    margin: 10% auto;
    padding: 2rem;
    border-radius: 8px;
    max-width: 500px;
    position: relative;
    direction: rtl;
}

.close {
    position: absolute;
    left: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    cursor: pointer;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--profile-border);
    border-radius: 4px;
    background-color: var(--profile-input-bg);
    color: var(--profile-input-text);
    border-color: var(--profile-border);
}

.submit-btn {
    background: var(--profile-tab-active);
    color: #fff;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
}

.submit-btn:hover {
    background: #0056b3;
}

/* ... existing code ... */

/* Fix for profile picture modal text visibility in dark mode */
.gravatar-modal {
    color: #333; /* Dark text color that works in both light and dark modes */
}

.gravatar-modal input[type="email"] {
    color: #333;
    background-color: #fff;
}

.gravatar-modal a {
    color: #0073aa; /* WordPress default link color */
}

/* ... existing code ... */