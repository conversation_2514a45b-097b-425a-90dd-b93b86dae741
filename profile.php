<?php
get_header();

// Get username from URL
$username = get_query_var('profile_username');
if (empty($username)) {
    wp_redirect(home_url('/404'));
    exit;
}

// Get user by username
$user = get_user_by('slug', $username);
if (!$user) {
    wp_redirect(home_url('/404'));
    exit;
}

// Get user stats
$args = array(
    'post_type' => 'chapter',
    'author' => $user->ID,
    'posts_per_page' => -1,
    'post_status' => 'publish'
);
$chapters_query = new WP_Query($args);
$total_translated_chapters = $chapters_query->found_posts;

$args = array(
    'post_type' => 'novel',
    'author' => $user->ID,
    'posts_per_page' => -1,
    'post_status' => 'publish'
);
$novels_query = new WP_Query($args);
$total_added_novels = $novels_query->found_posts;

$args = array(
    'user_id' => $user->ID,
    'count' => true
);
$total_comments = get_comments($args);

// Get user avatar
$avatar_url = get_avatar_url($user->ID, array('size' => 150));
?>

<div class="container py-5">
    <div class="row">
        <!-- Profile Card Column -->
        <div class="col-md-4">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <img src="<?php echo esc_url($avatar_url); ?>" alt="<?php echo esc_attr($user->display_name); ?>">
                    </div>
                    <h3 class="profile-name"><?php echo esc_html($user->display_name); ?></h3>
                    <div class="profile-badges">
                        <?php if (in_array('administrator', (array) $user->roles)): ?>
                            <span class="badge admin-badge">إدارة <i class="fas fa-shield-alt"></i></span>
                        <?php elseif (in_array('translator', (array) $user->roles)): ?>
                            <span class="badge translator-badge">مترجم <i class="fas fa-language"></i></span>
                        <?php else: ?>
                            <span class="badge reader-badge">قارئ <i class="fas fa-book-reader"></i></span>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo esc_html($total_translated_chapters); ?></span>
                        <span class="stat-label">الفصول</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo esc_html($total_added_novels); ?></span>
                        <span class="stat-label">الروايات</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo esc_html($total_comments); ?></span>
                        <span class="stat-label">التعليقات</span>
                    </div>
                </div>

                <div class="profile-bio">
                    <?php 
                    $bio = get_user_meta($user->ID, 'description', true);
                    if (!empty($bio)): 
                    ?>
                        <p><?php echo nl2br(esc_html($bio)); ?></p>
                    <?php endif; ?>
                </div>

                <?php if (get_current_user_id() === $user->ID): ?>
                    <a href="<?php echo esc_url(admin_url('profile.php')); ?>" class="btn btn-primary btn-edit-profile">
                        <i class="fas fa-edit"></i> تعديل الملف الشخصي
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Content Column -->
        <div class="col-md-8">
            <div class="profile-tabs">
                <nav>
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" href="#about" data-bs-toggle="tab">نبذة</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#novels" data-bs-toggle="tab">الروايات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#chapters" data-bs-toggle="tab">الفصول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#comments" data-bs-toggle="tab">التعليقات</a>
                        </li>
                    </ul>
                </nav>

                <div class="tab-content">
                    <!-- Tab panes content here -->
                    <!-- Chapters Tab -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4>الفصول المترجمة</h4>
                        </div>
                        <div class="card-body">
                            <?php if ($chapters_query->have_posts()): ?>
                                <div class="list-group">
                                    <?php while ($chapters_query->have_posts()): $chapters_query->the_post(); 
                                        $novel_id = get_post_meta(get_the_ID(), '_novel_id', true);
                                        $novel = get_post($novel_id);
                                        $chapter_number = get_post_meta(get_the_ID(), '_chapter_number', true);
                                    ?>
                                        <a href="<?php echo get_permalink(); ?>" class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1">
                                                    <?php echo esc_html($novel->post_title); ?> - 
                                                    الفصل <?php echo esc_html($chapter_number); ?>
                                                </h5>
                                                <small><?php echo get_the_date(); ?></small>
                                            </div>
                                        </a>
                                    <?php endwhile; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-muted">لا توجد فصول مترجمة بعد.</p>
                            <?php endif; ?>
                            <?php wp_reset_postdata(); ?>
                        </div>
                    </div>

                    <!-- Novels Tab -->
                    <?php if ($novels_query->have_posts()): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4>الروايات المضافة</h4>
                        </div>
                        <div class="card-body">
                            <div class="list-group">
                                <?php while ($novels_query->have_posts()): $novels_query->the_post(); ?>
                                    <a href="<?php echo get_permalink(); ?>" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h5 class="mb-1"><?php the_title(); ?></h5>
                                            <small><?php echo get_the_date(); ?></small>
                                        </div>
                                    </a>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php wp_reset_postdata(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-card {
    background: var(--bs-light);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.profile-header {
    text-align: center;
    margin-bottom: 20px;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    margin: 0 auto 15px;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.profile-name {
    margin: 10px 0;
    color: var(--bs-dark);
}

.profile-badges {
    margin: 10px 0;
}

.badge {
    padding: 5px 10px;
    margin: 0 5px;
    border-radius: 15px;
}

.admin-badge {
    background-color: #dc3545;
    color: white;
}

.translator-badge {
    background-color: #198754;
    color: white;
}

.reader-badge {
    background-color: #0d6efd;
    color: white;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    padding: 15px 0;
    border-top: 1px solid rgba(0,0,0,0.1);
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5em;
    font-weight: bold;
    color: var(--bs-primary);
}

.stat-label {
    font-size: 0.9em;
    color: var(--bs-secondary);
}

.profile-bio {
    margin: 20px 0;
    padding: 15px;
    background: rgba(0,0,0,0.05);
    border-radius: 5px;
}

.btn-edit-profile {
    width: 100%;
    margin-top: 15px;
}

.dark-mode .profile-card {
    background: var(--bs-dark);
}

.dark-mode .profile-name {
    color: var(--bs-light);
}

.dark-mode .stat-label {
    color: var(--bs-light);
}

.dark-mode .profile-bio {
    background: rgba(255,255,255,0.05);
}
</style>

<?php get_footer(); ?>
