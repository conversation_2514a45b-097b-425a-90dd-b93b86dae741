<?php
/* Template Name: Profile Page */

// Redirect non-logged-in users to the login page
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

wp_enqueue_style('profile-modern', get_template_directory_uri() . '/css/profile-modern.css');
wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

// Include Elite badge functionality
require_once get_template_directory() . '/inc/elite-badge.php';

get_header();

$user_id = get_current_user_id();
$user = get_userdata($user_id);

// Get user statistics
$novels_count = count_user_posts($user_id, 'novel');
$chapters_count = count_user_posts($user_id, 'chapter');
$comments_count = get_comments(array(
    'user_id' => $user_id,
    'count' => true
));

// Get user's cover image or default
$cover_image = get_user_meta($user_id, 'cover_image', true);
$cover_style = $cover_image ? "background-image: url('" . esc_url($cover_image) . "')" : "background-color: #007bff";
?>

<main class="profile-modern theme-sensitive">
    <!-- Profile Header -->
    <div class="profile-header-modern">
        <div class="profile-cover-modern" style="<?php echo $cover_style; ?>"></div>
        <div class="profile-info-modern">
            <div class="profile-avatar-section">
                <?php
                $user_email = get_the_author_meta('user_email', $user_id);
                $gravatar_url = get_avatar_url($user_email, array(
                    'size' => 400, // زيادة حجم الصورة
                    'default' => 'mp',
                    'rating' => 'g',
                    'force_default' => false,
                ));
                ?>
                <div class="avatar-container">
                    <img src="<?php echo esc_url($gravatar_url); ?>" 
                         alt="<?php echo esc_attr($user->display_name); ?>'s Profile Picture" 
                         class="profile-avatar" 
                         id="profileAvatar"
                         loading="eager"
                         decoding="sync">
                    <button type="button" class="btn-update-avatar" onclick="openGravatarModal()" aria-label="تحديث صورة الملف الشخصي">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
            </div>
            <div class="profile-details-modern">
                <h1><?php echo esc_html($user->display_name); ?></h1>
                <?php if (current_user_can('administrator')): ?>
                    <span class="badge admin-badge">إدارة<i class="fas fa-shield-alt"></i></span>
                <?php elseif (current_user_can('translator')): ?>
                    <span class="badge translator-badge">مترجم<i class="fas fa-language"></i></span>
                <?php else: ?>
                    <span class="badge reader-badge">قارئ<i class="fas fa-book-reader"></i></span>
                <?php endif; ?>
                <?php display_elite_badge($user_id); ?>
                <p class="join-date">عضو منذ: <?php echo date_i18n(get_option('date_format'), strtotime($user->user_registered)); ?></p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="profile-stats-modern">
        <div class="stat-card animate-fade-in">
            <i class="fas fa-book stat-icon"></i>
            <div class="stat-value"><?php echo $novels_count; ?></div>
            <div class="stat-label">الروايات المنشورة</div>
        </div>
        <div class="stat-card animate-fade-in">
            <i class="fas fa-file-alt stat-icon"></i>
            <div class="stat-value"><?php echo $chapters_count; ?></div>
            <div class="stat-label">الفصول المترجمة</div>
        </div>
        <div class="stat-card animate-fade-in">
            <i class="fas fa-comments stat-icon"></i>
            <div class="stat-value"><?php echo $comments_count; ?></div>
            <div class="stat-label">التعليقات</div>
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="profile-tabs-modern">
        <nav class="tabs-nav-modern">
            <a href="#" class="tab-link-modern active" data-tab="novels">
                <i class="fas fa-book"></i> الروايات
            </a>
            <a href="#" class="tab-link-modern" data-tab="chapters">
                <i class="fas fa-file-alt"></i> الفصول
            </a>
            <a href="#" class="tab-link-modern" data-tab="comments">
                <i class="fas fa-comments"></i> التعليقات
            </a>
            <?php if ($user_id === get_current_user_id()): ?>
                <a href="#" class="tab-link-modern" data-tab="settings">
                    <i class="fas fa-cog"></i> الإعدادات
                </a>
            <?php endif; ?>
        </nav>

        <div class="tab-content-modern">
            <!-- Novels Tab -->
            <div id="novels" class="tab-pane-modern active">
                <div class="content-grid">
                    <?php
                    $novels = get_posts(array(
                        'post_type' => 'novel',
                        'author' => $user_id,
                        'posts_per_page' => -1
                    ));

                    if (!empty($novels)): 
                        foreach ($novels as $novel):
                            $cover = get_the_post_thumbnail_url($novel->ID) ?: get_template_directory_uri() . '/images/default-novel.jpg';
                    ?>
                    <div class="content-card animate-fade-in">
                        <div class="content-card-image" style="background-image: url('<?php echo esc_url($cover); ?>')"></div>
                        <div class="content-card-body">
                            <h3 class="content-card-title">
                                <a href="<?php echo get_permalink($novel->ID); ?>"><?php echo esc_html($novel->post_title); ?></a>
                            </h3>
                            <div class="content-card-meta">
                                <i class="far fa-calendar"></i> <?php echo get_the_date('', $novel->ID); ?>
                            </div>
                        </div>
                    </div>
                    <?php 
                        endforeach;
                    else:
                    ?>
                        <div class="no-content-message">
                            <i class="fas fa-book"></i>
                            <p>لا توجد روايات منشورة حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Chapters Tab -->
            <div id="chapters" class="tab-pane-modern">
                <div class="content-grid">
                    <?php
                    $chapters = get_posts(array(
                        'post_type' => 'chapter',
                        'author' => $user_id,
                        'posts_per_page' => -1
                    ));

                    if (!empty($chapters)):
                        foreach ($chapters as $chapter):
                            $novel_id = get_post_meta($chapter->ID, 'novel_id', true);
                            $novel_title = get_the_title($novel_id);
                    ?>
                    <div class="content-card animate-fade-in">
                        <div class="content-card-body">
                            <h3 class="content-card-title">
                                <a href="<?php echo get_permalink($chapter->ID); ?>"><?php echo esc_html($chapter->post_title); ?></a>
                            </h3>
                            <div class="content-card-meta">
                                <div><i class="fas fa-book"></i> <?php echo esc_html($novel_title); ?></div>
                                <div><i class="far fa-calendar"></i> <?php echo get_the_date('', $chapter->ID); ?></div>
                            </div>
                        </div>
                    </div>
                    <?php 
                        endforeach;
                    else:
                    ?>
                        <div class="no-content-message">
                            <i class="fas fa-file-alt"></i>
                            <p>لا توجد فصول مترجمة حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Comments Tab -->
            <div id="comments" class="tab-pane-modern">
                <div class="comments-list">
                    <?php
                    $comments = get_comments(array(
                        'user_id' => $user_id,
                        'status' => 'approve',
                        'order' => 'DESC'
                    ));

                    if (!empty($comments)):
                        foreach ($comments as $comment):
                            $post = get_post($comment->comment_post_ID);
                    ?>
                    <div class="comment-card animate-fade-in">
                        <div class="comment-header">
                            <div class="comment-novel">
                                <a href="<?php echo get_permalink($post->ID); ?>"><?php echo esc_html($post->post_title); ?></a>
                            </div>
                            <div class="comment-date">
                                <i class="far fa-clock"></i> <?php echo get_comment_date('', $comment->comment_ID); ?>
                            </div>
                        </div>
                        <div class="comment-content">
                            <?php echo wp_kses_post($comment->comment_content); ?>
                        </div>
                    </div>
                    <?php 
                        endforeach;
                    else:
                    ?>
                        <div class="no-content-message">
                            <i class="fas fa-comments"></i>
                            <p>لا توجد تعليقات حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Settings Tab -->
            <?php if ($user_id === get_current_user_id()): ?>
            <div id="settings" class="tab-pane-modern">
                <form id="profile-settings-form" class="settings-form" method="post">
                    <?php wp_nonce_field('profile_update_nonce', 'nonce'); ?>
                    <div class="form-group">
                        <label for="display_name">الاسم المعروض</label>
                        <input type="text" id="display_name" name="display_name" value="<?php echo esc_attr($user->display_name); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" value="<?php echo esc_attr($user->user_email); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="bio">نبذة عني</label>
                        <textarea id="bio" name="bio" rows="4"><?php echo esc_textarea(get_user_meta($user_id, 'description', true)); ?></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </form>
            </div>
            <?php endif; ?>
        </div>
    </div>
</main>

<!-- Gravatar Modal -->
<div id="gravatarModal" class="modal">
    <div class="modal-content">
        <button type="button" class="modal-close" onclick="closeGravatarModal()">
            <i class="fas fa-times"></i>
        </button>
        <h3>تحديث صورة الملف الشخصي</h3>
        <div class="gravatar-instructions">
            <p>لتحديث صورتك الشخصية عبر Gravatar، اتبع الخطوات التالية:</p>
            <ol>
                <li>قم بزيارة موقع <a href="https://gravatar.com" target="_blank" rel="noopener">Gravatar.com</a></li>
                <li>سجل الدخول باستخدام بريدك الإلكتروني:</li>
            </ol>
            <div class="gravatar-email">
                <?php echo esc_html($user_email); ?>
            </div>
            <ol start="3">
                <li>قم بتحميل أو تحديث صورتك الشخصية</li>
                <li>عد إلى هذه الصفحة واضغط F5 لتحديث الصفحة ورؤية صورتك الجديدة</li>
            </ol>
            <div class="gravatar-direct-link">
                <a href="https://gravatar.com" class="btn-gravatar" target="_blank" rel="noopener">
                    <i class="fas fa-external-link-alt"></i>
                    فتح Gravatar
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Global functions
window.openGravatarModal = function() {
    const modal = document.getElementById('gravatarModal');
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
};

window.closeGravatarModal = function() {
    const modal = document.getElementById('gravatarModal');
    modal.classList.remove('show');
    document.body.style.overflow = '';
};

window.refreshAvatar = function() {
    const avatar = document.getElementById('profileAvatar');
    if (avatar) {
        const timestamp = new Date().getTime();
        avatar.src = avatar.src.split('?')[0] + '?t=' + timestamp;
    }
};

// Add ajaxurl if it's not already defined
var ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('gravatarModal');
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === this) {
            closeGravatarModal();
        }
    });

    // Other existing code...
    const tabLinks = document.querySelectorAll('.tab-link-modern');
    const tabPanes = document.querySelectorAll('.tab-pane-modern');

    function switchTab(targetId) {
        // Hide all panes and deactivate all tabs
        tabPanes.forEach(pane => {
            pane.classList.remove('active');
        });
        tabLinks.forEach(link => {
            link.classList.remove('active');
        });

        // Show target pane and activate target tab
        const targetPane = document.getElementById(targetId);
        const targetTab = document.querySelector(`[data-tab="${targetId}"]`);
        
        if (targetPane && targetTab) {
            targetPane.classList.add('active');
            targetTab.classList.add('active');
        }
    }

    tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetTab = link.getAttribute('data-tab');
            switchTab(targetTab);
        });
    });

    // Form submission
    const settingsForm = document.getElementById('profile-settings-form');
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Add loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            submitButton.disabled = true;

            // Create FormData
            const formData = new FormData(this);
            formData.append('action', 'update_profile');
            
            // Send AJAX request
            fetch(ajaxurl, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('تم حفظ التغييرات بنجاح');
                    location.reload();
                } else {
                    // Show error message
                    alert(data.data.message || 'حدث خطأ أثناء حفظ التغييرات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ التغييرات');
            })
            .finally(() => {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        });
    }

    // إضافة مستمعي الأحداث للنافذة المنبثقة
    document.querySelector('.btn-update-avatar').addEventListener('click', function() {
        refreshAvatar();
    });

    // Animate statistics on load
    const animateValue = (element, start, end, duration) => {
        if (start === end) return;
        const range = end - start;
        let current = start;
        const increment = end > start ? 1 : -1;
        const stepTime = Math.abs(Math.floor(duration / range));
        const timer = setInterval(() => {
            current += increment;
            element.textContent = current;
            if (current === end) {
                clearInterval(timer);
            }
        }, stepTime);
    };

    document.querySelectorAll('.stat-value').forEach(stat => {
        const value = parseInt(stat.textContent);
        animateValue(stat, 0, value, 1000);
    });
});
</script>

<?php get_footer(); ?>