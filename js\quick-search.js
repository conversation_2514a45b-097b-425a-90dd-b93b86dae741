/**
 * Quick Search functionality for Sekaiplus theme
 */

jQuery(document).ready(function($) {
    console.log('Quick Search script loaded');
    const searchInput = $('#quickSearch');
    const searchResults = $('#searchResults');
    const searchForm = $('#quickSearchForm');
    let searchTimeout;

    console.log('Search input element:', searchInput.length > 0 ? 'Found' : 'Not found');
    console.log('Search results element:', searchResults.length > 0 ? 'Found' : 'Not found');

    // إظهار وإخفاء نتائج البحث عند التركيز وفقدان التركيز
    searchInput.on('focus', function() {
        console.log('Search input focused');
        if (searchInput.val().length >= 2) {
            searchResults.show();
        }
    });

    // إخفاء نتائج البحث عند النقر خارجها
    $(document).on('click', function(e) {
        if (!searchInput.is(e.target) && !searchResults.is(e.target) && searchResults.has(e.target).length === 0) {
            searchResults.hide();
        }
    });

    // منع السلوك الافتراضي لنموذج البحث
    searchForm.on('submit', function(e) {
        console.log('Search form submitted');
        e.preventDefault();
        performSearch();
    });

    // معالجة البحث عند الكتابة
    searchInput.on('input', function() {
        console.log('Search input changed:', $(this).val());
        const query = $(this).val();
        
        // مسح المؤقت السابق
        clearTimeout(searchTimeout);
        
        // إذا كان النص أقل من حرفين، أخفِ النتائج
        if (query.length < 2) {
            searchResults.hide();
            return;
        }
        
        // تعيين مؤقت جديد لتأخير البحث حتى يتوقف المستخدم عن الكتابة
        searchTimeout = setTimeout(function() {
            performSearch();
        }, 300); // تأخير 300 مللي ثانية
    });

    // تفعيل البحث عند الضغط على Enter
    searchInput.on('keypress', function(e) {
        if (e.which === 13) {
            console.log('Enter key pressed');
            e.preventDefault();
            if (searchInput.val().length >= 2) {
                performSearch();
            }
        }
    });

    // دالة موحدة لتنفيذ البحث
    function performSearch() {
        const query = searchInput.val();
        
        if (query.length < 2) {
            return;
        }
        
        console.log('Sending AJAX request for:', query);
        console.log('AJAX URL:', sekaiplus_search.ajax_url);
        console.log('Action:', 'sekaiplus_quick_search');
        console.log('Nonce:', sekaiplus_search.nonce);
        
        // عرض مؤشر التحميل
        searchResults.html('<div class="p-4 text-center"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>');
        searchResults.show();
        
        // إرسال طلب AJAX
        $.ajax({
            url: sekaiplus_search.ajax_url,
            type: 'POST',
            dataType: 'html',
            data: {
                action: 'sekaiplus_quick_search',
                nonce: sekaiplus_search.nonce,
                query: query
            },
            success: function(response) {
                console.log('AJAX response received');
                console.log('Response length:', response.length);
                
                // عرض النتائج
                if (response.length > 0) {
                    searchResults.html(response);
                    searchResults.show();
                    
                    // إضافة معلومات التصحيح
                    const debugInfo = $('<div class="debug-info mt-3 p-3 bg-light border-top">');
                    debugInfo.append('<h6>معلومات التصحيح</h6>');
                    debugInfo.append('<p><strong>Nonce:</strong> ' + sekaiplus_search.nonce + '</p>');
                    debugInfo.append('<p><strong>Query:</strong> ' + query + '</p>');
                    debugInfo.append('<p><strong>Action:</strong> sekaiplus_quick_search</p>');
                    debugInfo.append('<p><strong>AJAX URL:</strong> ' + sekaiplus_search.ajax_url + '</p>');
                    debugInfo.append('<p><strong>Status:</strong> Success</p>');
                    searchResults.append(debugInfo);
                } else {
                    searchResults.html('<div class="p-4 text-center text-muted">لم يتم العثور على نتائج</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                console.error('Response:', xhr.responseText);
                
                // عرض رسالة الخطأ
                searchResults.html('<div class="p-4 text-center text-danger">حدث خطأ أثناء البحث</div>');
                
                // إضافة معلومات التصحيح
                const debugInfo = $('<div class="debug-info mt-3 p-3 bg-light border-top">');
                debugInfo.append('<h6>معلومات التصحيح</h6>');
                debugInfo.append('<p><strong>Nonce:</strong> ' + sekaiplus_search.nonce + '</p>');
                debugInfo.append('<p><strong>Query:</strong> ' + query + '</p>');
                debugInfo.append('<p><strong>Action:</strong> sekaiplus_quick_search</p>');
                debugInfo.append('<p><strong>AJAX URL:</strong> ' + sekaiplus_search.ajax_url + '</p>');
                debugInfo.append('<p><strong>Status:</strong> ' + status + '</p>');
                debugInfo.append('<p><strong>Error:</strong> ' + error + '</p>');
                if (xhr.responseText) {
                    debugInfo.append('<p><strong>Response:</strong> <pre>' + xhr.responseText + '</pre></p>');
                }
                searchResults.append(debugInfo);
            }
        });
    }

    // تحقق من متغيرات AJAX
    if (typeof sekaiplus_search === 'undefined') {
        console.error('Error: sekaiplus_search is not defined');
    } else {
        console.log('sekaiplus_search is defined:', sekaiplus_search);
    }

    // تنفيذ اختبار تلقائي للبحث بعد تحميل الصفحة
    setTimeout(function() {
        if (searchInput.length > 0 && typeof sekaiplus_search !== 'undefined') {
            console.log('Running automated search test...');
            
            // عرض معلومات التصحيح في الصفحة
            const testContainer = $('<div id="searchTestContainer" class="container mt-5 mb-5">');
            testContainer.append('<h3>اختبار البحث بالآجاكس (AJAX)</h3>');
            testContainer.append('<div id="searchTestResults" class="card p-3"></div>');
            $('body').append(testContainer);
            
            const testResults = $('#searchTestResults');
            testResults.append('<p><strong>Nonce:</strong> ' + sekaiplus_search.nonce + '</p>');
            testResults.append('<p><strong>Query:</strong> تزوجت</p>');
            testResults.append('<p><strong>Action:</strong> sekaiplus_quick_search</p>');
            testResults.append('<p><strong>AJAX URL:</strong> ' + sekaiplus_search.ajax_url + '</p>');
            
            // إرسال طلب AJAX اختباري
            $.ajax({
                url: sekaiplus_search.ajax_url,
                type: 'POST',
                dataType: 'html',
                data: {
                    action: 'sekaiplus_quick_search',
                    nonce: sekaiplus_search.nonce,
                    query: 'تزوجت'
                },
                success: function(response) {
                    testResults.append('<p><strong>Status:</strong> Success</p>');
                    testResults.append('<div class="mt-3"><strong>النتائج:</strong></div>');
                    testResults.append('<div class="border p-3 mt-2">' + response + '</div>');
                },
                error: function(xhr, status, error) {
                    testResults.append('<p><strong>Status:</strong> ' + status + '</p>');
                    testResults.append('<p><strong>Error:</strong> ' + error + '</p>');
                    if (xhr.responseText) {
                        testResults.append('<p><strong>Response:</strong> <pre>' + xhr.responseText + '</pre></p>');
                    }
                }
            });
        }
    }, 2000); // انتظر 2000 مللي ثانية بعد تحميل الصفحة
});
