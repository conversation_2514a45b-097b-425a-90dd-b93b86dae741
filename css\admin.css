/**
 * WordPress Admin Area Styles
 */

/* Font Loading */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;700&display=swap');

/* RTL Admin Overrides */
body.rtl {
    font-family: 'Noto Kufi Arabic', 'Tajawal', -apple-system, BlinkMacSystemFont, sans-serif;
}

body.rtl #wpcontent,
body.rtl #wpfooter {
    margin-right: 160px;
    margin-left: 0;
}

@media screen and (max-width: 782px) {
    body.rtl #wpcontent,
    body.rtl #wpfooter {
        margin-right: 0;
    }
}

/* Custom Post Type Icons */
#dashboard_right_now .novel-count a:before {
    content: '\f331';
}

#dashboard_right_now .chapter-count a:before {
    content: '\f478';
}

/* Novel Meta Box Styles */
.novel-meta-box {
    margin: -6px -12px -12px;
}

.novel-meta-box .inside {
    margin: 0;
    padding: 0;
}

.novel-meta-field {
    padding: 12px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.novel-meta-field:last-child {
    border-bottom: none;
}

.novel-meta-field label {
    flex: 0 0 150px;
    font-weight: 600;
}

.novel-meta-field input[type="text"],
.novel-meta-field input[type="number"],
.novel-meta-field select,
.novel-meta-field textarea {
    flex: 1;
}

.novel-meta-field .description {
    margin-top: 4px;
    color: #666;
}

/* Chapter Meta Box Styles */
.chapter-meta-box {
    margin: -6px -12px -12px;
}

.chapter-meta-box .inside {
    margin: 0;
    padding: 0;
}

.chapter-meta-field {
    padding: 12px;
    border-bottom: 1px solid #eee;
}

.chapter-meta-field:last-child {
    border-bottom: none;
}

.chapter-meta-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
}

.chapter-meta-field input[type="text"],
.chapter-meta-field input[type="number"],
.chapter-meta-field select,
.chapter-meta-field textarea {
    width: 100%;
}

/* Admin List Table Customizations */
.wp-list-table .column-thumbnail {
    width: 60px;
}

.wp-list-table .column-thumbnail img {
    max-width: 40px;
    height: auto;
    border-radius: 4px;
}

.wp-list-table .column-rating,
.wp-list-table .column-chapters,
.wp-list-table .column-views {
    width: 10%;
    text-align: center;
}

/* Status Colors */
.status-ongoing {
    color: #007cba;
}

.status-completed {
    color: #46b450;
}

.status-hiatus {
    color: #ffb900;
}

.status-dropped {
    color: #dc3232;
}

/* Admin Menu Customizations */
#adminmenu .wp-menu-image.dashicons-book {
    color: #007cba;
}

#adminmenu .wp-menu-image.dashicons-format-aside {
    color: #46b450;
}

/* Quick Edit Panel */
#the-list .inline-edit-row fieldset.inline-edit-col-right {
    margin-top: 0;
}

/* Editor Styles */
.block-editor__container {
    font-family: 'Noto Kufi Arabic', 'Tajawal', -apple-system, BlinkMacSystemFont, sans-serif;
}

.editor-post-title__input {
    font-family: 'Tajawal', sans-serif !important;
    font-weight: 700 !important;
}

/* Dashboard Widgets */
.sekaiplus-dashboard-widget {
    padding: 12px;
}

.sekaiplus-dashboard-widget .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 16px;
}

.sekaiplus-dashboard-widget .stat-card {
    background: #fff;
    border: 1px solid #e5e5e5;
    padding: 12px;
    border-radius: 4px;
    text-align: center;
}

.sekaiplus-dashboard-widget .stat-card h3 {
    margin: 0 0 4px;
    color: #1e1e1e;
}

.sekaiplus-dashboard-widget .stat-card p {
    margin: 0;
    color: #757575;
}

/* Media Upload Button */
.novel-cover-upload {
    margin-top: 8px;
}

.novel-cover-preview {
    max-width: 150px;
    height: auto;
    margin: 8px 0;
    border-radius: 4px;
}

.novel-cover-remove {
    color: #dc3232;
    text-decoration: none;
    margin-right: 8px;
}

/* Form Validation */
.form-field-error {
    border-color: #dc3232 !important;
}

.form-field-error-message {
    color: #dc3232;
    font-size: 12px;
    margin-top: 4px;
}

/* Help Text */
.contextual-help-tabs .active {
    border-right: 2px solid #007cba;
}

.contextual-help-sidebar {
    padding: 12px;
}
