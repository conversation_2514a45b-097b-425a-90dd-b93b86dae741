jQuery(document).ready(function($) {
    const darkModeToggle = $('#darkModeToggle');
    const body = $('body');

    // استرجاع الحالة المحفوظة
    const isDarkMode = localStorage.getItem('darkMode') === '1' || localStorage.getItem('darkMode') === 'enabled';

    // تعيين الحالة الأولية
    if (isDarkMode) {
        body.addClass('dark-mode');
    } else {
        body.removeClass('dark-mode');
    }

    // تبديل الوضع المظلم
    darkModeToggle.on('click', function(e) {
        e.preventDefault();

        // تبديل الوضع فوراً للحصول على انتقال سلس
        const isNowDark = !body.hasClass('dark-mode');
        body.toggleClass('dark-mode');

        // حفظ في localStorage دائماً
        localStorage.setItem('darkMode', isNowDark ? '1' : '0');

        // حفظ أيضًا بالتنسيق القديم للتوافق مع الكود الموجود
        localStorage.setItem('darkMode', isNowDark ? 'enabled' : 'disabled');

        // حفظ التفضيل في السيرفر للمستخدمين المسجلين فقط
        if (typeof darkModeVars !== 'undefined' && darkModeVars.ajaxurl && darkModeVars.isLoggedIn === '1') {
            $.ajax({
                url: darkModeVars.ajaxurl,
                type: 'POST',
                data: {
                    action: 'sekaiplus_toggle_dark_mode',
                    nonce: darkModeVars.nonce,
                    isDark: isNowDark ? '1' : '0'
                }
            });
        }
    });

    // التحقق من تفضيل النظام عند التحميل الأولي إذا لم يكن هناك تفضيل محفوظ
    if (!localStorage.getItem('darkMode')) {
        const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)');
        if (prefersDarkScheme.matches) {
            body.addClass('dark-mode');
            localStorage.setItem('darkMode', '1');
        }

        // الاستجابة لتغييرات تفضيل النظام
        try {
            // استخدام addEventListener للمتصفحات الحديثة
            prefersDarkScheme.addEventListener('change', (e) => {
                if (!localStorage.getItem('darkMode')) {
                    const shouldBeDark = e.matches;
                    body.toggleClass('dark-mode', shouldBeDark);
                }
            });
        } catch (error) {
            // استخدام addListener للمتصفحات القديمة - للتوافق مع المتصفحات القديمة
            prefersDarkScheme.addListener((e) => {
                if (!localStorage.getItem('darkMode')) {
                    const shouldBeDark = e.matches;
                    body.toggleClass('dark-mode', shouldBeDark);
                }
            });
        }
    }
});
