<?php
/**
 * Template part for displaying novel chapters
 */

// الحصول على جميع المجلدات المتوفرة
global $post;
$novel_id = $post->ID;

$chapters = get_posts(array(
    'post_type' => 'chapter',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'meta_query' => array(
        array(
            'key' => '_novel_id',
            'value' => $novel_id,
            'compare' => '='
        ),
        'chapter_order' => array(
            'key' => '_is_illustration',
            'compare' => 'EXISTS',
        ),
    ),
    'orderby' => array(
        'chapter_order' => 'DESC',
        'meta_value_num' => 'ASC',
    ),
    'meta_key' => '_chapter_number'
));

// Chapters Section
?>
<div class="chapters-container">
    <div class="chapters-header">
        <div class="header-title">
            <i class="fas fa-list-ul"></i>
            <h5>قائمة الفصول</h5>
        </div>
        <?php if (is_user_logged_in() && current_user_can('edit_posts')): ?>
            <a href="<?php echo admin_url('post-new.php?post_type=chapter&novel_id=' . $novel_id); ?>"
               class="add-chapter-btn">
                <i class="fas fa-plus"></i>
                <span>إضافة فصل جديد</span>
            </a>
        <?php endif; ?>
    </div>

    <div class="chapters-content">
        <?php
        if (!empty($chapters)) {
            // تجميع الترجمات حسب رقم الفصل
            $chapters_by_number = array();
            foreach ($chapters as $chapter) {
                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                if (!isset($chapters_by_number[$chapter_number])) {
                    $chapters_by_number[$chapter_number] = array();
                }
                $chapters_by_number[$chapter_number][] = $chapter;
            }

            // ترتيب الفصول
            ksort($chapters_by_number);

            $volumes = array();
            foreach ($chapters_by_number as $chapter_number => $chapter_translations) {
                $first_chapter = $chapter_translations[0];
                $chapter_unique_id = get_post_meta($first_chapter->ID, '_chapter_unique_id', true);
                $volume_number = get_post_meta($first_chapter->ID, '_volume_number', true);
                if (empty($volume_number)) $volume_number = '1';

                if (!isset($volumes[$volume_number])) {
                    $volumes[$volume_number] = array();
                }

                // تجميع معلومات الترجمات
                $translations = array();
                foreach ($chapter_translations as $translation) {
                    $translations[] = array(
                        'id' => $translation->ID,
                        'unique_id' => get_post_meta($translation->ID, '_chapter_unique_id', true),
                        'title' => $translation->post_title,
                        'author' => get_the_author_meta('display_name', $translation->post_author),
                        'date' => get_the_date('Y-m-d', $translation->ID),
                        'link' => rtrim(home_url(), '/') . '/chapter?r=' . get_post_meta($translation->ID, '_chapter_unique_id', true)
                    );
                }

                $volumes[$volume_number][] = array(
                    'number' => $chapter_number,
                    'translations' => $translations
                );
            }

            // ترتيب المجلدات
            ksort($volumes);

            foreach ($volumes as $volume_number => $chapters) {
                ?>
                <div class="volume-block">
                    <div class="volume-header" data-bs-toggle="collapse"
                         data-bs-target="#volume-<?php echo $volume_number; ?>"
                         role="button" aria-expanded="true">
                        <div class="volume-title">
                            <i class="fas fa-book"></i>
                            <span>المجلد <?php echo $volume_number; ?></span>
                            <span class="chapter-count"><?php echo count($chapters); ?> فصل</span>
                        </div>
                        <div class="volume-toggle">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="volume-<?php echo $volume_number; ?>">
                        <div class="chapter-list">
                            <?php foreach ($chapters as $chapter): ?>
                                <?php
                                $translations = $chapter['translations'];
                                $first_translation = $translations[0];
                                ?>
                                <div class="chapter-item">
                                    <a href="#" class="chapter-link"
                                       data-bs-toggle="modal"
                                       data-bs-target="#translationModal-<?php echo $chapter['number']; ?>"
                                       data-chapter-number="<?php echo $chapter['number']; ?>">
                                        <div class="chapter-number">
                                            <?php
                                            if ($chapter['number'] === '0') {
                                                echo '<span class="intro-badge">المقدمة</span>';
                                            } else {
                                                echo 'الفصل ' . $chapter['number'];
                                            }
                                            ?>
                                        </div>
                                        <div class="chapter-info">
                                            <?php if (!empty($first_translation['title'])): ?>
                                                <div class="chapter-title"><?php echo $first_translation['title']; ?></div>
                                            <?php endif; ?>
                                            <div class="chapter-meta">
                                                <span class="translator-count">
                                                    <i class="fas fa-users"></i>
                                                    <span><?php echo count($translations); ?> مترجم</span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="chapter-action">
                                            <i class="fas fa-chevron-left"></i>
                                        </div>
                                    </a>
                                </div>

                                <!-- نافذة اختيار المترجم -->
                                <div class="modal fade translation-modal" id="translationModal-<?php echo $chapter['number']; ?>" tabindex="-1">
                                    <div class="modal-dialog modal-dialog-centered">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    <i class="fas fa-user-edit"></i>
                                                    اختر المترجم - الفصل <?php echo $chapter['number']; ?>
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="translation-options">
                                                    <?php foreach ($translations as $translation): ?>
                                                        <a href="<?php echo $translation['link']; ?>" class="translation-option">
                                                            <div class="translator-info">
                                                                <div class="translator-name">
                                                                    <i class="fas fa-user"></i>
                                                                    <span><?php echo $translation['author']; ?></span>
                                                                </div>
                                                                <div class="translation-date">
                                                                    <i class="far fa-calendar-alt"></i>
                                                                    <span><?php echo $translation['date']; ?></span>
                                                                </div>
                                                            </div>
                                                            <div class="translation-action">
                                                                <span>قراءة</span>
                                                                <i class="fas fa-chevron-left"></i>
                                                            </div>
                                                        </a>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php
            }
        } else {
            ?>
            <div class="no-chapters">
                <div class="no-chapters-icon">
                    <i class="fas fa-book-open"></i>
                </div>
                <div class="no-chapters-text">
                    <h5>لا توجد فصول بعد</h5>
                    <p>لم يتم إضافة أي فصول لهذه الرواية حتى الآن.</p>
                </div>
                <?php if (is_user_logged_in() && current_user_can('edit_posts')): ?>
                    <a href="<?php echo admin_url('post-new.php?post_type=chapter&novel_id=' . $novel_id); ?>"
                       class="add-first-chapter-btn">
                        <i class="fas fa-plus"></i>
                        <span>إضافة أول فصل</span>
                    </a>
                <?php endif; ?>
            </div>
            <?php
        }
        ?>
    </div>
</div>

<style>
/* ===== أنماط قائمة الفصول ===== */
.chapters-container {
    width: 100%;
}

.chapters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--novel-border);
}

.header-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-title i {
    color: var(--novel-primary);
    font-size: 1.1rem;
}

.header-title h5 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--novel-text);
}

.add-chapter-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background: var(--novel-primary);
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--novel-transition);
}

.add-chapter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--bs-primary-rgb), 0.2);
    color: white;
}

.add-chapter-btn i {
    margin-right: 0.5rem;
}

.volume-block {
    margin-bottom: 1.25rem;
    border: 1px solid var(--novel-border);
    border-radius: 0.5rem;
    overflow: hidden;
}

.volume-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    cursor: pointer;
    transition: var(--novel-transition);
}

.volume-header:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.volume-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.volume-title i {
    color: var(--novel-primary);
}

.volume-title span {
    font-weight: 600;
    color: var(--novel-text);
}

.chapter-count {
    font-size: 0.85rem;
    color: var(--novel-secondary);
    margin-right: 0.75rem;
    padding: 0.25rem 0.75rem;
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
    border-radius: 1rem;
}

.volume-toggle i {
    transition: transform 0.3s ease;
    color: var(--novel-secondary);
}

.volume-header[aria-expanded="false"] .volume-toggle i {
    transform: rotate(-90deg);
}

.chapter-list {
    padding: 0.75rem;
}

.chapter-item {
    margin-bottom: 0.5rem;
}

.chapter-item:last-child {
    margin-bottom: 0;
}

.chapter-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--novel-text);
    transition: var(--novel-transition);
    background-color: var(--novel-bg);
    border: 1px solid var(--novel-border);
}

.chapter-link:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem var(--novel-shadow);
    color: var(--novel-primary);
}

.chapter-number {
    flex: 0 0 auto;
    font-weight: 600;
    margin-left: 1rem;
    min-width: 80px;
}

.intro-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--novel-primary);
    color: white;
    border-radius: 1rem;
    font-size: 0.85rem;
}

.chapter-info {
    flex: 1;
}

.chapter-title {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.chapter-meta {
    font-size: 0.85rem;
    color: var(--novel-secondary);
}

.translator-count {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.chapter-action {
    flex: 0 0 auto;
    color: var(--novel-secondary);
    transition: var(--novel-transition);
}

.chapter-link:hover .chapter-action {
    color: var(--novel-primary);
}

/* ===== نافذة اختيار المترجم ===== */
.translation-modal .modal-content {
    border: none;
    border-radius: 0.75rem;
    overflow: hidden;
    background-color: var(--novel-bg);
}

.translation-modal .modal-header {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-bottom: 1px solid var(--novel-border);
    padding: 1rem 1.25rem;
}

.translation-modal .modal-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    color: var(--novel-text);
}

.translation-modal .modal-title i {
    color: var(--novel-primary);
}

.translation-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.translation-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    color: var(--novel-text);
    background-color: var(--novel-bg);
    border: 1px solid var(--novel-border);
    transition: var(--novel-transition);
}

.translation-option:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem var(--novel-shadow);
    color: var(--novel-text);
}

.translator-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.translator-name, .translation-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.translator-name i, .translation-date i {
    color: var(--novel-primary);
    font-size: 0.9rem;
    width: 16px;
    text-align: center;
}

.translation-date {
    font-size: 0.85rem;
    color: var(--novel-secondary);
}

.translation-action {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--novel-primary);
    font-weight: 500;
}

/* ===== حالة عدم وجود فصول ===== */
.no-chapters {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
}

.no-chapters-icon {
    font-size: 3rem;
    color: var(--novel-secondary);
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-chapters-text h5 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--novel-text);
}

.no-chapters-text p {
    color: var(--novel-secondary);
    margin-bottom: 1.5rem;
}

.add-first-chapter-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    background: var(--novel-gradient-primary);
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: var(--novel-transition);
    box-shadow: 0 0.25rem 1rem rgba(var(--bs-primary-rgb), 0.2);
}

.add-first-chapter-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(var(--bs-primary-rgb), 0.3);
    color: white;
}

.add-first-chapter-btn i {
    margin-right: 0.75rem;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 767.98px) {
    .chapters-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .add-chapter-btn {
        width: 100%;
        justify-content: center;
    }

    .volume-title {
        flex-wrap: wrap;
    }

    .chapter-number {
        min-width: 60px;
    }
}

@media (max-width: 575.98px) {
    .chapter-link {
        flex-wrap: wrap;
    }

    .chapter-number {
        width: 100%;
        margin-bottom: 0.5rem;
        margin-left: 0;
    }

    .chapter-info {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .chapter-action {
        width: 100%;
        display: flex;
        justify-content: flex-end;
    }
}
</style>
