<?php
/**
 * Template Name: صفحة المترجمين
 * Description: قالب لعرض قائمة المترجمين مع إحصائياتهم وأعمالهم
 */

// Get all users with translator or administrator role
$translators = get_users([
    'role__in' => ['translator', 'administrator'],
    'orderby' => 'post_count',
    'order' => 'DESC',
    'has_published_posts' => ['chapter'],
]);

// Prepare translators data
$translators_data = [];
$total_translators = 0;
$total_novels = 0;
$total_chapters = 0;

// Get novels count per author
$novels_count = [];
$novels_query = new WP_Query([
    'post_type' => 'novel',
    'posts_per_page' => -1,
    'fields' => 'ids',
]);

if ($novels_query->have_posts()) {
    foreach ($novels_query->posts as $novel_id) {
        $author_id = get_post_field('post_author', $novel_id);
        if (!isset($novels_count[$author_id])) {
            $novels_count[$author_id] = 0;
        }
        $novels_count[$author_id]++;
    }
    wp_reset_postdata();
}

// Get chapters count per author
$chapters_count = [];
$chapters_query = new WP_Query([
    'post_type' => 'chapter',
    'posts_per_page' => -1,
    'fields' => 'ids',
]);

if ($chapters_query->have_posts()) {
    foreach ($chapters_query->posts as $chapter_id) {
        $author_id = get_post_field('post_author', $chapter_id);
        if (!isset($chapters_count[$author_id])) {
            $chapters_count[$author_id] = 0;
        }
        $chapters_count[$author_id]++;
    }
    wp_reset_postdata();
}

// Process each translator
foreach ($translators as $translator) {
    $user_id = $translator->ID;
    $chapters = isset($chapters_count[$user_id]) ? $chapters_count[$user_id] : 0;
    $novels = isset($novels_count[$user_id]) ? $novels_count[$user_id] : 0;
    
    // Skip users with no chapters
    if ($chapters === 0) continue;
    
    $translators_data[] = [
        'id' => $user_id,
        'name' => $translator->display_name,
        'avatar' => get_avatar_url($user_id, ['size' => 200]),
        'role' => in_array('administrator', $translator->roles) ? 'admin' : 'translator',
        'chapters_count' => $chapters,
        'novels_count' => $novels,
        'join_date' => $translator->user_registered,
        'bio' => get_user_meta($user_id, 'description', true),
    ];
    
    // Update totals
    $total_translators++;
    $total_novels += $novels;
    $total_chapters += $chapters;
}

// Sort translators by chapters count (descending)
usort($translators_data, function($a, $b) {
    return $b['chapters_count'] - $a['chapters_count'];
});

get_header();
?>

<style>
:root {
    /* متغيرات الألوان الأساسية */
    --primary-color: var(--bs-primary, #4a6cf7);
    --primary-hover: #3a5ce4;
    --primary-color-rgb: 74, 108, 247;
    --secondary-color: var(--bs-secondary, #6c757d);
    --success-color: var(--bs-success, #28a745);
    --danger-color: var(--bs-danger, #dc3545);
    --warning-color: var(--bs-warning, #ffc107);
    --info-color: var(--bs-info, #17a2b8);
    --light-color: var(--bs-light, #f8f9fa);
    --dark-color: var(--bs-dark, #343a40);
    --white: var(--bs-white, #ffffff);
    --black: var(--bs-black, #000000);
    
    /* متغيرات التدرج الرمادي */
    --gray-100: var(--bs-gray-100, #f8f9fa);
    --gray-200: var(--bs-gray-200, #e9ecef);
    --gray-300: var(--bs-gray-300, #dee2e6);
    --gray-400: var(--bs-gray-400, #ced4da);
    --gray-500: var(--bs-gray-500, #adb5bd);
    --gray-600: var(--bs-gray-600, #6c757d);
    --gray-700: var(--bs-gray-700, #495057);
    --gray-800: var(--bs-gray-800, #343a40);
    --gray-900: var(--bs-gray-900, #212529);
    
    /* متغيرات التصميم */
    --body-bg: var(--bs-body-bg, #f8f9fa);
    --card-bg: var(--bs-card-bg, #ffffff);
    --text-color: var(--bs-body-color, #212529);
    --text-muted: var(--bs-secondary-color, #6c757d);
    --border-color: var(--bs-border-color, #dee2e6);
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
    --border-radius: var(--bs-border-radius, 0.5rem);
}

/* دعم الوضع الداكن */
[data-bs-theme="dark"] {
    --bs-body-bg: #121212;
    --bs-body-color: #e9ecef;
    --bs-card-bg: #1e1e1e;
    --bs-border-color: #2c2c2c;
    --bs-secondary-color: #adb5bd;
    --primary-color: #4a6cf7;
    --primary-hover: #3a5ce4;
    --primary-color-rgb: 74, 108, 247;
    
    /* تحديث المتغيرات المشتقة */
    --body-bg: var(--bs-body-bg);
    --card-bg: var(--bs-card-bg);
    --text-color: var(--bs-body-color);
    --text-muted: var(--bs-secondary-color);
    --border-color: var(--bs-border-color);
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    
    /* تحديث درجات الرمادي للوضع الداكن */
    --gray-100: #1e1e1e;
    --gray-200: #252525;
    --gray-300: #2c2c2c;
    --gray-400: #343434;
    --gray-500: #495057;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
}

/* الأنماط الأساسية */
body {
    background-color: var(--body-bg);
}

.translators-page {
    font-family: 'Tajawal', sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    padding: 40px 0;
    transition: var(--transition);
    background-color: var(--body-bg);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* رأس الصفحة */
.translators-header {
    text-align: center;
    margin-bottom: 40px;
}

.translators-header h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.translators-header h1::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    border-radius: 2px;
}

.translators-header p {
    color: var(--text-muted);
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto 30px;
}

/* بطاقات الإحصائيات */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 50%;
}

.stat-card h3 {
    font-size: 2.2rem;
    margin: 10px 0 5px;
    color: var(--primary-color);
}

.stat-card p {
    color: var(--text-muted);
    margin: 0;
    font-size: 1rem;
}

/* أدوات البحث والتصفية */
.translator-tools {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 15px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: 12px 20px 12px 45px;
    border: 1px solid var(--border-color);
    border-radius: 30px;
    font-size: 1rem;
    transition: var(--transition);
    background-color: var(--card-bg);
    color: var(--text-color);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
    outline: none;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    transition: var(--transition);
}

.search-box input:focus + i {
    color: var(--primary-color);
}

.filter-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 30px;
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.2);
    transform: translateY(-2px);
}

.filter-btn i {
    font-size: 0.9em;
}

/* شبكة المترجمين */
.translators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.translator-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.translator-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.translator-card.admin .translator-header {
    background: linear-gradient(135deg, var(--primary-color), #6a11cb);
}

.translator-card.admin .translator-avatar::after {
    content: '\f521';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--warning-color);
    color: var(--dark-color);
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    padding-left: 1px;
    padding-top: 1px;
}

.translator-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    padding: 80px 20px 30px;
    position: relative;
    text-align: center;
    color: white;
    overflow: hidden;
}

.translator-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: rgba(255, 255, 255, 0.2);
}

.translator-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid white;
    margin: 0 auto 15px;
    overflow: hidden;
    position: relative;
    background: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.translator-card:hover .translator-avatar {
    transform: scale(1.05);
}

.translator-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.translator-name {
    font-size: 1.4rem;
    margin: 0 0 5px;
    font-weight: 700;
    color: white;
}

.translator-role {
    font-size: 0.85rem;
    opacity: 0.9;
    margin: 0;
    color: rgba(255, 255, 255, 0.9);
}

.translator-body {
    padding: 25px 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.translator-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.stat-item h4 {
    font-size: 1.5rem;
    margin: 0 0 5px;
    color: var(--primary-color);
    font-weight: 700;
}

.stat-item p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-muted);
}

.translator-meta {
    margin-top: auto;
    padding-top: 15px;
    font-size: 0.9rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-top: 1px solid var(--border-color);
}

.translator-meta i {
    color: var(--primary-color);
}

.translator-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 10px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 18px;
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
    cursor: pointer;
    gap: 5px;
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border: 1px solid transparent;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: rgba(var(--primary-color-rgb), 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* حالة عدم وجود نتائج */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 80px 30px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    margin: 20px 0;
    transition: all 0.3s ease;
}

.empty-state i {
    font-size: 4.5rem;
    color: var(--gray-400);
    margin-bottom: 25px;
    opacity: 0.8;
    display: inline-block;
    transition: all 0.3s ease;
}

.empty-state h3 {
    color: var(--text-color);
    margin-bottom: 15px;
    font-size: 1.8rem;
    font-weight: 700;
}

.empty-state p {
    color: var(--text-muted);
    max-width: 600px;
    margin: 0 auto 30px;
    line-height: 1.7;
    font-size: 1.05rem;
}

/* حالة التحميل */
.loading {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin: 20px 0;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.spinner {
    width: 60px;
    height: 60px;
    margin: 0 auto 25px;
    border: 4px solid rgba(var(--primary-color-rgb), 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    border-right-color: var(--primary-color);
    animation: spin 0.8s linear infinite;
    position: relative;
}

.spinner::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 4px solid transparent;
    border-radius: 50%;
    border-top-color: rgba(var(--primary-color-rgb), 0.3);
    animation: spin 1.2s linear infinite reverse;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    color: var(--text-muted);
    margin-top: 20px;
    font-size: 1.1rem;
    font-weight: 500;
}

/* تحسينات إضافية للوضع الداكن */
[data-bs-theme="dark"] {
    /* تحسينات الخلفية الرئيسية */
    body {
        background-color: var(--bs-body-bg) !important;
        color: var(--bs-body-color) !important;
    }
    
    .translators-page {
        background-color: var(--bs-body-bg) !important;
    }
    
    /* تحسينات بطاقات الإحصائيات */
    .stat-card {
        background: var(--card-bg) !important;
        border-color: var(--gray-700) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .stat-card i {
        background: rgba(var(--primary-color-rgb), 0.15) !important;
        color: var(--primary-color) !important;
    }
    
    .stat-card h3 {
        color: var(--primary-color) !important;
    }
    
    .stat-card p {
        color: var(--bs-secondary-color) !important;
    }
    
    /* تحسينات الأزرار */
    .btn-outline {
        border-color: var(--primary-color) !important;
        color: var(--primary-color) !important;
    }
    
    .btn-outline:hover {
        background: rgba(var(--primary-color-rgb), 0.1) !important;
    }
    
    .btn-primary {
        background: var(--primary-color) !important;
        color: white !important;
    }
    
    .btn-primary:hover {
        background: var(--primary-hover) !important;
    }
    
    /* تحسينات بطاقات المترجمين */
    .translator-card {
        background: var(--card-bg) !important;
        border-color: var(--gray-700) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .translator-card .translator-body {
        background: var(--card-bg) !important;
        color: var(--bs-body-color) !important;
    }
    
    .translator-stats {
        border-bottom-color: var(--gray-700) !important;
    }
    
    .translator-meta {
        border-top-color: var(--gray-700) !important;
        color: var(--bs-secondary-color) !important;
    }
    
    .stat-item h4 {
        color: var(--primary-color) !important;
    }
    
    .stat-item p {
        color: var(--bs-secondary-color) !important;
    }
    
    /* تحسينات أدوات البحث والتصفية */
    .translator-tools {
        background: var(--card-bg) !important;
        border-color: var(--gray-700) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .search-box input {
        background: var(--bs-body-bg) !important;
        border-color: var(--gray-700) !important;
        color: var(--bs-body-color) !important;
    }
    
    .search-box i {
        color: var(--bs-secondary-color) !important;
    }
    
    .filter-btn {
        background: var(--bs-body-bg) !important;
        border-color: var(--gray-700) !important;
        color: var(--bs-body-color) !important;
    }
    
    .filter-btn:hover {
        background: var(--gray-700) !important;
        color: white !important;
    }
    
    .filter-btn.active {
        background: var(--primary-color) !important;
        color: white !important;
        border-color: var(--primary-color) !important;
    }
    
    /* تحسينات حالات أخرى */
    .empty-state {
        background: var(--card-bg) !important;
        border-color: var(--gray-700) !important;
    }
    
    .empty-state i {
        color: var(--bs-secondary-color) !important;
    }
    
    .empty-state h3 {
        color: var(--bs-body-color) !important;
    }
    
    .empty-state p {
        color: var(--bs-secondary-color) !important;
    }
    
    .loading {
        background: var(--card-bg) !important;
        border-color: var(--gray-700) !important;
    }
    
    /* تحسينات رؤوس البطاقات */
    .translator-header {
        background: linear-gradient(135deg, var(--primary-color), #3a5ce4) !important;
    }
    
    .translator-card.admin .translator-header {
        background: linear-gradient(135deg, var(--primary-color), #4a00c7) !important;
    }
    
    .translator-name, .translator-role {
        color: white !important;
    }
    
    /* تحسينات لعنوان الصفحة */
    .translators-header h1 {
        color: var(--primary-color) !important;
    }
    
    .translators-header p {
        color: var(--bs-secondary-color) !important;
    }
}

/* تعديلات للأجهزة المحمولة */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: 1fr;
    }
    
    .translator-tools {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        max-width: 100%;
    }
    
    .filter-group {
        justify-content: center;
    }
    
    .translators-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="translators-page">
    <div class="container">
        <!-- رأس الصفحة -->
        <header class="translators-header">
            <h1>فريق الترجمة</h1>
            <p>تعرف على فريق المترجمين المتميزين الذين يعملون بجد لتوفير أفضل الروايات المترجمة</p>
        </header>
        
        <!-- بطاقات الإحصائيات -->
        <div class="stats-container">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3><?php echo number_format($total_translators); ?></h3>
                <p>عدد المترجمين</p>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-book"></i>
                <h3><?php echo number_format($total_novels); ?></h3>
                <p>إجمالي الروايات</p>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-file-alt"></i>
                <h3><?php echo number_format($total_chapters); ?></h3>
                <p>إجمالي الفصول المترجمة</p>
            </div>
        </div>
        
        <!-- البحث والتصفية -->
        <div class="translator-tools">
            <div class="search-box">
                <input type="text" id="translatorSearch" placeholder="ابحث عن مترجم...">
                <i class="fas fa-search"></i>
            </div>
            
            <div class="filter-group">
                <button class="filter-btn active" data-filter="all">
                    <i class="fas fa-list"></i> الكل
                </button>
                <button class="filter-btn" data-filter="admin">
                    <i class="fas fa-crown"></i> الإدارة
                </button>
                <button class="filter-btn" data-filter="translator">
                    <i class="fas fa-language"></i> المترجمين
                </button>
                <button class="filter-btn" data-sort="chapters">
                    <i class="fas fa-sort-amount-down"></i> الأكثر إنتاجية
                </button>
            </div>
        </div>
        
        <!-- شبكة المترجمين -->
        <div class="translators-grid" id="translatorsGrid">
            <?php if (!empty($translators_data)) : ?>
                <?php foreach ($translators_data as $translator) : ?>
                    <div class="translator-card <?php echo $translator['role'] === 'admin' ? 'admin' : ''; ?>" 
                         data-role="<?php echo $translator['role']; ?>"
                         data-chapters="<?php echo $translator['chapters_count']; ?>"
                         data-name="<?php echo esc_attr($translator['name']); ?>">
                        
                        <div class="translator-header">
                            <div class="translator-avatar">
                                <img src="<?php echo esc_url($translator['avatar']); ?>" alt="<?php echo esc_attr($translator['name']); ?>">
                            </div>
                            <h3 class="translator-name"><?php echo esc_html($translator['name']); ?></h3>
                            <p class="translator-role">
                                <?php echo $translator['role'] === 'admin' ? 'مدير' : 'مترجم'; ?>
                            </p>
                        </div>
                        
                        <div class="translator-body">
                            <div class="translator-stats">
                                <div class="stat-item">
                                    <h4><?php echo number_format($translator['novels_count']); ?></h4>
                                    <p>روايات</p>
                                </div>
                                <div class="stat-item">
                                    <h4><?php echo number_format($translator['chapters_count']); ?></h4>
                                    <p>فصول</p>
                                </div>
                            </div>
                            
                            <?php if (!empty($translator['bio'])) : ?>
                                <div class="translator-bio">
                                    <p><?php echo wp_trim_words($translator['bio'], 15, '...'); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <div class="translator-meta">
                                <i class="far fa-calendar-alt"></i>
                                <span>منذ <?php echo human_time_diff(strtotime($translator['join_date']), current_time('timestamp')); ?></span>
                            </div>
                            
                            <div class="translator-actions">
                                <a href="<?php echo esc_url(get_author_posts_url($translator['id'])); ?>" class="btn btn-primary">
                                    <i class="far fa-user"></i> الملف الشخصي
                                </a>
                                <a href="<?php echo esc_url(home_url('/author/' . urlencode($translator['name']) . '/?post_type=chapter')); ?>" class="btn btn-outline">
                                    <i class="far fa-list-alt"></i> الفصول
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else : ?>
                <div class="empty-state">
                    <i class="fas fa-user-slash"></i>
                    <h3>لا يوجد مترجمون حتى الآن</h3>
                    <p>لم يتم العثور على أي مترجمين نشطين. يرجى التحقق لاحقًا.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- إضافة Font Awesome للأيقونات -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('translatorSearch');
    const filterButtons = document.querySelectorAll('.filter-btn[data-filter]');
    const sortButtons = document.querySelectorAll('.filter-btn[data-sort]');
    const translatorCards = document.querySelectorAll('.translator-card');
    
    // وظيفة البحث
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            translatorCards.forEach(card => {
                const name = card.getAttribute('data-name').toLowerCase();
                if (name.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }
    
    // وظيفة التصفية
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // تحديث حالة الزر النشط
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // تطبيق التصفية
            translatorCards.forEach(card => {
                const role = card.getAttribute('data-role');
                if (filter === 'all' || role === filter) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
    
    // وظيفة الترتيب
    sortButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sortBy = this.getAttribute('data-sort');
            const container = document.getElementById('translatorsGrid');
            const cards = Array.from(container.querySelectorAll('.translator-card'));
            
            // تحديث حالة الزر النشط
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // ترتيب البطاقات
            cards.sort((a, b) => {
                if (sortBy === 'chapters') {
                    return parseInt(b.getAttribute('data-chapters')) - parseInt(a.getAttribute('data-chapters'));
                } else {
                    return a.getAttribute('data-name').localeCompare(b.getAttribute('data-name'));
                }
            });
            
            // إعادة ترتيب البطاقات في الصفحة
            cards.forEach(card => container.appendChild(card));
        });
    });
    
    // إضافة مؤثرات حركية للبطاقات
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
});
</script>

<?php get_footer(); ?>