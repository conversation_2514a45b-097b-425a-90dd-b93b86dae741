<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package Sekaiplus
 */

// Enqueue 404 page specific styles and scripts
function sekaiplus_404_scripts() {
    if (is_404()) {
        wp_enqueue_style('sekaiplus-404-style', get_template_directory_uri() . '/css/404-page.css');
        wp_enqueue_script('sekaiplus-404-script', get_template_directory_uri() . '/js/404-page.js', array('jquery'), '', true);
    }
}
add_action('wp_enqueue_scripts', 'sekaiplus_404_scripts');

get_header();
?>

<div class="error-page-container">
    <div class="error-content">
        <div class="error-animation">
            <div class="error-book">
                <div class="book-page"></div>
                <div class="book-page"></div>
                <div class="book-page"></div>
            </div>
            <div class="error-code">404</div>
        </div>

        <h1 class="error-title">عفواً، الصفحة غير موجودة</h1>
        <p class="error-message">يبدو أن الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.</p>

        <div class="error-actions">
            <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-home"></i> الصفحة الرئيسية
            </a>
            <button type="button" class="btn btn-outline-secondary btn-lg" id="searchToggle">
                <i class="fas fa-search"></i> البحث
            </button>
        </div>

        <div class="error-search mt-4" style="display: none;">
            <form role="search" method="get" class="search-form" action="<?php echo esc_url(home_url('/')); ?>">
                <div class="input-group">
                    <input type="search" class="form-control" placeholder="ابحث عن روايات، فصول..." value="<?php echo get_search_query(); ?>" name="s" />
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <div class="error-suggestions mt-5">
            <h3>قد تهمك أيضاً</h3>
            <div class="row mt-3">
                <?php
                // عرض أحدث الروايات
                $recent_novels = get_posts(array(
                    'post_type' => 'novel',
                    'posts_per_page' => 3,
                    'orderby' => 'date',
                    'order' => 'DESC'
                ));

                if ($recent_novels) {
                    foreach ($recent_novels as $novel) {
                        $novel_cover = get_the_post_thumbnail_url($novel->ID, 'thumbnail');
                        if (!$novel_cover) {
                            $novel_cover = get_template_directory_uri() . '/img/default-cover.jpg';
                        }
                        ?>
                        <div class="col-md-4 mb-3">
                            <div class="suggestion-card">
                                <a href="<?php echo get_permalink($novel->ID); ?>" class="suggestion-link">
                                    <div class="suggestion-image" style="background-image: url('<?php echo esc_url($novel_cover); ?>')"></div>
                                    <div class="suggestion-title"><?php echo get_the_title($novel->ID); ?></div>
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
