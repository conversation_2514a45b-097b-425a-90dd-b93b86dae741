<?php
/**
 * The template for displaying comments
 * قالب عرض التعليقات بتصميم عصري وذكي
 * @package Sekaiplus
 */

if (post_password_required()) {
    return;
}

// Añadimos el hook de WordPress para habilitar la moderación de comentarios
if (!get_option('comment_moderation')) {
    add_option('comment_moderation', 1);
}
?>

<div id="comments" class="comments-area modern-comments">
    <?php if (have_comments() || comments_open()): ?>
        <div class="comments-header">
            <h3 class="comments-title">
                <?php
                $comment_count = get_comments_number();
                if ($comment_count > 0) {
                    printf(
                        _nx(
                            '%1$s تعليق',
                            '%1$s تعليقات',
                            $comment_count,
                            'comments title',
                            'sekaiplus'
                        ),
                        number_format_i18n($comment_count)
                    );
                } else {
                    echo 'التعليقات';
                }
                ?>
            </h3>
            
            <div class="comments-sort">
                <select class="comment-sort-select form-select">
                    <option value="newest"><?php _e('الأحدث', 'sekaiplus'); ?></option>
                    <option value="oldest"><?php _e('الأقدم', 'sekaiplus'); ?></option>
                    <option value="popular"><?php _e('الأكثر تفاعلاً', 'sekaiplus'); ?></option>
                </select>
            </div>
        </div>

        <div class="comments-container">
            <?php if (comments_open()): ?>
                <div class="comment-form-wrapper">
                    <?php
                    // التحقق من تسجيل الدخول
                    if (is_user_logged_in()) {
                        // نموذج التعليق للمسجلين
                        ?>
                        <div class="comment-form-card">
                            <div class="comment-form-notice info">
                                <i class="fas fa-info-circle"></i>
                                <span><?php _e('سيتم مراجعة تعليقك قبل نشره', 'sekaiplus'); ?></span>
                            </div>
                            <form id="commentform" class="comment-form modern-comment-form" method="post" action="<?php echo site_url('/wp-comments-post.php'); ?>">
                                <div class="comment-form-header">
                                    <div class="comment-user-info">
                                        <div class="comment-user-avatar">
                                            <?php echo get_avatar(get_current_user_id(), 48); ?>
                                        </div>
                                        <div class="comment-user-name">
                                            <?php 
                                            $user = wp_get_current_user();
                                            echo !empty($user->display_name) ? $user->display_name : 'u/' . $user->user_login; 
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="comment-form-body">
                                    <div class="comment-textarea-wrapper">
                                        <textarea id="comment" name="comment" class="form-control comment-textarea" placeholder="<?php _e('اكتب تعليقك هنا...', 'sekaiplus'); ?>" required></textarea>
                                    </div>
                                </div>
                                
                                <div class="comment-form-footer">
                                    <div class="comment-policy">
                                        <small><?php _e('يجب الالتزام بقواعد النشر وعدم استخدام كلمات نابية أو مسيئة', 'sekaiplus'); ?></small>
                                    </div>
                                    <div class="comment-form-actions">
                                        <button type="submit" id="submit" name="submit" class="btn-comment-submit">
                                            <span class="submit-icon"><i class="fas fa-paper-plane"></i></span>
                                            <span class="submit-text"><?php _e('إرسال', 'sekaiplus'); ?></span>
                                        </button>
                                    </div>
                                </div>
                                
                                <?php comment_id_fields(); ?>
                                <?php do_action('comment_form', get_the_ID()); ?>
                            </form>
                        </div>
                        <?php
                    } else {
                        // نموذج التعليق للزوار غير المسجلين
                        ?>
                        <div class="guest-comment-card">
                            <div class="guest-comment-container">
                                <div class="guest-comment-info">
                                    <div class="guest-avatar">
                                        <div class="guest-icon"><i class="fas fa-user-lock"></i></div>
                                    </div>
                                    <div class="guest-comment-message">
                                        <div class="guest-comment-title"><?php _e('التعليقات متاحة للمستخدمين المسجلين فقط', 'sekaiplus'); ?></div>
                                        <div class="guest-comment-placeholder"><?php _e('سجل دخولك للتعليق والتفاعل مع المحتوى', 'sekaiplus'); ?></div>
                                    </div>
                                </div>
                                <div class="guest-login-button-wrapper">
                                    <a href="<?php echo wp_login_url(apply_filters('the_permalink', get_permalink())); ?>" class="btn-login-to-comment">
                                        <span class="login-icon"><i class="fas fa-sign-in-alt"></i></span>
                                        <span class="login-text"><?php _e('تسجيل الدخول', 'sekaiplus'); ?></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                    ?>
                </div>
            <?php endif; ?>

            <?php if (have_comments()): ?>
                <div class="comments-list-container">
                    <div class="comment-list">
                        <?php
                        wp_list_comments(array(
                            'style'      => 'div',
                            'short_ping' => true,
                            'callback'   => 'sekaiplus_comment_callback',
                            'avatar_size'=> 48,
                            'reply_text' => '',  // منع ظهور نص الرد الافتراضي
                            'max_depth'  => 3,   // الحد الأقصى لعمق التعليقات المتداخلة
                        ));
                        ?>
                    </div>

                    <?php if (get_comment_pages_count() > 1 && get_option('page_comments')) : ?>
                        <nav class="comment-navigation" role="navigation">
                            <div class="comment-nav-links">
                                <?php
                                $prev_link = get_previous_comments_link(__('← التعليقات السابقة', 'sekaiplus'));
                                $next_link = get_next_comments_link(__('التعليقات التالية →', 'sekaiplus'));
                                
                                if ($prev_link) echo '<div class="nav-previous">' . $prev_link . '</div>';
                                if ($next_link) echo '<div class="nav-next">' . $next_link . '</div>';
                                ?>
                            </div>
                        </nav>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style>
/* تصميم عصري للتعليقات - نسخة محسنة */
.modern-comments {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    padding: 25px;
    margin: 35px 0;
    transition: all 0.3s ease;
}

/* رأس قسم التعليقات */
.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.comments-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: #333;
    position: relative;
    padding-right: 15px;
}

.comments-title:before {
    content: '';
    position: absolute;
    right: 0;
    top: 5px;
    height: 70%;
    width: 4px;
    background-color: #4d90fe;
    border-radius: 2px;
}

.comments-sort {
    position: relative;
}

.comment-sort-select {
    padding: 8px 14px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #f8f9fa;
    font-size: 0.85rem;
    color: #555;
    cursor: pointer;
    transition: all 0.25s ease;
}

.comment-sort-select:hover {
    border-color: rgba(0, 0, 0, 0.25);
    background-color: #f1f3f5;
}

.comment-sort-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(77, 144, 254, 0.25);
}

/* حاوية التعليقات الرئيسية */
.comments-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

/* نموذج التعليق */
.comment-form-wrapper {
    margin-bottom: 30px;
}

.comment-form-card {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.comment-form-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

/* إشعار المراجعة */
.comment-form-notice {
    padding: 12px 16px;
    background-color: #e8f4fd;
    color: #0a58ca;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.comment-form-notice.info i {
    color: #0a58ca;
}

.comment-form-notice.success {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.comment-form-notice.success i {
    color: #2e7d32;
}

.comment-form-header {
    padding: 14px 18px;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.comment-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.comment-user-avatar img {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 2px solid #fff;
}

.comment-user-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: #333;
}

.comment-form-body {
    padding: 18px;
}

.comment-textarea-wrapper {
    position: relative;
}

.comment-textarea {
    width: 100%;
    min-height: 125px;
    padding: 14px 18px;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.12);
    background-color: #fff;
    font-size: 0.95rem;
    color: #333;
    resize: vertical;
    transition: all 0.25s ease;
}

.comment-textarea:focus {
    outline: none;
    border-color: #4d90fe;
    box-shadow: 0 0 0 3px rgba(77, 144, 254, 0.25);
}

.comment-form-footer {
    padding: 14px 18px;
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.comment-policy {
    font-size: 0.8rem;
    color: #777;
    max-width: 70%;
}

.comment-form-actions {
    display: flex;
    justify-content: flex-end;
}

.btn-comment-submit {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    background-color: #4d90fe;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.25s ease;
}

.btn-comment-submit:hover {
    background-color: #357ae8;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.btn-comment-submit:active {
    transform: translateY(0);
}

.submit-icon {
    font-size: 0.9rem;
}

/* نموذج تسجيل الدخول للزوار */
.guest-comment-card {
    background-color: #f8f9fa;
    border-radius: 12px;
    padding: 18px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.guest-comment-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.guest-comment-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.guest-avatar {
    display: flex;
    justify-content: center;
    align-items: center;
}

.guest-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: #e9ecef;
    border-radius: 50%;
    color: #4d90fe;
    font-size: 1.1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 2px solid #fff;
}

.guest-comment-message {
    flex: 1;
}

.guest-comment-title {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
    margin-bottom: 5px;
}

.guest-comment-placeholder {
    font-size: 0.9rem;
    color: #666;
}

.guest-login-button-wrapper {
    display: flex;
    justify-content: flex-end;
}

.btn-login-to-comment {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 18px;
    background-color: #4d90fe;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.25s ease;
}

.btn-login-to-comment:hover {
    background-color: #357ae8;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: white;
    transform: translateY(-1px);
}

.btn-login-to-comment:active {
    transform: translateY(0);
}

/* قائمة التعليقات */
.comments-list-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.comment-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.comment {
    background-color: #fff;
    border-radius: 10px;
    padding: 18px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.25s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.comment:hover {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

/* حالة التعليق تحت المراجعة */
.comment.comment-awaiting-moderation {
    border-right: 3px solid #f4d03f;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.comment-author img {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    border: 2px solid #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.comment-author .fn {
    font-weight: 600;
    font-size: 0.95rem;
    color: #333;
}

.comment-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.8rem;
    color: #777;
    margin-right: 15px;
}

.comment-content {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #333;
    margin: 15px 0;
}

.comment-content p:last-child {
    margin-bottom: 0;
}

.comment-actions {
    display: flex;
    gap: 20px;
    font-size: 0.85rem;
    margin-top: 15px;
}

.comment-reply-link,
.comment-like-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    text-decoration: none;
    transition: all 0.25s ease;
    cursor: pointer;
    padding: 5px;
}

.comment-reply-link:hover,
.comment-like-btn:hover {
    color: #4d90fe;
    text-decoration: none;
}

.comment-like-btn.active {
    color: #4d90fe;
}

.comment-like-count {
    font-weight: 500;
}

/* تنقل التعليقات */
.comment-navigation {
    display: flex;
    justify-content: space-between;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.comment-nav-links {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.nav-previous,
.nav-next {
    font-size: 0.9rem;
}

.nav-previous a,
.nav-next a {
    color: #4d90fe;
    text-decoration: none;
    transition: all 0.25s ease;
}

.nav-previous a:hover,
.nav-next a:hover {
    color: #357ae8;
    text-decoration: underline;
}

/* تعليق قيد المراجعة */
.comment-pending {
    background-color: #fffdf4;
    border-radius: 8px;
    border: 1px solid #f4e7be;
    padding: 12px 15px;
    margin: 12px 0;
    font-size: 0.9rem;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-pending i {
    color: #f4d03f;
}

/* تنبيه نجاح التعليق */
.comment-success-message {
    background-color: #e8f5e9;
    border-radius: 8px;
    border: 1px solid #c8e6c9;
    padding: 12px 15px;
    margin: 12px 0;
    font-size: 0.9rem;
    color: #2e7d32;
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-success-message i {
    color: #4caf50;
}

/* تنبيه خطأ التعليق */
.comment-error-message {
    background-color: #ffebee;
    border-radius: 8px;
    border: 1px solid #ffcdd2;
    padding: 12px 15px;
    margin: 12px 0;
    font-size: 0.9rem;
    color: #c62828;
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-error-message i {
    color: #f44336;
}

/* الوضع الداكن */
.dark-mode .modern-comments {
    background-color: #222;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
}

.dark-mode .comments-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .comments-title {
    color: #f0f0f0;
}

.dark-mode .comments-title:before {
    background-color: #4d90fe;
}

.dark-mode .comment-sort-select {
    background-color: #333;
    border-color: #444;
    color: #ddd;
}

.dark-mode .comment-sort-select:hover {
    border-color: #555;
    background-color: #3a3a3a;
}

.dark-mode .comment-form-card {
    background-color: #2a2a2a;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.2);
    border-color: #333;
}

.dark-mode .comment-form-notice {
    background-color: #27374d;
    color: #9ec5fe;
    border-color: #333;
}

.dark-mode .comment-form-notice.info i {
    color: #9ec5fe;
}

.dark-mode .comment-form-notice.success {
    background-color: #1b3a25;
    color: #75c288;
}

.dark-mode .comment-form-notice.success i {
    color: #75c288;
}

.dark-mode .comment-form-header,
.dark-mode .comment-form-footer {
    background-color: #333;
    border-color: rgba(255, 255, 255, 0.05);
}

.dark-mode .comment-user-name {
    color: #f0f0f0;
}

.dark-mode .comment-user-avatar img,
.dark-mode .comment-author img,
.dark-mode .guest-icon {
    border-color: #444;
}

.dark-mode .comment-textarea {
    background-color: #333;
    border-color: #444;
    color: #f0f0f0;
}

.dark-mode .comment-textarea:focus {
    border-color: #4d90fe;
    box-shadow: 0 0 0 3px rgba(77, 144, 254, 0.25);
}

.dark-mode .comment-policy {
    color: #aaa;
}

.dark-mode .btn-comment-submit {
    background-color: #4d90fe;
    color: white;
}

.dark-mode .btn-comment-submit:hover {
    background-color: #357ae8;
}

.dark-mode .guest-comment-card {
    background-color: #2a2a2a;
    border-color: #333;
}

.dark-mode .guest-icon {
    background-color: #444;
    color: #4d90fe;
}

.dark-mode .guest-comment-title {
    color: #f0f0f0;
}

.dark-mode .guest-comment-placeholder {
    color: #ccc;
}

.dark-mode .btn-login-to-comment {
    background-color: #4d90fe;
    color: white;
}

.dark-mode .btn-login-to-comment:hover {
    background-color: #357ae8;
}

.dark-mode .comment {
    background-color: #2a2a2a;
    border-color: #333;
}

.dark-mode .comment-author .fn {
    color: #f0f0f0;
}

.dark-mode .comment-meta {
    color: #aaa;
}

.dark-mode .comment-content {
    color: #e0e0e0;
}

.dark-mode .comment-actions a {
    color: #aaa;
}

.dark-mode .comment-actions a:hover {
    color: #4d90fe;
}

.dark-mode .comment-navigation {
    border-top-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .comment-navigation a {
    color: #4d90fe;
}

.dark-mode .comment-navigation a:hover {
    color: #357ae8;
}

.dark-mode .comment-pending {
    background-color: #3a3a00;
    border-color: #5e5318;
    color: #f4d03f;
}

.dark-mode .comment-success-message {
    background-color: #1b3a25;
    border-color: #2e5a3e;
    color: #75c288;
}

.dark-mode .comment-error-message {
    background-color: #3b1e1e;
    border-color: #582828;
    color: #ef9a9a;
}

/* تخصيصات للشاشات الصغيرة */
@media (max-width: 767px) {
    .modern-comments {
        padding: 20px;
        border-radius: 10px;
    }
    
    .comments-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .comments-sort {
        width: 100%;
    }
    
    .comment-sort-select {
        width: 100%;
    }
    
    .comment-form-footer {
        flex-direction: column;
        gap: 15px;
    }
    
    .comment-policy {
        max-width: 100%;
        order: 2;
    }
    
    .comment-form-actions {
        width: 100%;
        order: 1;
    }
    
    .btn-comment-submit {
        width: 100%;
        justify-content: center;
    }
    
    .guest-comment-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .guest-login-button-wrapper {
        justify-content: center;
    }
    
    .btn-login-to-comment {
        width: 100%;
        justify-content: center;
    }
    
    .comment-actions {
        flex-wrap: wrap;
        gap: 15px;
    }
}
</style>

<!-- AJAX Comment Script -->
<script type="text/javascript">
jQuery(document).ready(function($) {
    // متغير يخزن حالة نموذج التعليق (عادي أم في وضع الرد)
    var isReplyActive = false;
    var originalFormPosition = null;

    // Handle comment submission via AJAX
    $('#commentform').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = form.find('#submit');
        var submitText = submitBtn.find('.submit-text');
        var originalText = submitText.text();
        
        // Change button state to loading
        submitBtn.prop('disabled', true);
        submitText.text('جاري الإرسال...');
        
        // Get form data
        var formData = form.serialize();
        
        // Send AJAX request
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: formData,
            success: function(response) {
                // Always show success message
                handleSuccessfulComment();
            },
            error: function(xhr, status, error) {
                // في معظم الحالات، يكون الخطأ بسبب استجابة ووردبريس التي ترسل صفحة HTML كاملة
                // لذلك نعتبرها نجاحًا إذا وصل حجمها إلى أكثر من حد معين
                if (xhr.responseText && xhr.responseText.length > 200) {
                    // من المحتمل أن التعليق تم إرساله بنجاح ولكن ووردبريس استجاب بصفحة HTML كاملة
                    handleSuccessfulComment();
                } else {
                    // خطأ حقيقي
                    var errorDiv = $('<div class="comment-error-message"><i class="fas fa-exclamation-circle"></i>حدث خطأ أثناء إرسال التعليق. الرجاء المحاولة مرة أخرى.</div>');
                    form.prepend(errorDiv);
                    
                    setTimeout(function() {
                        errorDiv.fadeOut(function() {
                            $(this).remove();
                        });
                    }, 4000);
                }
                
                // إعادة تفعيل الزر في كل الأحوال
                submitBtn.prop('disabled', false);
                submitText.text(originalText);
            }
        });
        
        // دالة لمعالجة التعليق الناجح
        function handleSuccessfulComment() {
            // إعادة تعيين النموذج
            form.find('#comment').val('');
            if (form.find('#comment_parent').val() !== '0') {
                // إذا كانت ردًا، إعادة تعيين معرف الأب
                form.find('#comment_parent').val('0');
                // نقل النموذج إلى موضعه الأصلي
                $('.comment-form-wrapper').append(form.parent());
            }
            
            // عرض رسالة النجاح مع معلومات المراجعة
            var messageDiv = $('<div class="comment-success-message"><i class="fas fa-check-circle"></i>تم إرسال التعليق بنجاح! سيظهر بعد المراجعة.</div>');
            form.prepend(messageDiv);
            
            // إزالة الرسالة بعد 4 ثوانٍ
            setTimeout(function() {
                messageDiv.fadeOut(function() {
                    $(this).remove();
                });
            }, 4000);
            
            // إعادة تعيين الزر
            submitBtn.prop('disabled', false);
            submitText.text(originalText);
        }
    });
    
    // Handle reply button
    $(document).on('click', '.comment-reply-btn', function(e) {
        e.preventDefault();
        
        var commentId = $(this).data('comment-id');
        var commentAuthor = $(this).closest('.comment-author').find('.fn').text().trim();
        var commentItem = $('#comment-' + commentId);
        var replyForm = $('#commentform').parent();
        
        // تخزين حالة الرد
        isReplyActive = true;
        
        // حفظ الموقع الأصلي للنموذج إذا لم يكن محفوظًا بالفعل
        if (!originalFormPosition) {
            originalFormPosition = $('.comment-form-wrapper').parent();
        }
        
        // Set the parent comment ID
        $('#comment_parent').val(commentId);
        
        // Move the comment form
        commentItem.append(replyForm);
        
        // Focus on the textarea
        $('#comment').focus();
        
        // Update placeholder text
        $('#comment').attr('placeholder', 'رد على تعليق ' + commentAuthor);
    });
    
    // إضافة مستمع لإلغاء الرد عند النقر خارج نموذج التعليق
    $(document).on('click', function(e) {
        if (isReplyActive && originalFormPosition) {
            var $commentForm = $('#commentform');
            var $replyButtons = $('.comment-reply-btn');
            
            // التحقق مما إذا كان النقر خارج النموذج وليس على زر الرد
            if (!$commentForm.is(e.target) && 
                $commentForm.has(e.target).length === 0 && 
                !$replyButtons.is(e.target) &&
                !$replyButtons.has(e.target).length) {
                
                // التحقق من أن النموذج غير فارغ (لتجنب فقدان التعليق المكتوب)
                var commentText = $('#comment').val().trim();
                
                // إذا كان النموذج فارغًا أو المستخدم يؤكد أنه يريد إلغاء الرد
                if (commentText === '') {
                    // إعادة تعيين النموذج
                    resetCommentForm();
                }
            }
        }
    });
    
    // وظيفة لإعادة تعيين نموذج التعليق إلى الحالة العادية
    function resetCommentForm() {
        // إعادة تعيين معرف الأب
        $('#comment_parent').val('0');
        
        // إعادة نموذج التعليق إلى موضعه الأصلي
        $('.comment-form-wrapper').append($('#commentform').parent());
        
        // إعادة تعيين نص التعبئة
        $('#comment').attr('placeholder', 'اكتب تعليقك هنا...');
        
        // إعادة تعيين النص
        $('#comment').val('');
        
        // إعادة تعيين حالة الرد
        isReplyActive = false;
    }
    
    // Handle show/hide replies
    $(document).on('click', '.show-replies-btn', function() {
        var commentItem = $(this).closest('.comment-item');
        var repliesContainer = commentItem.find('.comment-replies');
        
        repliesContainer.slideToggle();
        $(this).find('i').toggleClass('fa-chevron-down fa-chevron-up');
    });
    
    // Handle comment like button
    $(document).on('click', '.comment-like-btn', function() {
        var commentId = $(this).data('comment-id');
        var likeBtn = $(this);
        var likeCount = likeBtn.find('.like-count');
        
        // Disable button temporarily
        likeBtn.prop('disabled', true);
        
        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_comment_like',
                comment_id: commentId,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update like count
                    likeCount.text(response.data.count);
                    
                    // Toggle like button appearance
                    if (response.data.action === 'liked') {
                        likeBtn.addClass('active');
                        likeBtn.find('i').removeClass('far').addClass('fas');
                    } else {
                        likeBtn.removeClass('active');
                        likeBtn.find('i').removeClass('fas').addClass('far');
                    }
                } else {
                    if (response.data.indexOf('تسجيل الدخول') > -1) {
                        // Redirect to login if not logged in
                        window.location.href = '<?php echo wp_login_url(get_permalink()); ?>';
                    } else {
                        alert(response.data);
                    }
                }
                
                // Re-enable button
                likeBtn.prop('disabled', false);
            },
            error: function() {
                alert('حدث خطأ أثناء تسجيل الإعجاب.');
                likeBtn.prop('disabled', false);
            }
        });
    });
    
    // Handle comment sorting
    $('.comment-sort-select').on('change', function() {
        var sortOption = $(this).val();
        var commentList = $('.comment-list');
        var comments = commentList.children('.comment-item').get();
        
        // Sort comments based on selected option
        comments.sort(function(a, b) {
            if (sortOption === 'oldest') {
                return $(a).data('comment-id') - $(b).data('comment-id');
            } else if (sortOption === 'newest') {
                return $(b).data('comment-id') - $(a).data('comment-id');
            } else if (sortOption === 'popular') {
                var likesA = parseInt($(a).find('.comment-like-btn .like-count').text()) || 0;
                var likesB = parseInt($(b).find('.comment-like-btn .like-count').text()) || 0;
                return likesB - likesA;
            }
        });
        
        // Reorder comments in the DOM
        $.each(comments, function(index, item) {
            commentList.append(item);
        });
    });
});
</script>

<?php
/**
 * Función personalizada para mostrar los comentarios
 */
if (!function_exists('sekaiplus_comment_callback')) {
    function sekaiplus_comment_callback($comment, $args, $depth) {
        $GLOBALS['comment'] = $comment;
        $comment_id = get_comment_ID();
        $is_approved = $comment->comment_approved;
        $classes = 'comment-item';
        
        // Agregar clase especial para comentarios en espera de aprobación
        if ($is_approved == '0') {
            $classes .= ' comment-awaiting-moderation';
        }
        ?>
        <div id="comment-<?php echo $comment_id; ?>" class="<?php echo $classes; ?>" data-comment-id="<?php echo $comment_id; ?>">
            <div class="comment">
                <div class="comment-author">
                    <?php echo get_avatar($comment, $args['avatar_size']); ?>
                    <div class="comment-author-info">
                        <cite class="fn">
                            <?php comment_author_link(); ?>
                        </cite>
                        <div class="comment-meta">
                            <time datetime="<?php comment_time('c'); ?>">
                                <?php
                                printf(_x('%s ago', '%s = human-readable time difference', 'sekaiplus'), human_time_diff(get_comment_time('U'), current_time('timestamp')));
                                ?>
                            </time>
                        </div>
                    </div>
                </div>
                
                <div class="comment-content">
                    <?php comment_text(); ?>
                    
                    <?php if ($is_approved == '0' && $comment->user_id == get_current_user_id()) : ?>
                    <div class="comment-pending">
                        <i class="fas fa-clock"></i>
                        <span><?php _e('تعليقك قيد المراجعة وسيظهر بعد الموافقة عليه', 'sekaiplus'); ?></span>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="comment-actions">
                    <?php if ($is_approved == '1') : ?>
                    <div class="comment-reply">
                        <span class="comment-reply-btn" data-comment-id="<?php echo $comment_id; ?>"><i class="fas fa-reply"></i> <?php _e('رد', 'sekaiplus'); ?></span>
                    </div>
                    
                    <div class="comment-like">
                        <?php
                        $like_count = get_comment_meta($comment_id, 'comment_like_count', true) ?: 0;
                        $has_liked = is_user_logged_in() ? get_user_meta(get_current_user_id(), 'liked_comment_' . $comment_id, true) : false;
                        $like_icon_class = $has_liked ? 'fas' : 'far';
                        $like_btn_class = $has_liked ? 'comment-like-btn active' : 'comment-like-btn';
                        ?>
                        <button class="<?php echo $like_btn_class; ?>" data-comment-id="<?php echo $comment_id; ?>">
                            <i class="<?php echo $like_icon_class; ?> fa-heart"></i>
                            <span class="like-count"><?php echo $like_count; ?></span>
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($args['max_depth'] > 1 && $depth < $args['max_depth']) : ?>
            <div class="comment-replies">
                <!-- Replies will go here -->
            </div>
            <?php endif; ?>
        </div>
        <?php
    }
}
?>
