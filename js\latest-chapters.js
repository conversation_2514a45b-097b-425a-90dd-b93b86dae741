jQuery(document).ready(function($) {
    const latestChapters = $('#latest-chapters');
    const template = document.getElementById('chapter-template');
    const loadingSpinner = $('.latest-loading');
    let page = 1;
    let loading = false;
    let noMorePosts = false;
    let currentFilter = 'all';

    // Function to format date
    function timeAgo(date) {
        const seconds = Math.floor((new Date() - new Date(date)) / 1000);

        let interval = Math.floor(seconds / 31536000);
        if (interval > 1) return interval + ' سنوات';
        if (interval === 1) return 'سنة';

        interval = Math.floor(seconds / 2592000);
        if (interval > 1) return interval + ' شهور';
        if (interval === 1) return 'شهر';

        interval = Math.floor(seconds / 86400);
        if (interval > 1) return interval + ' أيام';
        if (interval === 1) return 'يوم';

        interval = Math.floor(seconds / 3600);
        if (interval > 1) return interval + ' ساعات';
        if (interval === 1) return 'ساعة';

        interval = Math.floor(seconds / 60);
        if (interval > 1) return interval + ' دقائق';
        if (interval === 1) return 'دقيقة';

        if (seconds < 10) return 'الآن';

        return Math.floor(seconds) + ' ثواني';
    }

    // Function to create chapter element
    function createChapterElement(chapter) {
        const clone = template.content.cloneNode(true);

        // Set novel cover
        const coverImg = clone.querySelector('.novel-cover-img');
        if (chapter.novel_cover) {
            coverImg.src = chapter.novel_cover;
        } else {
            coverImg.src = sekaiplus_latest.default_cover || get_default_cover();
        }
        coverImg.alt = chapter.novel_title;

        // Set novel cover link
        const coverLink = clone.querySelector('.novel-cover-link');
        coverLink.href = chapter.novel_link;
        coverLink.title = chapter.novel_title;

        // Set novel details
        const novelLink = clone.querySelector('.novel-link');
        novelLink.href = chapter.novel_link;
        novelLink.textContent = chapter.novel_title;

        // Set chapter details
        const chapterLink = clone.querySelector('.chapter-link');
        chapterLink.href = chapter.chapter_link;
        chapterLink.textContent = chapter.chapter_title;

        // Set translator details
        const translatorLink = clone.querySelector('.translator-link');
        translatorLink.href = chapter.translator_link;
        translatorLink.textContent = chapter.translator_name;

        // Set translator avatar
        const translatorAvatar = clone.querySelector('.translator-avatar');
        if (chapter.translator_avatar) {
            translatorAvatar.src = chapter.translator_avatar;
            translatorAvatar.alt = chapter.translator_name;
        } else {
            translatorAvatar.style.display = 'none';
        }

        // Set time ago
        const timeElement = clone.querySelector('.time-ago');
        timeElement.textContent = timeAgo(chapter.date);

        return clone;
    }

    // Function to get default cover if novel cover is missing
    function get_default_cover() {
        return 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22200%22%20height%3D%22250%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20200%20250%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_text%20%7B%20fill%3A%23999%3Bfont-weight%3Anormal%3Bfont-family%3AArial%2C%20Helvetica%2C%20Open%20Sans%2C%20sans-serif%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder%22%3E%3Crect%20width%3D%22200%22%20height%3D%22250%22%20fill%3D%22%23373940%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2256.1875%22%20y%3D%22130.1%22%20id%3D%22holder_text%22%3E%D8%BA%D9%84%D8%A7%D9%81%20%D8%A7%D9%84%D8%B1%D9%88%D8%A7%D9%8A%D8%A9%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E';
    }

    // Function to show loading spinner
    function showLoading() {
        if (page === 1) {
            loadingSpinner.show();
        } else {
            latestChapters.append('<div class="latest-loading mt-4"><div class="latest-loading-spinner"></div><div class="latest-loading-text">جاري تحميل المزيد...</div></div>');
        }
    }

    // Function to hide loading spinner
    function hideLoading() {
        loadingSpinner.hide();
        $('.latest-loading.mt-4').remove();
    }

    // Function to load chapters
    function loadChapters(filter = currentFilter) {
        if (loading || noMorePosts) return;

        loading = true;
        showLoading();

        $.ajax({
            url: sekaiplus_latest.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_load_latest_chapters',
                nonce: sekaiplus_latest.nonce,
                page: page,
                filter: filter
            },
            success: function(response) {
                hideLoading();

                if (response.success && response.data) {
                    const chapters = response.data;

                    if (chapters.length === 0) {
                        noMorePosts = true;
                        if (page === 1) {
                            latestChapters.html(`
                                <div class="no-chapters-message">
                                    <i class="fas fa-info-circle"></i>
                                    <p>لا توجد فصول حالياً</p>
                                    <div class="sub-message">لم يتم العثور على فصول تطابق معايير البحث الحالية</div>
                                </div>
                            `);
                        }
                        return;
                    }

                    chapters.forEach(chapter => {
                        latestChapters.append(createChapterElement(chapter));
                    });

                    page++;
                } else {
                    noMorePosts = true;
                    if (page === 1) {
                        latestChapters.html(`
                            <div class="no-chapters-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <p>حدث خطأ</p>
                                <div class="sub-message">لم نتمكن من تحميل الفصول، يرجى المحاولة مرة أخرى لاحقاً</div>
                            </div>
                        `);
                    }
                }
            },
            error: function() {
                hideLoading();
                if (page === 1) {
                    latestChapters.html(`
                        <div class="no-chapters-message">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>حدث خطأ أثناء تحميل الفصول</p>
                            <div class="sub-message">يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى</div>
                        </div>
                    `);
                } else {
                    latestChapters.append('<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>حدث خطأ أثناء تحميل الفصول</div>');
                }
                noMorePosts = true;
            },
            complete: function() {
                loading = false;
            }
        });
    }

    // Initial load
    loadChapters();

    // Handle filter buttons
    $('.latest-filter-btn').on('click', function() {
        const filterBtn = $(this);
        const filter = filterBtn.data('filter');

        // Skip if already active
        if (filter === currentFilter) return;

        // Update UI
        $('.latest-filter-btn').removeClass('active');
        filterBtn.addClass('active');

        // Reset and load with new filter
        currentFilter = filter;
        page = 1;
        noMorePosts = false;
        latestChapters.empty();
        loadChapters(filter);
    });

    // Dark mode is now handled by the main theme toggle in the header
    // We don't need to handle it here anymore as it's managed globally
    // The CSS in page-latest-chapters.php now responds to body.dark-mode

    // Infinite scroll
    $(window).scroll(function() {
        if ($(window).scrollTop() + $(window).height() > $(document).height() - 300) {
            loadChapters();
        }
    });
});
