<?php
get_header();

global $current_novel, $current_volume;

// التأكد من وجود المتغيرات المطلوبة
if (!isset($current_novel) || !isset($current_volume)) {
    wp_redirect(home_url('/404'));
    exit;
}

$volume_number = $current_volume['number'];
$chapters = $current_volume['chapters'];

// الحصول على معلومات المجلد
$volume_covers = get_post_meta($current_novel->ID, '_volume_covers', true);
$volume_index = intval($volume_number) - 1;
$volume_cover = isset($volume_covers[$volume_index]['image']) ? $volume_covers[$volume_index]['image'] : get_the_post_thumbnail_url($current_novel->ID, 'full');
$volume_title = isset($volume_covers[$volume_index]['title']) ? $volume_covers[$volume_index]['title'] : '';

// تجميع الفصول حسب رقم الفصل لحساب عدد الفصول الفريدة
$unique_chapters = [];
foreach ($chapters as $chapter) {
    $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
    if (!isset($unique_chapters[$chapter_number])) {
        $unique_chapters[$chapter_number] = true;
    }
}
$unique_chapter_count = count($unique_chapters);

// الحصول على رابط الرواية
$novel_url = get_permalink($current_novel->ID);
?>

<div class="volume-page-wrapper">
    <!-- شريط التنقل (Breadcrumb) -->
    <div class="breadcrumb-wrapper">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo esc_url(home_url()); ?>">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo esc_url($novel_url); ?>"><?php echo esc_html(get_the_title($current_novel->ID)); ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">
                        المجلد <?php echo esc_html($volume_number); ?>
                        <?php if (!empty($volume_title)): ?>
                            - <?php echo esc_html($volume_title); ?>
                        <?php endif; ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- معلومات المجلد -->
    <div class="volume-header-section">
        <div class="container">
            <div class="volume-header-card">
                <div class="volume-cover-wrapper">
                    <img src="<?php echo esc_url($volume_cover); ?>"
                         alt="غلاف المجلد <?php echo esc_attr($volume_number); ?>"
                         class="volume-cover">
                </div>
                <div class="volume-info">
                    <h1 class="volume-title">
                        <?php echo esc_html(get_the_title($current_novel->ID)); ?>
                    </h1>
                    <h2 class="volume-subtitle">
                        المجلد <?php echo esc_html($volume_number); ?>
                        <?php if (!empty($volume_title)): ?>
                            <span class="volume-title-text">- <?php echo esc_html($volume_title); ?></span>
                        <?php endif; ?>
                    </h2>
                    <div class="volume-meta">
                        <div class="volume-badge">
                            <i class="fas fa-book-open"></i>
                            <span><?php echo $unique_chapter_count; ?> فصل</span>
                        </div>
                        <a href="<?php echo esc_url($novel_url); ?>" class="back-to-novel-btn">
                            <i class="fas fa-arrow-right"></i>
                            <span>العودة للرواية</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-4">
        <div class="row">
            <div class="col-12">

            <!-- قائمة الفصول -->
            <div class="chapters-section">
                <div class="chapters-header">
                    <h3 class="chapters-title">
                        <i class="fas fa-list-ul"></i>
                        قائمة الفصول
                    </h3>
                    <div class="chapters-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="expandAllChapters">
                            <i class="fas fa-expand-alt"></i> عرض الكل
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="collapseAllChapters">
                            <i class="fas fa-compress-alt"></i> طي الكل
                        </button>
                    </div>
                </div>

                <div class="chapters-content">
                    <?php if (!empty($chapters)):
                        // تجميع الفصول حسب رقم الفصل
                        $grouped_chapters = [];
                        foreach ($chapters as $chapter) {
                            $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                            $chapter_unique_id = get_post_meta($chapter->ID, '_chapter_unique_id', true);

                            // استخدام رقم الفصل كمفتاح للتجميع
                            if (!isset($grouped_chapters[$chapter_number])) {
                                $grouped_chapters[$chapter_number] = [];
                            }

                            $grouped_chapters[$chapter_number][] = $chapter;
                        }

                        // ترتيب الفصول حسب رقم الفصل
                        ksort($grouped_chapters, SORT_NUMERIC);
                    ?>
                        <div class="chapters-list">
                            <?php
                            $chapter_count = 0;
                            foreach ($grouped_chapters as $chapter_number => $chapter_translations):
                                $chapter_count++;
                                // ترتيب الترجمات حسب تاريخ النشر
                                usort($chapter_translations, function($a, $b) {
                                    return strtotime($a->post_date) - strtotime($b->post_date);
                                });

                                // استخدام عنوان الفصل الأول كعنوان افتراضي
                                $first_published_chapter = $chapter_translations[0];
                                $chapter_title = $first_published_chapter->post_title;

                                // تحديد ما إذا كان الفصل جديدًا (تم نشره خلال الأسبوع الماضي)
                                $is_new_chapter = (time() - strtotime($first_published_chapter->post_date)) < (7 * 24 * 60 * 60);

                                // إذا كان هناك ترجمة واحدة فقط
                                if (count($chapter_translations) === 1):
                            ?>
                                <div class="chapter-item">
                                    <a href="<?php echo get_permalink($first_published_chapter->ID); ?>" class="chapter-link">
                                        <div class="chapter-info">
                                            <div class="chapter-main">
                                                <span class="chapter-number">الفصل <?php echo esc_html($chapter_number); ?></span>
                                                <?php if (!empty($chapter_title)): ?>
                                                    <span class="chapter-title">- <?php echo esc_html($chapter_title); ?></span>
                                                <?php endif; ?>
                                                <?php if ($is_new_chapter): ?>
                                                    <span class="chapter-badge new">جديد</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="chapter-meta">
                                                <span class="translator-name">
                                                    <?php if ($translator_avatar = get_avatar_url($first_published_chapter->post_author, ['size' => 24])): ?>
                                                        <img src="<?php echo esc_url($translator_avatar); ?>"
                                                             alt="<?php echo esc_attr(get_the_author_meta('display_name', $first_published_chapter->post_author)); ?>"
                                                             class="translator-avatar">
                                                    <?php endif; ?>
                                                    <?php echo get_the_author_meta('display_name', $first_published_chapter->post_author); ?>
                                                </span>
                                                <span class="chapter-date">
                                                    <i class="far fa-clock"></i>
                                                    <?php echo human_time_diff(strtotime($first_published_chapter->post_date), current_time('timestamp')); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="chapter-arrow">
                                            <i class="fas fa-chevron-left"></i>
                                        </div>
                                    </a>
                                </div>
                            <?php else: // إذا كان هناك أكثر من ترجمة ?>
                                <div class="chapter-item multiple-translations">
                                    <div class="chapter-header" data-bs-toggle="collapse" data-bs-target="#chapter-translations-<?php echo $chapter_number; ?>">
                                        <div class="chapter-info">
                                            <div class="chapter-main">
                                                <span class="chapter-number">الفصل <?php echo esc_html($chapter_number); ?></span>
                                                <?php if (!empty($chapter_title)): ?>
                                                    <span class="chapter-title">- <?php echo esc_html($chapter_title); ?></span>
                                                <?php endif; ?>
                                                <?php if ($is_new_chapter): ?>
                                                    <span class="chapter-badge new">جديد</span>
                                                <?php endif; ?>
                                            </div>
                                            <div class="translations-count">
                                                <span class="badge bg-primary rounded-pill"><?php echo count($chapter_translations); ?> ترجمة</span>
                                                <i class="fas fa-chevron-down toggle-icon"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="collapse" id="chapter-translations-<?php echo $chapter_number; ?>">
                                        <div class="translations-list">
                                            <?php foreach ($chapter_translations as $translation):
                                                $translator_name = get_the_author_meta('display_name', $translation->post_author);
                                                $translator_avatar = get_avatar_url($translation->post_author, ['size' => 24]);
                                            ?>
                                                <a href="<?php echo get_permalink($translation->ID); ?>" class="translation-item">
                                                    <div class="translator-info">
                                                        <?php if ($translator_avatar): ?>
                                                            <img src="<?php echo esc_url($translator_avatar); ?>"
                                                                 alt="<?php echo esc_attr($translator_name); ?>"
                                                                 class="translator-avatar">
                                                        <?php endif; ?>
                                                        <span class="translator-name"><?php echo esc_html($translator_name); ?></span>
                                                    </div>
                                                    <div class="translation-meta">
                                                        <span class="translation-date">
                                                            <i class="far fa-clock"></i>
                                                            <?php echo human_time_diff(strtotime($translation->post_date), current_time('timestamp')); ?>
                                                        </span>
                                                        <i class="fas fa-chevron-left"></i>
                                                    </div>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            <?php endforeach; ?>
                        </div>


                    <?php else: ?>
                        <div class="no-chapters">
                            <div class="no-chapters-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="no-chapters-text">
                                لا توجد فصول في هذا المجلد بعد.
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --volume-primary: var(--bs-primary, #0d6efd);
    --volume-secondary: var(--bs-secondary, #6c757d);
    --volume-success: var(--bs-success, #198754);
    --volume-info: var(--bs-info, #0dcaf0);
    --volume-warning: var(--bs-warning, #ffc107);
    --volume-danger: var(--bs-danger, #dc3545);
    --volume-light: var(--bs-light, #f8f9fa);
    --volume-dark: var(--bs-dark, #212529);
    --volume-bg: var(--bs-body-bg, #fff);
    --volume-text: var(--bs-body-color, #212529);
    --volume-border: rgba(0, 0, 0, 0.125);
    --volume-shadow: rgba(0, 0, 0, 0.1);
    --volume-hover-bg: rgba(0, 0, 0, 0.03);
    --volume-transition: all 0.3s ease;
}

.dark-mode {
    --volume-bg: var(--bs-dark, #212529);
    --volume-text: var(--bs-light, #f8f9fa);
    --volume-border: rgba(255, 255, 255, 0.125);
    --volume-shadow: rgba(0, 0, 0, 0.25);
    --volume-hover-bg: rgba(255, 255, 255, 0.05);
}

/* ===== Volume Page Wrapper ===== */
.volume-page-wrapper {
    background-color: var(--volume-bg);
    color: var(--volume-text);
    transition: var(--volume-transition);
}

/* ===== Breadcrumb Styles ===== */
.breadcrumb-wrapper {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--volume-border);
    margin-bottom: 2rem;
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: var(--volume-primary);
    text-decoration: none;
    transition: var(--volume-transition);
}

.breadcrumb-item a:hover {
    color: rgba(var(--bs-primary-rgb), 0.8);
}

.breadcrumb-item.active {
    color: var(--volume-secondary);
}

/* ===== Volume Header Section ===== */
.volume-header-section {
    margin-bottom: 2rem;
}

.volume-header-card {
    display: flex;
    align-items: center;
    background-color: var(--volume-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--volume-shadow);
    overflow: hidden;
    padding: 1.5rem;
    transition: var(--volume-transition);
}

.volume-cover-wrapper {
    flex: 0 0 auto;
    margin-right: 2rem;
    position: relative;
}

.volume-cover {
    width: 180px;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem var(--volume-shadow);
    transition: var(--volume-transition);
    object-fit: cover;
}

.volume-info {
    flex: 1;
}

.volume-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--volume-text);
}

.volume-subtitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--volume-secondary);
}

.volume-title-text {
    font-weight: 500;
    color: var(--volume-text);
    opacity: 0.9;
    margin-right: 0.5rem;
}

.volume-meta {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.volume-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--volume-primary);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
}

.volume-badge i {
    margin-right: 0.5rem;
}

.back-to-novel-btn {
    display: inline-flex;
    align-items: center;
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
    color: var(--volume-secondary);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    text-decoration: none;
    font-weight: 500;
    transition: var(--volume-transition);
}

.back-to-novel-btn:hover {
    background-color: var(--volume-primary);
    color: white;
}

.back-to-novel-btn i {
    margin-right: 0.5rem;
}

/* ===== Chapters Section ===== */
.chapters-section {
    background-color: var(--volume-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--volume-shadow);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: var(--volume-transition);
}

.chapters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--volume-border);
    background-color: rgba(var(--bs-primary-rgb), 0.03);
}

.chapters-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--volume-text);
    display: flex;
    align-items: center;
}

.chapters-title i {
    margin-right: 0.75rem;
    color: var(--volume-primary);
}

.chapters-actions {
    display: flex;
    gap: 0.5rem;
}

.chapters-content {
    padding: 1.5rem;
    position: relative;
}

/* ===== Chapter List Styles ===== */
.chapters-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.chapter-item {
    border-radius: 0.5rem;
    border: 1px solid var(--volume-border);
    overflow: hidden;
    transition: var(--volume-transition);
}

.chapter-item:hover {
    box-shadow: 0 0.25rem 0.75rem var(--volume-shadow);
    transform: translateY(-2px);
}

.chapter-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    color: var(--volume-text);
    text-decoration: none;
    transition: var(--volume-transition);
}

.chapter-link:hover {
    background-color: var(--volume-hover-bg);
}

.chapter-info {
    flex: 1;
}

.chapter-main {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.chapter-number {
    color: var(--volume-primary);
    font-weight: 600;
}

.chapter-title {
    color: var(--volume-text);
    font-weight: 500;
}

.chapter-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.chapter-badge.new {
    background-color: rgba(var(--bs-success-rgb), 0.1);
    color: var(--volume-success);
}

.chapter-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--volume-secondary);
}

.translator-name {
    display: inline-flex;
    align-items: center;
}

.translator-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 0.5rem;
    object-fit: cover;
}

.chapter-date {
    display: inline-flex;
    align-items: center;
}

.chapter-date i {
    margin-right: 0.5rem;
}

.chapter-arrow {
    flex: 0 0 auto;
    color: var(--volume-secondary);
    transition: var(--volume-transition);
}

.chapter-link:hover .chapter-arrow {
    color: var(--volume-primary);
    transform: translateX(-5px);
}

/* ===== Multiple Translations Styles ===== */
.chapter-item.multiple-translations .chapter-header {
    padding: 1rem 1.25rem;
    cursor: pointer;
    transition: var(--volume-transition);
}

.chapter-item.multiple-translations .chapter-header:hover {
    background-color: var(--volume-hover-bg);
}

.chapter-item.multiple-translations .chapter-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.translations-count {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.chapter-header[aria-expanded="true"] .toggle-icon {
    transform: rotate(180deg);
}

.translations-list {
    display: flex;
    flex-direction: column;
    border-top: 1px solid var(--volume-border);
    background-color: rgba(var(--bs-primary-rgb), 0.02);
}

.translation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.25rem;
    color: var(--volume-text);
    text-decoration: none;
    border-bottom: 1px solid var(--volume-border);
    transition: var(--volume-transition);
}

.translation-item:last-child {
    border-bottom: none;
}

.translation-item:hover {
    background-color: var(--volume-hover-bg);
}

.translator-info {
    display: flex;
    align-items: center;
}

.translation-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--volume-secondary);
    font-size: 0.875rem;
}

.translation-date {
    display: inline-flex;
    align-items: center;
}

.translation-date i {
    margin-right: 0.5rem;
}

.translation-item:hover i.fa-chevron-left {
    color: var(--volume-primary);
    transform: translateX(-5px);
}

/* ===== No Chapters Styles ===== */
.no-chapters {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
}

.no-chapters-icon {
    font-size: 3rem;
    color: var(--volume-secondary);
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-chapters-text {
    font-size: 1.25rem;
    color: var(--volume-secondary);
}



/* ===== Responsive Styles ===== */
@media (max-width: 991.98px) {
    .volume-header-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .volume-cover-wrapper {
        margin-right: 0;
        margin-bottom: 1.5rem;
    }

    .volume-meta {
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .volume-cover {
        width: 150px;
    }

    .volume-title {
        font-size: 1.5rem;
    }

    .volume-subtitle {
        font-size: 1.1rem;
    }

    .chapters-header {
        flex-direction: column;
        gap: 1rem;
    }

    .chapter-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .chapter-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .chapter-item.multiple-translations .chapter-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 575.98px) {
    .breadcrumb-wrapper {
        margin-bottom: 1rem;
    }

    .volume-header-section {
        margin-bottom: 1rem;
    }

    .volume-header-card {
        padding: 1rem;
    }

    .volume-cover {
        width: 120px;
    }

    .chapters-content {
        padding: 1rem;
    }

    .chapter-link,
    .chapter-item.multiple-translations .chapter-header,
    .translation-item {
        padding: 0.75rem 1rem;
    }

}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل حالة الفصول المتعددة
    const chapterHeaders = document.querySelectorAll('.chapter-header[data-bs-toggle="collapse"]');
    chapterHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const target = document.querySelector(this.getAttribute('data-bs-target'));
            if (target) {
                if (target.classList.contains('show')) {
                    target.classList.remove('show');
                    this.setAttribute('aria-expanded', 'false');
                } else {
                    target.classList.add('show');
                    this.setAttribute('aria-expanded', 'true');
                }
            }
        });
    });

    // زر عرض الكل
    const expandAllBtn = document.getElementById('expandAllChapters');
    if (expandAllBtn) {
        expandAllBtn.addEventListener('click', function() {
            const collapseElements = document.querySelectorAll('.collapse');
            collapseElements.forEach(el => {
                el.classList.add('show');
                const trigger = document.querySelector(`[data-bs-target="#${el.id}"]`);
                if (trigger) {
                    trigger.setAttribute('aria-expanded', 'true');
                }
            });
        });
    }

    // زر طي الكل
    const collapseAllBtn = document.getElementById('collapseAllChapters');
    if (collapseAllBtn) {
        collapseAllBtn.addEventListener('click', function() {
            const collapseElements = document.querySelectorAll('.collapse');
            collapseElements.forEach(el => {
                el.classList.remove('show');
                const trigger = document.querySelector(`[data-bs-target="#${el.id}"]`);
                if (trigger) {
                    trigger.setAttribute('aria-expanded', 'false');
                }
            });
        });
    }


});
</script>

<?php get_footer(); ?>
