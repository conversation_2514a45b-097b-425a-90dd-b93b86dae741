/**
 * 404 Page Interactive Elements
 * 
 * This script handles all the interactive elements on the 404 error page
 * including animations, hover effects, and particle generation.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Toggle search form
    const searchToggle = document.getElementById('searchToggle');
    const searchForm = document.querySelector('.error-search');
    
    if (searchToggle && searchForm) {
        searchToggle.addEventListener('click', function() {
            searchForm.style.display = searchForm.style.display === 'none' ? 'block' : 'none';
            
            if (searchForm.style.display === 'block') {
                searchForm.querySelector('input').focus();
            }
        });
    }
    
    // Add interactive hover effect to book pages
    const bookPages = document.querySelectorAll('.book-page');
    const errorBook = document.querySelector('.error-book');
    
    if (errorBook && bookPages.length) {
        errorBook.addEventListener('mouseenter', function() {
            bookPages.forEach((page, index) => {
                page.style.animation = 'none';
                setTimeout(() => {
                    page.style.transform = `rotateY(-${(index + 1) * 30}deg)`;
                }, index * 100);
            });
        });
        
        errorBook.addEventListener('mouseleave', function() {
            bookPages.forEach((page, index) => {
                page.style.transform = '';
                page.style.animation = `flip-page-${index + 1} 5s infinite`;
            });
        });
    }
    
    // Add random floating particles in the background
    const errorContent = document.querySelector('.error-content');
    
    if (errorContent) {
        createParticles(errorContent);
    }
    
    // Create random particles
    function createParticles(container) {
        for (let i = 0; i < 15; i++) {
            const particle = document.createElement('div');
            particle.className = 'error-particle';
            
            // Random styling
            const size = Math.random() * 10 + 5;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            particle.style.opacity = `${Math.random() * 0.5 + 0.1}`;
            
            // Random position
            particle.style.top = `${Math.random() * 100}%`;
            particle.style.left = `${Math.random() * 100}%`;
            
            // Animation
            const duration = Math.random() * 5 + 3;
            particle.style.animation = `float ${duration}s ease-in-out infinite`;
            particle.style.animationDelay = `${Math.random() * 5}s`;
            
            container.appendChild(particle);
        }
    }
    
    // Add shake effect when clicking on 404
    const errorCode = document.querySelector('.error-code');
    
    if (errorCode) {
        errorCode.addEventListener('click', function() {
            this.classList.add('shake');
            
            setTimeout(() => {
                this.classList.remove('shake');
            }, 500);
        });
    }
    
    // Add shake animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes shake {
            0%, 100% { transform: translate(-50%, -50%); }
            10%, 30%, 50%, 70%, 90% { transform: translate(-55%, -50%); }
            20%, 40%, 60%, 80% { transform: translate(-45%, -50%); }
        }
        
        .error-code.shake {
            animation: shake 0.5s ease-in-out;
        }
    `;
    document.head.appendChild(style);
});
