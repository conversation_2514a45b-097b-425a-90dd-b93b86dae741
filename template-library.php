<?php
/**
 * Template Name: مكتبة الروايات
 *
 * قالب متقدم لعرض مكتبة الروايات مع خيارات تصفية وبحث متعددة
 * @package Sekaiplus
 * @version 2.0
 */

get_header();

// الحصول على الفلاتر الحالية من URL
$current_genre = isset($_GET['genre']) ? sanitize_text_field(urldecode($_GET['genre'])) : '';
$current_status = isset($_GET['status']) ? sanitize_text_field(urldecode($_GET['status'])) : '';
$current_sort = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'latest';

// اسم ملف البحث لتحديثه لتحديث معايير البحث
$search_query = isset($_GET['novel_search']) ? sanitize_text_field($_GET['novel_search']) : '';

// تحديد عنوان URL للصفحة الحالية بشكل أكثر دقة
$current_page_id = get_queried_object_id();
$current_page_url = get_permalink($current_page_id);

// تحميل البيانات المخزنة مؤقتًا إذا كانت متوفرة
$cache_key = 'library_genres_' . md5(serialize([$current_genre, $current_status, $current_sort, $search_query]));
$cached_data = get_transient($cache_key);

// تحديد عدد الروايات في كل صفحة
$novels_per_page = apply_filters('sekaiplus_novels_per_page', 12);
?>

<div class="library-container" id="library-container">
    <!-- قسم الفلاتر -->
    <div class="filters-section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <!-- عنوان المكتبة -->
                    <div class="library-header">
                        <h1 class="library-title">مكتبة الروايات</h1>
                        <p class="library-subtitle">استكشف مجموعة متنوعة من الروايات المترجمة</p>
                    </div>

                    <!-- علامات التبويب للتصنيفات -->
                    <div class="categories-tabs">
                        <div class="scroll-container">
                            <?php
                            // الحصول على جميع التصنيفات مع عدد الروايات لكل تصنيف
                            $genres = get_terms(array(
                                'taxonomy' => 'genre',
                                'hide_empty' => true,
                                'orderby' => 'count',
                                'order' => 'DESC'
                            ));

                            // بناء الرابط الأساسي لفلتر التصنيف
                            $genre_base_url = add_query_arg(array(
                                'status' => $current_status,
                                'sort' => $current_sort,
                                'novel_search' => $search_query
                            ), $current_page_url);
                            ?>
                            <a href="<?php echo esc_url(remove_query_arg('genre', $genre_base_url)); ?>"
                               class="tab-btn <?php echo empty($current_genre) ? 'active' : ''; ?>">
                                <i class="fas fa-book-open"></i>
                                <span>الكل</span>
                                <span class="count"><?php echo wp_count_posts('novel')->publish; ?></span>
                            </a>
                            <?php foreach ($genres as $genre) : ?>
                                <a href="<?php echo esc_url(add_query_arg('genre', $genre->slug, $genre_base_url)); ?>"
                                   class="tab-btn <?php echo $current_genre === $genre->slug ? 'active' : ''; ?>">
                                    <span><?php echo $genre->name; ?></span>
                                    <span class="count"><?php echo $genre->count; ?></span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- الفلاتر المتقدمة -->
                    <form class="advanced-filters" method="GET" action="<?php echo esc_url($current_page_url); ?>">
                        <input type="hidden" name="page_id" value="<?php echo esc_attr($current_page_id); ?>">
                        <div class="row g-4">
                            <div class="col-lg-4">
                                <div class="filter-group">
                                    <label><i class="fas fa-bookmark"></i> الحالة</label>
                                    <div class="btn-group w-100" role="group">
                                        <?php
                                        // الحصول على جميع الحالات
                                        $statuses = get_terms(array(
                                            'taxonomy' => 'status',
                                            'hide_empty' => true
                                        ));
                                        ?>
                                        <input type="radio" class="btn-check" name="status" id="status-all" value="" <?php checked($current_status, ''); ?>>
                                        <label class="btn" for="status-all">الكل</label>

                                        <?php foreach ($statuses as $status) : ?>
                                            <input type="radio" class="btn-check" name="status"
                                                   id="status-<?php echo esc_attr($status->slug); ?>"
                                                   value="<?php echo esc_attr($status->slug); ?>"
                                                   <?php checked($current_status, $status->slug); ?>>
                                            <label class="btn" for="status-<?php echo esc_attr($status->slug); ?>">
                                                <?php echo esc_html($status->name); ?>
                                            </label>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="filter-group">
                                    <label><i class="fas fa-sort"></i> الترتيب حسب</label>
                                    <select class="form-select" name="sort">
                                        <option value="latest" <?php selected($current_sort, 'latest'); ?>>الأحدث تحديثًا</option>
                                        <option value="rating" <?php selected($current_sort, 'rating'); ?>>الأعلى تقييمًا</option>
                                        <option value="views" <?php selected($current_sort, 'views'); ?>>الأكثر مشاهدة</option>
                                        <option value="title" <?php selected($current_sort, 'title'); ?>>أبجدي (أ-ي)</option>
                                        <option value="chapters" <?php selected($current_sort, 'chapters'); ?>>عدد الفصول</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="filter-group">
                                    <label><i class="fas fa-search"></i> البحث</label>
                                    <div class="search-box">
                                        <input type="text" name="novel_search" value="<?php echo esc_attr($search_query); ?>" placeholder="ابحث عن رواية..." class="form-control">
                                        <button type="submit" class="btn-search">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الاحتفاظ بفلتر التصنيف -->
                        <?php if (!empty($current_genre)) : ?>
                            <input type="hidden" name="genre" value="<?php echo esc_attr($current_genre); ?>">
                        <?php endif; ?>

                        <!-- عرض الفلاتر النشطة -->
                        <?php if (!empty($current_genre) || !empty($current_status) || !empty($search_query)) : ?>
                        <div class="active-filters">
                            <?php if (!empty($current_genre)) :
                                $genre_term = get_term_by('slug', $current_genre, 'genre');
                            ?>
                                <div class="filter-tag">
                                    <span>التصنيف: <?php echo $genre_term->name; ?></span>
                                    <a href="<?php echo esc_url(remove_query_arg('genre', add_query_arg(array('status' => $current_status, 'sort' => $current_sort, 'novel_search' => $search_query), $current_page_url))); ?>" class="remove"><i class="fas fa-times"></i></a>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($current_status)) :
                                $status_term = get_term_by('slug', $current_status, 'status');
                            ?>
                                <div class="filter-tag">
                                    <span>الحالة: <?php echo $status_term->name; ?></span>
                                    <a href="<?php echo esc_url(remove_query_arg('status', add_query_arg(array('genre' => $current_genre, 'sort' => $current_sort, 'novel_search' => $search_query), $current_page_url))); ?>" class="remove"><i class="fas fa-times"></i></a>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($search_query)) : ?>
                                <div class="filter-tag">
                                    <span>البحث: <?php echo esc_html($search_query); ?></span>
                                    <a href="<?php echo esc_url(remove_query_arg('novel_search', add_query_arg(array('genre' => $current_genre, 'status' => $current_status, 'sort' => $current_sort), $current_page_url))); ?>" class="remove"><i class="fas fa-times"></i></a>
                                </div>
                            <?php endif; ?>

                            <a href="<?php echo esc_url($current_page_url); ?>" class="clear-all">مسح الكل <i class="fas fa-trash-alt"></i></a>
                        </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- شبكة الروايات -->
    <div class="novels-grid">
        <div class="container">
            <div class="row g-4" id="novelsContainer">
                <?php
                $tax_query = array();

                // إضافة فلتر التصنيف
                if (!empty($current_genre)) {
                    $tax_query[] = array(
                        'taxonomy' => 'genre',
                        'field' => 'slug',
                        'terms' => $current_genre
                    );
                }

                // إضافة فلتر الحالة
                if (!empty($current_status)) {
                    $tax_query[] = array(
                        'taxonomy' => 'status',
                        'field' => 'slug',
                        'terms' => $current_status
                    );
                }

                $args = array(
                    'post_type' => 'novel',
                    'posts_per_page' => $novels_per_page,
                    'paged' => get_query_var('paged') ? get_query_var('paged') : 1
                );

                // إضافة استعلام الضرائب إذا كانت الفلاتر معينة
                if (!empty($tax_query)) {
                    $args['tax_query'] = array(
                        'relation' => 'AND',
                        $tax_query
                    );
                }

                // إضافة استعلام البحث
                if (!empty($search_query)) {
                    // إضافة استعلام meta_query للبحث في كل حقول العناوين والبيانات الوصفية
                    $args['meta_query'] = array(
                        'relation' => 'OR',
                        // البحث في العنوان العربي
                        array(
                            'key'     => '_novel_title_arabic',
                            'value'   => $search_query,
                            'compare' => 'LIKE'
                        ),
                        // البحث في العنوان الياباني
                        array(
                            'key'     => 'japanese_title',
                            'value'   => $search_query,
                            'compare' => 'LIKE'
                        ),
                        // البحث في العنوان الروماجي
                        array(
                            'key'     => 'romaji_title',
                            'value'   => $search_query,
                            'compare' => 'LIKE'
                        ),
                        // البحث في العنوان الإنجليزي
                        array(
                            'key'     => '_novel_title_english',
                            'value'   => $search_query,
                            'compare' => 'LIKE'
                        ),
                        // البحث في اسم المؤلف
                        array(
                            'key'     => '_novel_author',
                            'value'   => $search_query,
                            'compare' => 'LIKE'
                        )
                    );

                    // إضافة بحث في عنوان المنشور الأساسي أيضًا
                    add_filter('posts_where', function($where, $query) use ($search_query) {
                        global $wpdb;
                        if (isset($query->query['_meta_or_title']) && $query->query['_meta_or_title'] == $search_query) {
                            $search_term = '%' . $wpdb->esc_like($search_query) . '%';
                            $where .= $wpdb->prepare(" OR {$wpdb->posts}.post_title LIKE %s", $search_term);
                        }
                        return $where;
                    }, 10, 2);

                    $args['_meta_or_title'] = $search_query;
                }

                // إضافة التصنيف
                switch ($current_sort) {
                    case 'rating':
                        $args['meta_key'] = '_novel_rating';
                        $args['orderby'] = 'meta_value_num';
                        $args['order'] = 'DESC';
                        break;
                    case 'title':
                        $args['orderby'] = 'title';
                        $args['order'] = 'ASC';
                        break;
                    case 'views':
                        $args['meta_key'] = '_novel_views';
                        $args['orderby'] = 'meta_value_num';
                        $args['order'] = 'DESC';
                        break;
                    case 'chapters':
                        $args['meta_key'] = '_novel_chapters';
                        $args['orderby'] = 'meta_value_num';
                        $args['order'] = 'DESC';
                        break;
                    default: // latest
                        $args['orderby'] = 'date';
                        $args['order'] = 'DESC';
                }

                $novels_query = new WP_Query($args);

                if ($novels_query->have_posts()) :
                    while ($novels_query->have_posts()) : $novels_query->the_post();
                        // الحصول على بيانات الرواية
                        $status_terms = wp_get_object_terms(get_the_ID(), 'status', array('fields' => 'names'));
                        $status = !empty($status_terms) ? $status_terms[0] : '';
                        $status_class = '';

                        // الحصول على بيانات التقييم
                        $user_ratings = get_post_meta(get_the_ID(), '_user_ratings', true);
                        $rating_count = is_array($user_ratings) ? count($user_ratings) : 0;
                        $average_rating = 0;

                        if ($rating_count > 0) {
                            $total_rating = 0;
                            foreach ($user_ratings as $rating) {
                                $total_rating += $rating['rating'];
                            }
                            $average_rating = round($total_rating / $rating_count, 1);
                        }

                        // الحصول على عدد الفصول
                        $chapters_count = get_post_meta(get_the_ID(), '_novel_chapters', true);
                        $chapters_count = !empty($chapters_count) ? intval($chapters_count) : 0;

                        // الحصول على عدد المشاهدات
                        $views_count = get_post_meta(get_the_ID(), '_novel_views', true);
                        $views_count = !empty($views_count) ? intval($views_count) : 0;

                        // الحصول على التصنيفات
                        $genres = wp_get_object_terms(get_the_ID(), 'genre', array('fields' => 'names'));

                        // الحصول على حالة الرواية
                        if ($status) {
                            if (strpos($status, 'مكتمل') !== false || strpos(strtolower($status), 'complet') !== false) {
                                $status_class = 'completed';
                            } elseif (strpos($status, 'مستمر') !== false || strpos(strtolower($status), 'ongoing') !== false) {
                                $status_class = 'ongoing';
                            } elseif (strpos($status, 'متوقف') !== false || strpos(strtolower($status), 'hiatus') !== false) {
                                $status_class = 'hiatus';
                            } elseif (strpos($status, 'ملغي') !== false || strpos(strtolower($status), 'cancel') !== false) {
                                $status_class = 'cancelled';
                            } else {
                                $status_class = sanitize_html_class(strtolower($status));
                            }
                        }
                ?>
                        <div class="col-6 col-md-4 col-lg-3">
                            <div class="novel-card">
                                <div class="novel-cover">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('novel-cover', array('class' => 'img-fluid')); ?>
                                        <?php else : ?>
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/default-cover.png" alt="غلاف افتراضي" class="img-fluid">
                                        <?php endif; ?>
                                    </a>
                                    <?php if ($status) : ?>
                                        <div class="novel-status <?php echo $status_class; ?>">
                                            <?php echo esc_html($status); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($rating_count > 0) : ?>
                                    <div class="novel-rating">
                                        <i class="fas fa-star"></i> <?php echo $average_rating; ?>
                                    </div>
                                    <?php endif; ?>

                                    <div class="novel-hover-info">
                                        <div class="hover-content">
                                            <h3><?php the_title(); ?></h3>
                                            <?php if (!empty($genres)) : ?>
                                            <div class="genres">
                                                <?php foreach (array_slice($genres, 0, 3) as $genre) : ?>
                                                    <span class="genre-tag"><?php echo $genre; ?></span>
                                                <?php endforeach; ?>
                                                <?php if (count($genres) > 3) : ?>
                                                    <span class="genre-tag more">+<?php echo count($genres) - 3; ?></span>
                                                <?php endif; ?>
                                            </div>
                                            <?php endif; ?>

                                            <div class="novel-stats">
                                                <div class="stat">
                                                    <i class="fas fa-book-open"></i>
                                                    <span><?php echo sekaiplus_get_unique_chapter_count(get_the_ID()) . ' فصل'; ?></span>
                                                </div>
                                                <div class="stat">
                                                    <i class="fas fa-eye"></i>
                                                    <span><?php echo number_format($views_count); ?></span>
                                                </div>
                                            </div>

                                            <div class="novel-excerpt">
                                                <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="novel-info">
                                    <h3 class="novel-title">
                                        <a href="<?php the_permalink(); ?>" title="<?php the_title(); ?>"><?php echo wp_trim_words(get_the_title(), 4, '...'); ?></a>
                                    </h3>
                                    <div class="novel-meta">
                                        <div class="last-update">
                                            <i class="fas fa-history"></i>
                                            <span><?php echo human_time_diff(get_the_modified_time('U'), current_time('timestamp')) . ' ' . __('مضت', 'sekaiplus'); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                <?php
                    endwhile;
                    wp_reset_postdata();
                else:
                ?>
                    <div class="col-12 text-center py-5">
                        <div class="no-results">
                            <i class="fas fa-book-open fa-3x mb-3 text-muted"></i>
                            <h3>لا توجد روايات</h3>
                            <p class="text-muted">لم يتم العثور على روايات تطابق معايير البحث</p>
                            <a href="<?php echo esc_url($current_page_url); ?>" class="btn btn-primary mt-3">عرض جميع الروايات</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- التصفح -->
            <?php if ($novels_query->max_num_pages > 1) : ?>
                <div class="pagination-wrapper mt-5">
                    <?php
                    $big = 999999999;
                    echo paginate_links(array(
                        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                        'format' => '?paged=%#%',
                        'current' => max(1, get_query_var('paged')),
                        'total' => $novels_query->max_num_pages,
                        'prev_text' => '<i class="fas fa-chevron-right"></i>',
                        'next_text' => '<i class="fas fa-chevron-left"></i>',
                        'type' => 'list',
                        'add_args' => array(
                            'genre' => $current_genre,
                            'status' => $current_status,
                            'sort' => $current_sort,
                            'novel_search' => $search_query
                        )
                    ));
                    ?>
                </div>
            <?php endif; ?>

            <!-- إحصائيات المكتبة -->
            <div class="library-stats mt-5">
                <div class="row g-3">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-book"></i></div>
                            <div class="stat-info">
                                <h3><?php echo wp_count_posts('novel')->publish; ?></h3>
                                <p>رواية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-bookmark"></i></div>
                            <div class="stat-info">
                                <h3><?php echo count(get_terms(array('taxonomy' => 'genre', 'hide_empty' => true))); ?></h3>
                                <p>تصنيف</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
                            <div class="stat-info">
                                <?php
                                $chapters_count = $wpdb->get_var("SELECT COUNT(*) FROM $wpdb->posts WHERE post_type='chapter' AND post_status='publish'");
                                ?>
                                <h3><?php echo number_format($chapters_count); ?></h3>
                                <p>فصل</p>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// تخزين البيانات مؤقتًا لتحسين الأداء
if (!$cached_data) {
    set_transient($cache_key, true, HOUR_IN_SECONDS);
}

get_footer();
?>

<script>
jQuery(document).ready(function($) {
    // حفظ حالة الفلاتر في URL
    function updateURL(params) {
        const url = new URL(window.location.href);
        Object.keys(params).forEach(key => {
            if (params[key]) {
                url.searchParams.set(key, params[key]);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.history.pushState({}, '', url);
    }

    // تحديث الفلاتر عند تغيير الحالة
    $('input[name="status"]').change(function() {
        $(this).closest('form').submit();
    });

    // تأخير البحث حتى يتوقف المستخدم عن الكتابة
    let searchTimeout;
    $('.search-box input').on('input', function() {
        clearTimeout(searchTimeout);
        const $form = $(this).closest('form');
        searchTimeout = setTimeout(function() {
            $form.submit();
        }, 500);
    });

    // إضافة حالة التحميل
    $('form').on('submit', function() {
        $('#novelsContainer').closest('.novels-grid').addClass('loading');
        // عرض مؤشر التحميل
        if ($('#loading-indicator').length === 0) {
            $('#novelsContainer').before('<div id="loading-indicator" class="text-center py-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        } else {
            $('#loading-indicator').show();
        }
    });

    // تحريك إلى أعلى الصفحة عند تغيير الفلتر
    $('.tab-btn, .btn-check, select[name="sort"]').on('change click', function() {
        $('html, body').animate({
            scrollTop: $('.library-container').offset().top - 100
        }, 500);
    });

    // تمرير سلس للتصنيفات
    const scrollContainer = document.querySelector('.scroll-container');
    let isDown = false;
    let startX;
    let scrollLeft;

    scrollContainer.addEventListener('mousedown', (e) => {
        isDown = true;
        scrollContainer.classList.add('active');
        startX = e.pageX - scrollContainer.offsetLeft;
        scrollLeft = scrollContainer.scrollLeft;
    });

    scrollContainer.addEventListener('mouseleave', () => {
        isDown = false;
        scrollContainer.classList.remove('active');
    });

    scrollContainer.addEventListener('mouseup', () => {
        isDown = false;
        scrollContainer.classList.remove('active');
    });

    scrollContainer.addEventListener('mousemove', (e) => {
        if (!isDown) return;
        e.preventDefault();
        const x = e.pageX - scrollContainer.offsetLeft;
        const walk = (x - startX) * 2;
        scrollContainer.scrollLeft = scrollLeft - walk;
    });

    // تمرير التصنيف النشط إلى المنتصف
    const activeTab = document.querySelector('.tab-btn.active');
    if (activeTab) {
        activeTab.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
        });
    }

    // تفعيل Lazy Loading للصور
    if ('loading' in HTMLImageElement.prototype) {
        const images = document.querySelectorAll('img[loading="lazy"]');
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    } else {
        // Fallback لمتصفحات قديمة
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/lazysizes/5.3.2/lazysizes.min.js';
        document.body.appendChild(script);
    }

    // تحسين تجربة المستخدم عند التمرير
    let scrollTimer;
    $(window).scroll(function() {
        clearTimeout(scrollTimer);
        $('body').addClass('is-scrolling');

        scrollTimer = setTimeout(function() {
            $('body').removeClass('is-scrolling');
        }, 250);
    });
});
</script>

<style>
/* ===== متغيرات CSS ===== */
:root {
    --library-primary: var(--bs-primary, #0d6efd);
    --library-secondary: var(--bs-secondary, #6c757d);
    --library-success: var(--bs-success, #198754);
    --library-danger: var(--bs-danger, #dc3545);
    --library-warning: var(--bs-warning, #ffc107);
    --library-info: var(--bs-info, #0dcaf0);
    --library-light: var(--bs-light, #f8f9fa);
    --library-dark: var(--bs-dark, #212529);
    --library-bg: var(--bs-body-bg, #fff);
    --library-text: var(--bs-body-color, #212529);
    --library-border: rgba(0, 0, 0, 0.125);
    --library-shadow: rgba(0, 0, 0, 0.1);
    --library-hover-bg: rgba(0, 0, 0, 0.03);
    --library-transition: all 0.3s ease;
    --library-gradient-primary: linear-gradient(90deg, #6d8cff, #f49ca0);
    --library-gradient-hover: linear-gradient(90deg, #4361ee, #ff6b6b);
}

.dark-mode {
    --library-bg: var(--bs-dark, #212529);
    --library-text: var(--bs-light, #f8f9fa);
    --library-border: rgba(255, 255, 255, 0.125);
    --library-shadow: rgba(0, 0, 0, 0.25);
    --library-hover-bg: rgba(255, 255, 255, 0.05);
    --library-gradient-primary: linear-gradient(90deg, #233466, #7c4a4a);
}

/* تحسينات إضافية للتمرير */
.scroll-container.active {
    cursor: grabbing;
    cursor: -webkit-grabbing;
    user-select: none;
}

.scroll-container {
    cursor: grab;
    cursor: -webkit-grab;
}

/* Library Page Styles */
.library-container {
    padding: 2rem 0;
    background: var(--library-bg);
}

/* ===== عنوان المكتبة ===== */
.library-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--library-border);
}

.library-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--library-text);
    background: var(--library-gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.library-subtitle {
    font-size: 1.1rem;
    color: var(--library-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== علامات تبويب التصنيفات ===== */
.categories-tabs {
    position: relative;
    margin-bottom: 2rem;
    background: var(--library-bg);
    padding: 0.75rem;
    border-radius: 1rem;
    box-shadow: 0 0.25rem 1rem var(--library-shadow);
    border: 1px solid var(--library-border);
}

.scroll-container {
    display: flex;
    overflow-x: auto;
    gap: 0.75rem;
    padding: 0.5rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.scroll-container::-webkit-scrollbar {
    display: none;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: var(--library-bg);
    color: var(--library-text);
    border-radius: 2rem;
    white-space: nowrap;
    transition: var(--library-transition);
    border: 1px solid var(--library-border);
    text-decoration: none;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.tab-btn .count {
    background: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--library-primary);
    border-radius: 2rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.85rem;
    min-width: 1.5rem;
    text-align: center;
}

.tab-btn:hover {
    background: var(--library-primary);
    color: #fff;
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.2);
}

.tab-btn:hover .count {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* ===== أنماط الفلتر النشط ===== */
.tab-btn.active {
    background: var(--library-gradient-primary);
    color: #fff;
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.3);
    position: relative;
    font-weight: 600;
    border: none;
}

.tab-btn.active .count {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.tab-btn.active::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: var(--library-primary);
    border-radius: 50%;
}

/* ===== الفلاتر المتقدمة ===== */
.advanced-filters {
    background: var(--library-bg);
    padding: 1.75rem;
    border-radius: 1rem;
    box-shadow: 0 0.25rem 1rem var(--library-shadow);
    margin-bottom: 2rem;
    border: 1px solid var(--library-border);
}

.filter-group {
    margin-bottom: 1.25rem;
}

.filter-group label {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--library-text);
    gap: 0.75rem;
    font-size: 1rem;
}

.filter-group label i {
    color: var(--library-primary);
    width: 1.5rem;
    text-align: center;
}

.btn-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-group .btn {
    flex: 1;
    background: var(--library-bg);
    border: 1px solid var(--library-border);
    color: var(--library-text);
    transition: var(--library-transition);
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    border-radius: 0.5rem;
}

.btn-group .btn:hover {
    background: var(--library-primary);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--bs-primary-rgb), 0.2);
}

.btn-check:checked + .btn {
    background: var(--library-primary);
    color: #fff;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--bs-primary-rgb), 0.3);
    position: relative;
    font-weight: 600;
}

.btn-check:checked + .btn::after {
    content: "\f00c";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.85rem;
    margin-right: 0.5rem;
}

/* ===== حالة التحديد النشطة ===== */
.form-select {
    border: 1px solid var(--library-border);
    background-color: var(--library-bg);
    color: var(--library-text);
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    transition: var(--library-transition);
    border-radius: 0.5rem;
    font-size: 0.95rem;
    height: auto;
}

.form-select:focus {
    border-color: var(--library-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* ===== مؤشرات الفلتر النشط ===== */
.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 0.75rem;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--library-gradient-primary);
    color: #fff;
    border-radius: 2rem;
    font-size: 0.9rem;
    gap: 0.75rem;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--bs-primary-rgb), 0.2);
    transition: var(--library-transition);
}

.filter-tag:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.3);
}

.filter-tag .remove {
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.3s ease;
    color: #fff;
    text-decoration: none;
}

.filter-tag .remove:hover {
    opacity: 1;
}

.clear-all {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--library-danger, #dc3545);
    color: #fff;
    border-radius: 2rem;
    font-size: 0.9rem;
    gap: 0.75rem;
    text-decoration: none;
    transition: var(--library-transition);
    box-shadow: 0 0.25rem 0.75rem rgba(220, 53, 69, 0.2);
}

.clear-all:hover {
    background: var(--library-danger, #bb2d3b);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(220, 53, 69, 0.3);
}

/* ===== مربع البحث ===== */
.search-box {
    position: relative;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 3rem;
    background: var(--library-bg);
    border: 1px solid var(--library-border);
    color: var(--library-text);
    width: 100%;
    border-radius: 2rem;
    transition: var(--library-transition);
    font-size: 0.95rem;
    height: auto;
}

.search-box input:focus {
    border-color: var(--library-primary);
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
    outline: none;
}

.btn-search {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    border: none;
    background: none;
    padding: 0 1.25rem;
    color: var(--library-secondary);
    transition: var(--library-transition);
    font-size: 1rem;
}

.btn-search:hover {
    color: var(--library-primary);
}

/* ===== شبكة الروايات ===== */
.novels-grid {
    min-height: 400px;
    position: relative;
    padding: 1rem 0;
}

.novels-grid.loading {
    opacity: 0.7;
}

#loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

.novel-card {
    background: var(--library-bg);
    border-radius: 0.75rem;
    overflow: hidden;
    transition: var(--library-transition);
    box-shadow: 0 0.25rem 1rem var(--library-shadow);
    height: 100%;
    border: 1px solid var(--library-border);
    position: relative;
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem var(--library-shadow);
}

.novel-cover {
    position: relative;
    padding-top: 142.85714286%; /* 1:1.4285714286 aspect ratio */
    overflow: hidden;
    background-color: rgba(var(--bs-secondary-rgb), 0.1);
}

.novel-cover a {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.novel-cover img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.novel-card:hover .novel-cover img {
    transform: scale(1.05);
}

.novel-hover-info {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    opacity: 0;
    transition: var(--library-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.25rem;
    color: #fff;
    pointer-events: none;
}

.novel-card:hover .novel-hover-info {
    opacity: 1;
    pointer-events: auto;
}

.hover-content {
    width: 100%;
    overflow: hidden;
}

.hover-content h3 {
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #fff;
}

.genres {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.genre-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.8rem;
    white-space: nowrap;
}

.genre-tag.more {
    background: var(--library-primary);
}

.novel-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.novel-excerpt {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    opacity: 0.9;
    line-height: 1.4;
    max-height: 4.2rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}

.novel-rating, .novel-status {
    position: absolute;
    padding: 0.35rem 0.85rem;
    border-radius: 2rem;
    font-size: 0.85rem;
    font-weight: 600;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.2);
    z-index: 5;
}

.novel-rating {
    top: 0.75rem;
    left: 0.75rem;
    background: rgba(0, 0, 0, 0.6);
    color: var(--library-warning);
    display: flex;
    align-items: center;
    gap: 0.35rem;
}

.novel-status {
    top: 0.75rem;
    right: 0.75rem;
    color: #fff;
}

.novel-status.u0645u0643u062au0645u0644,
.novel-status.u0645u0643u062au0645u0644u0629,
.novel-status.completed {
    background: var(--library-success);
}

.novel-status.u0645u0633u062au0645u0631,
.novel-status.u0645u0633u062au0645u0631u0629,
.novel-status.ongoing {
    background: var(--library-primary);
}

.novel-status.u0645u062au0648u0642u0641,
.novel-status.u0645u062au0648u0642u0641u0629,
.novel-status.hiatus {
    background: var(--library-warning);
    color: #000;
}

.novel-status.u0645u0644u063au064a,
.novel-status.u0645u0644u063au064au0629,
.novel-status.cancelled {
    background: var(--library-danger);
}

.novel-info {
    padding: 1.25rem;
}

.novel-title {
    font-size: 1.1rem;
    margin: 0 0 0.75rem;
    line-height: 1.4;
    font-weight: 600;
}

.novel-title a {
    color: var(--library-text);
    text-decoration: none;
    transition: var(--library-transition);
}

.novel-title a:hover {
    color: var(--library-primary);
}

.novel-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(--library-secondary);
    font-size: 0.9rem;
}

.rating, .chapters {
    display: flex;
    align-items: center;
    gap: 0.35rem;
}

.rating .fas {
    color: var(--library-warning);
}

.chapters .fas {
    color: var(--library-primary);
}

.last-update {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.last-update i {
    color: var(--library-primary);
}

.dark-mode .novel-hover-info {
    background: rgba(0, 0, 0, 0.85);
}

.dark-mode .genre-tag {
    background: rgba(255, 255, 255, 0.15);
}

@media (max-width: 991.98px) {
    .library-title {
        font-size: 2rem;
    }

    .library-subtitle {
        font-size: 1rem;
    }

    .novel-card {
        margin-bottom: 1.5rem;
    }

    .novel-info {
        padding: 1rem;
    }

    .novel-title {
        font-size: 1rem;
    }
}

@media (max-width: 767.98px) {
    .library-title {
        font-size: 1.75rem;
    }

    .hover-content h3 {
        font-size: 1rem;
    }

    .novel-excerpt {
        font-size: 0.85rem;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        max-height: 2.8rem;
    }

    .genre-tag {
        font-size: 0.75rem;
    }

    .novel-stats {
        font-size: 0.8rem;
    }

    .novel-rating, .novel-status {
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }

    .page-numbers {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 575.98px) {
    .library-title {
        font-size: 1.5rem;
    }

    .library-subtitle {
        font-size: 0.9rem;
    }

    .tab-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }

    .filter-group label {
        font-size: 0.9rem;
    }

    .btn-group .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }

    .novel-title {
        font-size: 0.9rem;
    }

    .novel-meta {
        font-size: 0.8rem;
    }

    .filter-tag {
        font-size: 0.8rem;
        padding: 0.35rem 0.75rem;
    }

    .clear-all {
        font-size: 0.8rem;
    }
}

/* ===== الترقيم ===== */
.pagination-wrapper {
    margin-top: 3.5rem;
}

.pagination {
    justify-content: center;
    gap: 0.75rem;
}

.page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: var(--library-bg);
    color: var(--library-text);
    text-decoration: none;
    transition: var(--library-transition);
    border: 1px solid var(--library-border);
    font-weight: 500;
    font-size: 1rem;
}

.page-numbers:hover,
.page-numbers.current {
    background: var(--library-gradient-primary);
    color: #fff;
    border-color: transparent;
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.3);
}

/* ===== حالة عدم وجود نتائج ===== */
.no-results {
    text-align: center;
    padding: 4rem 0;
    background: var(--library-bg);
    border-radius: 1rem;
    box-shadow: 0 0.25rem 1rem var(--library-shadow);
    border: 1px solid var(--library-border);
}

.no-results i {
    color: var(--library-secondary);
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-results h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--library-text);
}

.no-results p {
    color: var(--library-secondary);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.no-results .btn {
    background: var(--library-gradient-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-weight: 500;
    transition: var(--library-transition);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--bs-primary-rgb), 0.2);
}

.no-results .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem rgba(var(--bs-primary-rgb), 0.3);
}

/* ===== إحصائيات المكتبة ===== */
.library-stats {
    margin-top: 5rem;
    padding-top: 2.5rem;
    border-top: 1px solid var(--library-border);
}

.stat-card {
    background: var(--library-bg);
    border-radius: 1rem;
    padding: 1.75rem;
    display: flex;
    align-items: center;
    gap: 1.25rem;
    box-shadow: 0 0.25rem 1rem var(--library-shadow);
    border: 1px solid var(--library-border);
    transition: var(--library-transition);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem var(--library-shadow);
}

.stat-icon {
    width: 3.5rem;
    height: 3.5rem;
    background: rgba(var(--bs-primary-rgb), 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--library-primary);
    font-size: 1.3rem;
    flex-shrink: 0;
}

.stat-info h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--library-text);
}

.stat-info p {
    margin: 0;
    color: var(--library-secondary);
    font-size: 1rem;
}

/* ===== تعديلات متجاوبة ===== */
@media (max-width: 768px) {
    .stat-card {
        padding: 1.25rem;
    }

    .stat-icon {
        width: 3rem;
        height: 3rem;
        font-size: 1.1rem;
    }

    .stat-info h3 {
        font-size: 1.5rem;
    }
}

/* ===== تعديلات الوضع الداكن ===== */
.dark-mode .advanced-filters {
    background: var(--library-bg);
    border-color: var(--library-border);
}

.dark-mode .btn-group .btn {
    background: var(--library-bg);
    border-color: var(--library-border);
    color: var(--library-text);
}

.dark-mode .form-select {
    background-color: var(--library-bg);
    border-color: var(--library-border);
    color: var(--library-text);
}

.dark-mode .search-box input {
    background: var(--library-bg);
    border-color: var(--library-border);
    color: var(--library-text);
}

.dark-mode .novel-card {
    background: var(--library-bg);
    border-color: var(--library-border);
}

.dark-mode .novel-info {
    background: var(--library-bg);
}

.dark-mode .page-numbers {
    background: var(--library-bg);
    border-color: var(--library-border);
}

.dark-mode .no-results {
    background: var(--library-bg);
    border-color: var(--library-border);
}

.dark-mode .stat-card {
    background: var(--library-bg);
    border-color: var(--library-border);
}

.dark-mode .active-filters {
    background: rgba(var(--bs-primary-rgb), 0.1);
}

/* ===== تأثيرات التمرير ===== */
.is-scrolling .novel-card {
    transition: transform 0.1s ease;
}
</style>
