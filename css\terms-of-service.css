/* تصميم صفحة شروط الخدمة */
:root {
    --terms-primary: #3498db;
    --terms-secondary: #2ecc71;
    --terms-accent: #f39c12;
    --terms-dark: #343a40;
    --terms-light: #ecf0f1;
    --terms-bg: #f8f9fa;
    --terms-card: #ffffff;
    --terms-text: #2c3e50;
    --terms-text-light: #7f8c8d;
    --terms-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --terms-border-radius: 15px;
}

/* دعم الوضع الداكن */
.dark-mode {
    --terms-bg: #1a1a2e;
    --terms-card: #343a40;
    --terms-text: #e3e3e3;
    --terms-text-light: #a8a8a8;
    --terms-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.dark-mode .terms-of-service-header {
    background: linear-gradient(135deg, #2c3e50, #1a1a2e);
}

.dark-mode .terms-section h2 {
    color: #3498db;
}

.dark-mode .terms-section h3 {
    color: #2ecc71;
}

.dark-mode .terms-section li:before {
    color: #f39c12;
}

.dark-mode .lang-btn {
    background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .lang-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .lang-btn.active {
    background-color: #3498db;
    color: white;
}

.terms-of-service-container {
    background-color: var(--terms-bg);
    color: var(--terms-text);
    font-family: 'Cairo', sans-serif;
    padding-bottom: 50px;
}

/* قواعد اتجاه المحتوى */
.rtl-content {
    direction: rtl;
}

.ltr-content {
    direction: ltr;
}

.terms-of-service-header {
    background: linear-gradient(135deg, var(--terms-primary), var(--terms-secondary));
    color: white;
    padding: 60px 0 40px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--terms-shadow);
}

.terms-of-service-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 Z" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 100% 100%;
}

.terms-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
}

.language-switcher {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    position: relative;
}

.lang-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.lang-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.4s ease;
    z-index: -1;
}

.lang-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.lang-btn:hover::before {
    transform: translateX(0);
}

.lang-btn.active {
    background-color: white;
    color: var(--terms-primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.lang-btn.active::before {
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.2), rgba(255,255,255,0));
    transform: translateX(100%);
    transition: transform 1.5s ease;
}

.terms-of-service-content {
    background-color: var(--terms-card);
    border-radius: var(--terms-border-radius);
    padding: 40px;
    box-shadow: var(--terms-shadow);
    margin-bottom: 30px;
    position: relative;
}

.terms-section {
    margin-bottom: 40px;
    animation: fadeIn 0.5s ease-in-out;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.terms-section:last-child {
    margin-bottom: 0;
}

.terms-section:hover h2 {
    transform: translateY(-2px);
    text-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.terms-section h2 {
    color: var(--terms-primary);
    font-size: 1.8rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--terms-accent);
    display: inline-block;
    transition: transform 0.3s ease, text-shadow 0.3s ease;
}

/* محاذاة العناوين حسب اللغة */
.terms-content-ar .terms-section h2 {
    /*float: right;*/
    clear: right;
}

.terms-content-en .terms-section h2,
.terms-content-ja .terms-section h2 {
    /*float: left;*/
    clear: left;
}

.terms-section h3 {
    color: var(--terms-secondary);
    font-size: 1.4rem;
    margin: 25px 0 15px;
}

.terms-section p {
    margin-bottom: 15px;
    line-height: 1.7;
}

/* قواعد عامة للقوائم */
.terms-section ul {
    margin-bottom: 20px;
}

.terms-section li {
    margin-bottom: 10px;
    position: relative;
}

/* قواعد القوائم للغة العربية */
.terms-content-ar .terms-section ul {
    padding-right: 20px;
    padding-left: 0;
}

.terms-content-ar .terms-section li {
    padding-right: 25px;
    padding-left: 0;
}

.terms-content-ar .terms-section li:before {
    content: '•';
    color: var(--terms-accent);
    font-weight: bold;
    position: absolute;
    right: 0;
    left: auto;
}

/* قواعد القوائم للغة الإنجليزية واليابانية */
.terms-content-en .terms-section ul,
.terms-content-ja .terms-section ul {
    padding-left: 20px;
    padding-right: 0;
}

.terms-content-en .terms-section li,
.terms-content-ja .terms-section li {
    padding-left: 25px;
    padding-right: 0;
}

.terms-content-en .terms-section li:before,
.terms-content-ja .terms-section li:before {
    content: '•';
    color: var(--terms-accent);
    font-weight: bold;
    position: absolute;
    left: 0;
    right: auto;
}

/* قواعد اتجاه النص للغات المختلفة */
.terms-content-ar {
    display: none;
    direction: rtl;
    text-align: right;
}

.terms-content-en, .terms-content-ja {
    display: none;
    direction: ltr;
    text-align: left;
}

.terms-content-ar.active, .terms-content-en.active, .terms-content-ja.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

/* تعديل اتجاه العناصر حسب اللغة */
.terms-content-ar h2, .terms-content-ar h3, .terms-content-ar p, .terms-content-ar ul {
    text-align: right;
}

.terms-content-en h2, .terms-content-en h3, .terms-content-en p, .terms-content-en ul,
.terms-content-ja h2, .terms-content-ja h3, .terms-content-ja p, .terms-content-ja ul {
    text-align: left;
}

/* تعديل اتجاه النقاط في القوائم */
.terms-content-ar .terms-section li:before {
    right: 0;
}

.terms-content-en .terms-section li:before,
.terms-content-ja .terms-section li:before {
    left: 0;
    right: auto;
}

/* زر العودة إلى الأعلى */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--terms-primary);
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background-color: var(--terms-secondary);
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* في الوضع الداكن */
.dark-mode .scroll-to-top {
    background-color: #2c3e50;
}

.dark-mode .scroll-to-top:hover {
    background-color: #3498db;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .terms-of-service-header {
        padding: 40px 0 30px;
    }
    
    .terms-title {
        font-size: 2rem;
    }
    
    .terms-of-service-content {
        padding: 25px;
    }
    
    .terms-section h2 {
        font-size: 1.5rem;
    }
    
    .terms-section h3 {
        font-size: 1.2rem;
    }
    
    /* تحسينات محاذاة العناوين على الأجهزة المحمولة */
    .terms-content-ar .terms-section h2,
    .terms-content-en .terms-section h2,
    .terms-content-ja .terms-section h2 {
        float: none;
        display: block;
        width: 100%;
    }
    
    /* تحسين المسافات للأجهزة المحمولة */
    .terms-section {
        margin-bottom: 30px;
    }
    
    .terms-section li {
        margin-bottom: 8px;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .language-switcher {
        flex-direction: column;
        align-items: center;
    }
    
    .lang-btn {
        width: 100%;
        max-width: 200px;
        margin-bottom: 5px;
    }
    
    .terms-of-service-content {
        padding: 20px 15px;
    }
    
    .terms-section h2 {
        font-size: 1.3rem;
    }
    
    .terms-section h3 {
        font-size: 1.1rem;
    }
    
    .terms-title {
        font-size: 1.8rem;
    }
    
    /* تحسين المسافات للشاشات الصغيرة جداً */
    .terms-section {
        margin-bottom: 25px;
    }
    
    .terms-section p {
        font-size: 0.95rem;
    }
    
    .terms-section li {
        font-size: 0.95rem;
    }
}
