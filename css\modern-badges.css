/* Modern Badge Styles */
:root {
    --elite-badge-bg: #ffcc00;
    --elite-badge-text: #000;
    --elite-badge-icon: #e8b320;
    --admin-badge-bg: #1aa1ff;
    --admin-badge-text: #fff;
    --admin-badge-icon: #fff;
    --translator-badge-bg: #28a745;
    --translator-badge-text: #fff;
    --translator-badge-icon: #fff;
    --reader-badge-bg: #6c757d;
    --reader-badge-text: #fff;
    --reader-badge-icon: #fff;
}

/* Base Badge Style */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.875rem;
    margin: 0.25rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.badge i {
    margin-right: 0.5rem;
}

/* Elite Badge */
.elite-badge {
    background-color: var(--elite-badge-bg);
    color: var(--elite-badge-text);
}

.elite-badge i {
    color: var(--elite-badge-icon);
}

/* Admin Badge */
.admin-badge {
    background-color: var(--admin-badge-bg);
    color: var(--admin-badge-text);
}

.admin-badge i {
    color: var(--admin-badge-icon);
}

/* Translator Badge */
.translator-badge {
    background-color: var(--translator-badge-bg);
    color: var(--translator-badge-text);
}

.translator-badge i {
    color: var(--translator-badge-icon);
}

/* Reader Badge */
.reader-badge {
    background-color: var(--reader-badge-bg);
    color: var(--reader-badge-text);
}

.reader-badge i {
    color: var(--reader-badge-icon);
}

/* Badge Hover Effects */
.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* RTL Support */
html[dir="rtl"] .badge i {
    margin-right: 0;
    margin-left: 0.5rem;
}
