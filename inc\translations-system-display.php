<?php
/**
 * u0648u0638u0627u0626u0641 u0639u0631u0636 u0646u0638u0627u0645 u062au0631u062cu0645u0627u062a u0627u0644u0641u0635u0648u0644 - SekaiPLU
 *
 * u064au0648u0641u0631 u0647u0630u0627 u0627u0644u0645u0644u0641 u0627u0644u0648u0638u0627u0626u0641 u0627u0644u0644u0627u0632u0645u0629 u0644u0639u0631u0636 u0648u0627u062cu0647u0627u062a u0646u0638u0627u0645 u0627u0644u062au0631u062cu0645u0627u062a u0627u0644u0645u062au0639u062fu062fu0629
 *
 * @package SekaiPLU
 */

if (!defined('ABSPATH')) {
    exit; // u0645u0646u0639 u0627u0644u0648u0635u0648u0644 u0627u0644u0645u0628u0627u0634u0631
}

/**
 * u0627u0644u062du0635u0648u0644 u0639u0644u0649 u062cu0645u064au0639 u062au0631u062cu0645u0627u062a u0627u0644u0641u0635u0644
 *
 * @param int $chapter_id u0645u0639u0631u0641 u0627u0644u0641u0635u0644
 * @return array u0645u0635u0641u0648u0641u0629 u0645u0646 u0627u0644u062au0631u062cu0645u0627u062a
 */
function sekaiplu_get_chapter_translations($chapter_id) {
    $chapter = get_post($chapter_id);
    if (!$chapter || $chapter->post_type !== 'chapter') {
        return array();
    }

    // u0627u0644u062du0635u0648u0644 u0639u0644u0649 u0627u0644u0645u0639u0631u0641 u0627u0644u0641u0631u064au062fu060c u0644u0647u0630u0627 u0627u0644u0641u0635u0644
    $unique_id = get_post_meta($chapter_id, '_chapter_unique_id', true);
    
    // u0625u0630u0627 u0644u0645 u064au0643u0646 u0647u0646u0627u0643 u0645u0639u0631u0641 u0641u0631u064au062fu060c u0625u0646u0634u0627u0621 u0648u0627u062du062f u0648u062du0641u0638u0647
    if (empty($unique_id)) {
        $unique_id = sekaiplu_get_chapter_unique_id($chapter_id);
    }

    // u0627u0644u0628u062du062b u0639u0646 u062cu0645u064au0639 u0627u0644u0641u0635u0648u0644 u0627u0644u062au064a u062au062du0645u0644 u0646u0641u0633 u0627u0644u0645u0639u0631u0641 u0627u0644u0641u0631u064au062fu0629
    $translations = get_posts(array(
        'post_type' => 'chapter',
        'posts_per_page' => -1,
        'post__not_in' => array($chapter_id),
        'meta_query' => array(
            array(
                'key' => '_chapter_unique_id',
                'value' => $unique_id
            )
        ),
        'orderby' => 'post_date',
        'order' => 'DESC'
    ));

    return array_map(function($translation) {
        $translator_id = $translation->post_author;
        return array(
            'ID' => $translation->ID,
            'unique_id' => get_post_meta($translation->ID, '_chapter_unique_id', true),
            'translator_id' => $translator_id,
            'translator_nicename' => get_the_author_meta('user_nicename', $translator_id),
            'translator_name' => get_the_author_meta('display_name', $translator_id),
            'translator_avatar' => get_avatar_url($translator_id, array('size' => 32)),
            'date' => get_the_date('Y-m-d', $translation->ID),
            'status' => get_post_status($translation->ID),
            'likes_count' => intval(get_post_meta($translation->ID, '_likes_count', true)),
            'views_count' => intval(get_post_meta($translation->ID, '_views_count', true)),
            'notes' => get_post_meta($translation->ID, '_translation_notes', true)
        );
    }, $translations);
}

/**
 * u0639u0631u0636 u0642u0627u0626u0645u0629 u062au0631u062cu0645u0627u062a u0627u0644u0641u0635u0644
 *
 * @param array $translations u0645u0635u0641u0648u0641u0629 u0645u0646 u0627u0644u062au0631u062cu0645u0627u062a
 * @param int $current_chapter_id u0645u0639u0631u0641 u0627u0644u0641u0635u0644 u0627u0644u062du0627u0644u064a
 * @param string $current_translator u0627u0644u0645u062au0631u062cu0645 u0627u0644u062du0627u0644u064a
 * @return void
 */
function sekaiplu_display_translations_list($translations, $current_chapter_id, $current_translator = '') {
    if (empty($translations)) {
        return;
    }
    
    echo '<div class="translations-list mb-4">';
    echo '<h5 class="mb-3"><i class="fas fa-language me-2"></i>u0627u0644u062au0631u062cu0645u0627u062a u0627u0644u0645u062au0648u0641u0631u0629:</h5>';
    echo '<div class="list-group">';
    
    // u0627u0644u062du0635u0648u0644 u0639u0644u0649 u0645u0639u0644u0648u0645u0627u062a u0627u0644u0641u0635u0644 u0627u0644u062du0627u0644u064a
    $current_chapter = get_post($current_chapter_id);
    $current_translator_name = get_the_author_meta('display_name', $current_chapter->post_author);
    $current_translator_nicename = get_the_author_meta('user_nicename', $current_chapter->post_author);
    $current_views = intval(get_post_meta($current_chapter_id, '_views_count', true));
    $current_likes = intval(get_post_meta($current_chapter_id, '_likes_count', true));
    $current_date = get_the_date('Y-m-d', $current_chapter_id);
    
    // u0625u0636u0627u0641u0629 u0627u0644u0641u0635u0644 u0627u0644u062du0627u0644u064a u0625u0644u0649 u0627u0644u0642u0627u0626u0645u0629
    echo '<a href="' . get_permalink($current_chapter_id) . '" class="list-group-item list-group-item-action active d-flex align-items-center gap-2">';
    echo get_avatar($current_chapter->post_author, 32, '', '', array('class' => 'rounded-circle'));
    echo '<div class="flex-grow-1">';
    echo '<div class="translator-name">' . esc_html($current_translator_name) . ' <span class="badge bg-success ms-2">u0627u0644u062du0627u0644u064au0629</span></div>';
    echo '<div class="translation-meta">';
    echo '<small class="text-light">';
    echo '<i class="far fa-calendar-alt me-1"></i>' . $current_date;
    echo '<span class="mx-1">u2022</span>';
    echo '<i class="fas fa-eye me-1"></i>' . number_format($current_views);
    echo '<span class="mx-1">u2022</span>';
    echo '<i class="fas fa-heart me-1"></i>' . number_format($current_likes);
    echo '</small>';
    echo '</div>';
    echo '</div>';
    echo '</a>';
    
    // u0625u0636u0627u0641u0629 u0628u0642u064au0629 u0627u0644u062au0631u062cu0645u0627u062a
    foreach ($translations as $translation) {
        // u0628u0646u0627u0621 u0631u0627u0628u0637 u0627u0644u062au0631u062cu0645u0629 u0645u0639 u0645u0639u0644u0645 u0627u0644u0643u0627u062au0628/u0627u0644u0645u062au0631u062cu0645
        $chapter_unique_id = get_post_meta($current_chapter_id, '_chapter_unique_id', true);
        
        // u0627u0633u062au062eu062fu0627u0645 u0645u0639u0631u0641 u0627u0644u0645u062au0631u062cu0645 u0645u0628u0627u0634u0631u0629
        $author_id = $translation['translator_id'];
        
        // u0644u0644u062au0635u062du064au062d u0641u0642u0637
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: u0628u0646u0627u0621 u0631u0627u0628u0637 u062au0631u062cu0645u0629 u0644u0644u0641u0635u0644 {$chapter_unique_id} u0648u0627u0644u0643u0627u062au0628 {$author_id} | u0646u0648u0639 u0627u0644u0628u064au0627u0646u0627u062a: " . gettype($author_id));
        }
        
        $translation_permalink = add_query_arg(
            array(
                'r' => $chapter_unique_id,
                'author' => strval($author_id) // u062au062du0648u064au0644 u0645u0639u0631u0641 u0627u0644u0643u0627u062au0628 u0625u0644u0649 u0646u0635 u0644u062au062cu0646u0628 u0645u0634u0627u0643u0644 u0627u0644u0645u0642u0627u0631u0646u0629
            ),
            home_url('/chapter/')
        );
        
        // u0644u0644u062au0635u062du064au062d u0641u0642u0637
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: u0627u0644u0631u0627u0628u0637 u0627u0644u0646u0647u0627u0626u064a u0644u0644u062au0631u062cu0645u0629: " . $translation_permalink);
        }
        
        echo '<a href="' . esc_url($translation_permalink) . '" class="list-group-item list-group-item-action d-flex align-items-center gap-2">';
        echo '<img src="' . esc_url($translation['translator_avatar']) . '" alt="" class="rounded-circle" width="32" height="32">';
        echo '<div class="flex-grow-1">';
        echo '<div class="translator-name">' . esc_html($translation['translator_name']) . '</div>';
        echo '<div class="translation-meta">';
        echo '<small class="text-muted">';
        echo '<i class="far fa-calendar-alt me-1"></i>' . $translation['date'];
        echo '<span class="mx-1">u2022</span>';
        echo '<i class="fas fa-eye me-1"></i>' . number_format($translation['views_count']);
        echo '<span class="mx-1">u2022</span>';
        echo '<i class="fas fa-heart me-1"></i>' . number_format($translation['likes_count']);
        echo '</small>';
        echo '</div>';
        echo '</div>';
        echo '</a>';
    }
    
    echo '</div>';
    echo '</div>';
}

/**
 * u0639u0631u0636 u0642u0627u0626u0645u0629 u0645u0646u0633u062fu0644u0629 u0644u0644u062au0631u062cu0645u0627u062a
 *
 * @param array $translations u0645u0635u0641u0648u0641u0629 u0645u0646 u0627u0644u062au0631u062cu0645u0627u062a
 * @return void
 */
function sekaiplu_display_translations_dropdown($translations) {
    if (empty($translations)) {
        return;
    }
    
    echo '<div class="dropdown ms-auto">';
    echo '<button class="btn btn-sm btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">';
    echo '<i class="fas fa-language me-1"></i>';
    echo 'u062au0631u062cu0645u0627u062a u0623u062eu0631u0649 (' . count($translations) . ')';
    echo '</button>';
    echo '<ul class="dropdown-menu translations-menu">';
    
    foreach ($translations as $translation) {
        // u0628u0646u0627u0621 u0631u0627u0628u0637 u0627u0644u062au0631u062cu0645u0629 u0645u0639 u0645u0639u0644u0645 u0627u0644u0643u0627u062au0628/u0627u0644u0645u062au0631u062cu0645
        $chapter_unique_id = get_post_meta($translation['ID'], '_chapter_unique_id', true);
        
        // u0627u0633u062au062eu062fu0627u0645 u0645u0639u0631u0641 u0627u0644u0645u062au0631u062cu0645 u0645u0628u0627u0634u0631u0629
        $author_id = $translation['translator_id'];
        
        // u0644u0644u062au0635u062du064au062d u0641u0642u0637
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: u0628u0646u0627u0621 u0631u0627u0628u0637 u062au0631u062cu0645u0629 u0644u0644u0641u0635u0644 {$chapter_unique_id} u0648u0627u0644u0643u0627u062au0628 {$author_id} | u0646u0648u0639 u0627u0644u0628u064au0627u0646u0627u062a: " . gettype($author_id));
        }
        
        $translation_permalink = add_query_arg(
            array(
                'r' => $chapter_unique_id,
                'author' => strval($author_id) // u062au062du0648u064au0644 u0645u0639u0631u0641 u0627u0644u0643u0627u062au0628 u0625u0644u0649 u0646u0635 u0644u062au062cu0646u0628 u0645u0634u0627u0643u0644 u0627u0644u0645u0642u0627u0631u0646u0629
            ),
            home_url('/chapter/')
        );
        
        // u0644u0644u062au0635u062du064au062d u0641u0642u0637
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: u0627u0644u0631u0627u0628u0637 u0627u0644u0646u0647u0627u0626u064a u0644u0644u062au0631u062cu0645u0629: " . $translation_permalink);
        }
        
        echo '<li>';
        echo '<a class="dropdown-item d-flex align-items-center gap-2" href="' . esc_url($translation_permalink) . '">';
        echo '<img src="' . esc_url($translation['translator_avatar']) . '" alt="" class="rounded-circle" width="24" height="24">';
        echo '<div class="flex-grow-1">';
        echo '<div class="translator-name">' . esc_html($translation['translator_name']) . '</div>';
        echo '<div class="translation-meta">';
        echo '<small class="text-muted">';
        echo '<i class="far fa-calendar-alt me-1"></i>' . $translation['date'];
        echo '<span class="mx-1">u2022</span>';
        echo '<i class="fas fa-eye me-1"></i>' . number_format($translation['views_count']);
        echo '<span class="mx-1">u2022</span>';
        echo '<i class="fas fa-heart me-1"></i>' . number_format($translation['likes_count']);
        echo '</small>';
        echo '</div>';
        echo '</div>';
        echo '</a>';
        echo '</li>';
    }
    
    echo '</ul>';
    echo '</div>';
}
