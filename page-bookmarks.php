<?php
/**
 * Template Name: Bookmarks
 */

// Redirect if not logged in
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

$user_id = get_current_user_id();
$paged = get_query_var('paged') ? get_query_var('paged') : 1;
$items_per_page = 12; // زيادة عدد العناصر لكل صفحة

// Get view mode from user preference or default to grid
$view_mode = isset($_GET['view']) && in_array($_GET['view'], ['grid', 'list']) ? $_GET['view'] : 'grid';

// Get sort option from user preference or default to date
$sort_option = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'date';

// Get search query if exists
$search_query = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';

// Get bookmarked novel IDs
$bookmarked_novels = get_user_meta($user_id, '_novel_bookmarks', true);
$bookmarked_novels = is_array($bookmarked_novels) ? $bookmarked_novels : array();

// Set up query arguments
$query_args = array(
    'post_type' => 'novel',
    'post__in' => !empty($bookmarked_novels) ? $bookmarked_novels : array(0),
    'posts_per_page' => $items_per_page,
    'paged' => $paged
);

// Add search if provided
if (!empty($search_query)) {
    $query_args['s'] = $search_query;
}

// Set orderby based on sort option
switch ($sort_option) {
    case 'title_asc':
        $query_args['orderby'] = 'title';
        $query_args['order'] = 'ASC';
        break;
    case 'title_desc':
        $query_args['orderby'] = 'title';
        $query_args['order'] = 'DESC';
        break;
    case 'date_asc':
        $query_args['orderby'] = 'date';
        $query_args['order'] = 'ASC';
        break;
    case 'date_desc':
    default:
        $query_args['orderby'] = 'date';
        $query_args['order'] = 'DESC';
        break;
}

// Query bookmarked novels
$novels_query = new WP_Query($query_args);

get_header();
?>

<div class="bookmarks-page">
    <div class="container py-5">
        <div class="bookmarks-header mb-4">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <h1 class="bookmarks-title mb-0">المفضلة</h1>
                    <p class="text-muted mb-0 d-none d-md-block">
                        <small>
                            <?php echo count($bookmarked_novels); ?>
                            <?php echo count($bookmarked_novels) == 1 ? 'رواية' : 'روايات'; ?> في المفضلة
                        </small>
                    </p>
                </div>

                <div class="col-md-8">
                    <div class="bookmarks-controls d-flex flex-wrap justify-content-md-end gap-2 mt-3 mt-md-0">
                        <!-- بحث -->
                        <div class="search-box">
                            <form method="get" action="<?php echo esc_url(get_permalink()); ?>" class="d-flex">
                                <?php if ($view_mode): ?>
                                    <input type="hidden" name="view" value="<?php echo esc_attr($view_mode); ?>">
                                <?php endif; ?>
                                <?php if ($sort_option): ?>
                                    <input type="hidden" name="sort" value="<?php echo esc_attr($sort_option); ?>">
                                <?php endif; ?>
                                <input type="text" name="search" class="form-control form-control-sm"
                                       placeholder="بحث في المفضلة..." value="<?php echo esc_attr($search_query); ?>">
                                <button type="submit" class="btn btn-sm btn-primary ms-2">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>

                        <!-- فرز -->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                    id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-sort me-1"></i> فرز
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                                <li>
                                    <a class="dropdown-item <?php echo $sort_option == 'date_desc' ? 'active' : ''; ?>"
                                       href="<?php echo add_query_arg(['sort' => 'date_desc', 'view' => $view_mode, 'search' => $search_query]); ?>">
                                        <i class="fas fa-calendar-alt me-2"></i> الأحدث أولاً
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item <?php echo $sort_option == 'date_asc' ? 'active' : ''; ?>"
                                       href="<?php echo add_query_arg(['sort' => 'date_asc', 'view' => $view_mode, 'search' => $search_query]); ?>">
                                        <i class="fas fa-calendar-alt me-2"></i> الأقدم أولاً
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item <?php echo $sort_option == 'title_asc' ? 'active' : ''; ?>"
                                       href="<?php echo add_query_arg(['sort' => 'title_asc', 'view' => $view_mode, 'search' => $search_query]); ?>">
                                        <i class="fas fa-sort-alpha-down me-2"></i> العنوان (أ-ي)
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item <?php echo $sort_option == 'title_desc' ? 'active' : ''; ?>"
                                       href="<?php echo add_query_arg(['sort' => 'title_desc', 'view' => $view_mode, 'search' => $search_query]); ?>">
                                        <i class="fas fa-sort-alpha-up me-2"></i> العنوان (ي-أ)
                                    </a>
                                </li>
                            </ul>
                        </div>

                        <!-- طريقة العرض -->
                        <div class="btn-group" role="group" aria-label="طريقة العرض">
                            <a href="<?php echo add_query_arg(['view' => 'grid', 'sort' => $sort_option, 'search' => $search_query]); ?>"
                               class="btn btn-sm btn-outline-secondary <?php echo $view_mode == 'grid' ? 'active' : ''; ?>">
                                <i class="fas fa-th-large"></i>
                            </a>
                            <a href="<?php echo add_query_arg(['view' => 'list', 'sort' => $sort_option, 'search' => $search_query]); ?>"
                               class="btn btn-sm btn-outline-secondary <?php echo $view_mode == 'list' ? 'active' : ''; ?>">
                                <i class="fas fa-list"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($search_query)): ?>
            <div class="search-results-info alert alert-info d-flex align-items-center mb-4">
                <i class="fas fa-search me-2"></i>
                <div>
                    نتائج البحث عن: <strong><?php echo esc_html($search_query); ?></strong>
                    <a href="<?php echo remove_query_arg('search', add_query_arg(['view' => $view_mode, 'sort' => $sort_option])); ?>"
                       class="ms-2 text-decoration-none">
                        <i class="fas fa-times-circle"></i> إلغاء البحث
                    </a>
                </div>
            </div>
        <?php endif; ?>

    <?php if (!$novels_query->have_posts()): ?>
        <div class="card shadow-sm">
            <div class="card-body text-center py-5">
                <i class="fas fa-bookmark fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد روايات في المفضلة.</h5>
                <?php if (!empty($search_query)): ?>
                    <p class="text-muted mb-0">لا توجد نتائج تطابق بحثك. حاول استخدام كلمات مختلفة.</p>
                <?php else: ?>
                    <p class="text-muted mb-0">يمكنك إضافة الروايات إلى المفضلة من صفحة الرواية.</p>
                <?php endif; ?>

                <?php if (!empty($search_query)): ?>
                    <div class="mt-3">
                        <a href="<?php echo remove_query_arg('search', add_query_arg(['view' => $view_mode, 'sort' => $sort_option])); ?>"
                           class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> العودة إلى كل المفضلة
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php else: ?>
        <?php if ($view_mode == 'grid'): ?>
            <!-- عرض الشبكة -->
            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-4 bookmarks-grid">
                <?php while ($novels_query->have_posts()): $novels_query->the_post(); ?>
                    <div class="col">
                        <div class="card h-100 shadow-sm novel-card position-relative">
                            <!-- زر الحذف -->
                            <button type="button" class="btn btn-danger btn-sm rounded-circle position-absolute top-0 end-0 m-2 bookmark-toggle"
                                    data-novel-id="<?php echo get_the_ID(); ?>" style="z-index: 2;" title="إزالة من المفضلة">
                                <i class="fas fa-times"></i>
                            </button>

                            <!-- صورة الرواية -->
                            <a href="<?php the_permalink(); ?>" class="text-decoration-none">
                                <div class="novel-cover-wrapper">
                                    <?php if (has_post_thumbnail()): ?>
                                        <?php the_post_thumbnail('novel-cover', array(
                                            'class' => 'novel-cover',
                                            'alt' => get_the_title()
                                        )); ?>
                                    <?php else: ?>
                                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/default-cover.png"
                                             class="novel-cover" alt="<?php echo get_the_title(); ?>">
                                    <?php endif; ?>
                                </div>
                            </a>

                            <div class="card-body">
                                <!-- عنوان الرواية -->
                                <h5 class="card-title">
                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none text-reset">
                                        <?php the_title(); ?>
                                    </a>
                                </h5>

                                <!-- معلومات إضافية -->
                                <div class="d-flex justify-content-between align-items-center small text-muted">
                                    <span>
                                        <i class="fas fa-book-open me-1"></i>
                                        <?php echo sekaiplus_get_chapter_count(get_the_ID()); ?> فصل
                                    </span>
                                    <span>
                                        <i class="fas fa-star text-warning me-1"></i>
                                        <?php echo sekaiplus_get_novel_rating(get_the_ID()); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <!-- عرض القائمة -->
            <div class="bookmarks-list">
                <?php while ($novels_query->have_posts()): $novels_query->the_post(); ?>
                    <div class="card mb-3 shadow-sm novel-list-item">
                        <div class="row g-0">
                            <div class="col-md-2 col-sm-3 col-4">
                                <a href="<?php the_permalink(); ?>" class="text-decoration-none">
                                    <div class="novel-list-cover-wrapper h-100">
                                        <?php if (has_post_thumbnail()): ?>
                                            <?php the_post_thumbnail('novel-cover', array(
                                                'class' => 'novel-list-cover',
                                                'alt' => get_the_title()
                                            )); ?>
                                        <?php else: ?>
                                            <img src="<?php echo get_template_directory_uri(); ?>/assets/images/default-cover.png"
                                                 class="novel-list-cover" alt="<?php echo get_the_title(); ?>">
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>
                            <div class="col-md-10 col-sm-9 col-8">
                                <div class="card-body position-relative">
                                    <!-- زر الحذف -->
                                    <button type="button" class="btn btn-danger btn-sm rounded-circle position-absolute top-0 end-0 m-2 bookmark-toggle"
                                            data-novel-id="<?php echo get_the_ID(); ?>" title="إزالة من المفضلة">
                                        <i class="fas fa-times"></i>
                                    </button>

                                    <h5 class="card-title">
                                        <a href="<?php the_permalink(); ?>" class="text-decoration-none text-reset">
                                            <?php the_title(); ?>
                                        </a>
                                    </h5>

                                    <div class="novel-list-meta d-flex flex-wrap gap-3 text-muted small mb-2">
                                        <span>
                                            <i class="fas fa-book-open me-1"></i>
                                            <?php echo sekaiplus_get_chapter_count(get_the_ID()); ?> فصل
                                        </span>
                                        <span>
                                            <i class="fas fa-star text-warning me-1"></i>
                                            <?php echo sekaiplus_get_novel_rating(get_the_ID()); ?>
                                        </span>
                                        <span>
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            <?php echo get_the_date(); ?>
                                        </span>
                                    </div>

                                    <div class="novel-list-excerpt text-muted small d-none d-md-block">
                                        <?php echo wp_trim_words(get_the_excerpt(), 30, '...'); ?>
                                    </div>

                                    <a href="<?php the_permalink(); ?>" class="btn btn-sm btn-primary mt-2">
                                        عرض الرواية <i class="fas fa-arrow-left me-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php endif; ?>

        <!-- Pagination -->
        <?php if ($novels_query->max_num_pages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <?php
                $big = 999999999;
                echo paginate_links(array(
                    'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
                    'format' => '?paged=%#%',
                    'current' => max(1, get_query_var('paged')),
                    'total' => $novels_query->max_num_pages,
                    'type' => 'list',
                    'prev_text' => '<span aria-hidden="true">&laquo;</span>',
                    'next_text' => '<span aria-hidden="true">&raquo;</span>',
                    'class' => 'pagination justify-content-center'
                ));
                ?>
            </nav>
        <?php endif; ?>
    <?php endif; ?>
    <?php wp_reset_postdata(); ?>
</div>

<style>
/* تنسيقات عامة */
.bookmarks-page {
    --primary-color: #007bff;
    --primary-hover: #0069d9;
    --danger-color: #dc3545;
    --danger-hover: #c82333;
    --card-bg: #fff;
    --card-border: rgba(0, 0, 0, 0.125);
    --text-color: #212529;
    --text-muted: #6c757d;
    --shadow-sm: 0 .125rem .25rem rgba(0, 0, 0, .075);
    --shadow: 0 .5rem 1rem rgba(0, 0, 0, .15);
    --transition-speed: 0.3s;
}

/* تنسيقات الرأس */
.bookmarks-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-color);
}

.bookmarks-controls .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.bookmarks-controls .btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.bookmarks-controls .btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.bookmarks-controls .btn-outline-secondary.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.dropdown-item.active {
    background-color: var(--primary-color);
}

/* تنسيقات عرض الشبكة */
.novel-card {
    transition: all var(--transition-speed);
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأخير ظهور البطاقات بشكل متتالي */
.novel-card:nth-child(1) { animation-delay: 0.1s; }
.novel-card:nth-child(2) { animation-delay: 0.2s; }
.novel-card:nth-child(3) { animation-delay: 0.3s; }
.novel-card:nth-child(4) { animation-delay: 0.4s; }
.novel-card:nth-child(5) { animation-delay: 0.5s; }
.novel-card:nth-child(n+6) { animation-delay: 0.6s; }

.novel-cover-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 4px 4px 0 0;
    height: 220px;
}

.novel-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-speed);
}

.novel-card:hover .novel-cover {
    transform: scale(1.05);
}

.card-title {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 2.5rem;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

/* تنسيقات عرض القائمة */
.novel-list-item {
    transition: all var(--transition-speed);
    overflow: hidden;
    opacity: 0;
    transform: translateX(20px);
    animation: fadeInRight 0.5s ease forwards;
}

.novel-list-item:hover {
    transform: translateX(-5px);
    box-shadow: var(--shadow);
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تأخير ظهور عناصر القائمة بشكل متتالي */
.novel-list-item:nth-child(1) { animation-delay: 0.1s; }
.novel-list-item:nth-child(2) { animation-delay: 0.2s; }
.novel-list-item:nth-child(3) { animation-delay: 0.3s; }
.novel-list-item:nth-child(4) { animation-delay: 0.4s; }
.novel-list-item:nth-child(5) { animation-delay: 0.5s; }
.novel-list-item:nth-child(n+6) { animation-delay: 0.6s; }

.novel-list-cover-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 4px 0 0 4px;
}

.novel-list-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-speed);
}

.novel-list-item:hover .novel-list-cover {
    transform: scale(1.05);
}

/* زر الحذف من المفضلة */
.bookmark-toggle {
    opacity: 0.8;
    transition: all var(--transition-speed);
    z-index: 10;
}

.bookmark-toggle:hover {
    opacity: 1;
    background-color: var(--danger-hover);
}

/* تنسيقات الوضع الداكن */
.dark-mode .bookmarks-page,
[data-theme="dark"] .bookmarks-page {
    --card-bg: #2d3748;
    --card-border: rgba(255, 255, 255, 0.1);
    --text-color: #f8f9fa;
    --text-muted: #a0aec0;
    --shadow-sm: 0 .125rem .25rem rgba(0, 0, 0, .2);
    --shadow: 0 .5rem 1rem rgba(0, 0, 0, .3);
}

.dark-mode .novel-card,
.dark-mode .novel-list-item,
[data-theme="dark"] .novel-card,
[data-theme="dark"] .novel-list-item {
    background-color: var(--card-bg);
    border-color: var(--card-border);
}

.dark-mode .card-title a,
.dark-mode .novel-list-meta,
[data-theme="dark"] .card-title a,
[data-theme="dark"] .novel-list-meta {
    color: var(--text-color) !important;
}

.dark-mode .text-muted,
[data-theme="dark"] .text-muted {
    color: var(--text-muted) !important;
}

.dark-mode .search-results-info,
[data-theme="dark"] .search-results-info {
    background-color: rgba(23, 162, 184, 0.2);
    border-color: rgba(23, 162, 184, 0.3);
    color: #e9ecef;
}

/* تأثير العناصر عند التمرير */
.novel-card.in-view,
.novel-list-item.in-view {
    animation: none;
    opacity: 1;
    transform: translateY(0) translateX(0);
}

/* تأثير النبض عند التحويم */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.bookmark-toggle:hover i {
    animation: pulse 0.5s infinite;
}

/* تنسيقات التجاوب */
@media (max-width: 767.98px) {
    .novel-cover-wrapper {
        height: 180px;
    }

    .novel-list-cover-wrapper {
        height: 150px;
        border-radius: 4px 4px 0 0;
    }

    .novel-list-item .row {
        flex-direction: column;
    }

    .novel-list-item .col-md-2 {
        width: 100%;
        margin-bottom: 0;
    }

    .novel-list-item .col-md-10 {
        width: 100%;
    }
}

@media (max-width: 575.98px) {
    .bookmarks-controls {
        justify-content: center !important;
    }

    .search-box {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .novel-cover-wrapper {
        height: 160px;
    }
}
</style>

<script>
(function($) {
    'use strict';

    // تفعيل زر حذف الرواية من المفضلة
    $('.bookmark-toggle').on('click', function(e) {
        e.preventDefault();

        const $btn = $(this);
        const novelId = $btn.data('novel-id');
        const $card = $btn.closest('.novel-card, .novel-list-item');

        // تأثير بصري للحذف
        $card.css('opacity', '0.5');

        // إرسال طلب الحذف
        $.ajax({
            url: sekaiplus.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_toggle_bookmark',
                nonce: sekaiplus.nonce,
                novel_id: novelId
            },
            success: function(response) {
                if (response.success) {
                    // تأثير بصري وحذف البطاقة
                    $card.fadeOut(300, function() {
                        $(this).remove();

                        // التحقق إذا كانت هذه آخر رواية
                        if ($('.novel-card, .novel-list-item').length === 0) {
                            location.reload(); // إعادة تحميل الصفحة لعرض رسالة "لا توجد روايات"
                        }
                    });
                } else {
                    // إعادة البطاقة للحالة الطبيعية في حالة الفشل
                    $card.css('opacity', '1');
                    alert('حدث خطأ أثناء الحذف. يرجى المحاولة مرة أخرى.');
                }
            },
            error: function() {
                // إعادة البطاقة للحالة الطبيعية في حالة الفشل
                $card.css('opacity', '1');
                alert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            }
        });
    });

    // تفعيل البحث الفوري
    let searchTimeout;
    $('input[name="search"]').on('input', function() {
        clearTimeout(searchTimeout);
        const $form = $(this).closest('form');
        const $submitBtn = $form.find('button[type="submit"]');

        // إظهار أيقونة التحميل
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i>');

        searchTimeout = setTimeout(function() {
            $form.submit();
        }, 500);
    });

    // تحسين عرض التنقل بين الصفحات
    $('.pagination .page-numbers').addClass('page-link');
    $('.pagination .page-item').addClass('mx-1');

    // تفعيل تأثيرات التمرير
    $(window).scroll(function() {
        const scrollTop = $(this).scrollTop();

        // ظهور البطاقات تدريجياً عند التمرير
        $('.novel-card, .novel-list-item').each(function() {
            const $card = $(this);
            const cardTop = $card.offset().top;
            const cardHeight = $card.height();
            const windowHeight = $(window).height();

            if (scrollTop + windowHeight > cardTop && scrollTop < cardTop + cardHeight) {
                $card.addClass('in-view');
            }
        });
    }).scroll(); // تشغيل الحدث عند تحميل الصفحة
})(jQuery);
</script>

<?php get_footer(); ?>
