<?php
/**
 * Custom template tags for this theme
 */

/**
 * Display novel rating stars
 */
function sekaiplus_novel_rating_stars($rating) {
    $whole = floor($rating);
    $fraction = $rating - $whole;
    $output = '';

    // Full stars
    for ($i = 0; $i < $whole; $i++) {
        $output .= '<i class="fas fa-star text-warning"></i>';
    }

    // Half star
    if ($fraction >= 0.5) {
        $output .= '<i class="fas fa-star-half-alt text-warning"></i>';
        $whole++;
    }

    // Empty stars
    for ($i = $whole; $i < 5; $i++) {
        $output .= '<i class="far fa-star text-warning"></i>';
    }

    return $output;
}

/**
 * Display novel status badge
 */
function sekaiplus_novel_status_badge($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $status_terms = get_the_terms($post_id, 'status');
    if (!$status_terms || is_wp_error($status_terms)) {
        return '';
    }

    $status = $status_terms[0];
    $badge_class = 'bg-secondary';

    switch ($status->slug) {
        case 'ongoing':
            $badge_class = 'bg-primary';
            break;
        case 'completed':
            $badge_class = 'bg-success';
            break;
        case 'hiatus':
            $badge_class = 'bg-warning text-dark';
            break;
        case 'dropped':
            $badge_class = 'bg-danger';
            break;
    }

    return sprintf(
        '<span class="badge %s">%s</span>',
        esc_attr($badge_class),
        esc_html($status->name)
    );
}

/**
 * Display novel genres list
 */
function sekaiplus_novel_genres_list($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $genres = get_the_terms($post_id, 'genre');
    if (!$genres || is_wp_error($genres)) {
        return '';
    }

    $output = '<div class="genres-list">';
    foreach ($genres as $genre) {
        $output .= sprintf(
            '<a href="%s" class="badge bg-light text-dark text-decoration-none me-1">%s</a>',
            esc_url(get_term_link($genre)),
            esc_html($genre->name)
        );
    }
    $output .= '</div>';

    return $output;
}

/**
 * Display novel chapters list
 */
function sekaiplus_novel_chapters_list($novel_id = null, $limit = -1) {
    if (!$novel_id) {
        $novel_id = get_the_ID();
    }

    $chapters = get_posts(array(
        'post_type' => 'chapter',
        'post_parent' => $novel_id,
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    if (!$chapters) {
        return '<div class="text-muted text-center p-4">لا توجد فصول متاحة حالياً</div>';
    }

    $output = '<div class="list-group">';
    foreach ($chapters as $chapter) {
        $translator = get_the_author_meta('display_name', $chapter->post_author);
        $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
        
        $output .= sprintf(
            '<a href="%s" class="list-group-item list-group-item-action">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>%s</strong>
                        <small class="d-block text-muted">%s</small>
                    </div>
                    <div class="text-end">
                        <small class="text-muted d-block">%s</small>
                        <small class="text-muted">%s</small>
                    </div>
                </div>
            </a>',
            esc_url(get_permalink($chapter->ID)),
            sprintf(__('الفصل %d: %s', 'sekaiplus'), $chapter_number, $chapter->post_title),
            esc_html($translator),
            get_the_date('Y/m/d', $chapter->ID),
            sprintf(__('قراءات: %d', 'sekaiplus'), get_post_meta($chapter->ID, '_views_count', true) ?: 0)
        );
    }
    $output .= '</div>';

    return $output;
}

/**
 * Display novel meta information
 */
function sekaiplus_novel_meta($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $rating = sekaiplus_get_novel_rating($post_id);
    $chapters_count = sekaiplus_get_chapter_count($post_id);
    $views = get_post_meta($post_id, '_views_count', true) ?: 0;

    ob_start();
    ?>
    <div class="novel-meta">
        <div class="d-flex flex-wrap gap-3">
            <div>
                <i class="fas fa-star text-warning"></i>
                <span><?php echo number_format($rating, 1); ?></span>
            </div>
            <div>
                <i class="fas fa-book-open"></i>
                <span><?php echo $chapters_count; ?> فصل</span>
            </div>
            <div>
                <i class="fas fa-eye"></i>
                <span><?php echo number_format($views); ?> قراءة</span>
            </div>
            <div>
                <i class="fas fa-clock"></i>
                <span><?php echo get_the_date('Y/m/d', $post_id); ?></span>
            </div>
            <div>
                <i class="fas fa-user"></i>
                <span><?php echo get_the_author_meta('display_name', get_post_field('post_author', $post_id)); ?></span>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Display pagination
 */
function sekaiplus_pagination() {
    global $wp_query;

    if ($wp_query->max_num_pages <= 1) {
        return;
    }

    $big = 999999999;
    $pages = paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'type' => 'array',
        'prev_next' => true,
        'prev_text' => '<i class="fas fa-chevron-' . (is_rtl() ? 'left' : 'right') . '"></i>',
        'next_text' => '<i class="fas fa-chevron-' . (is_rtl() ? 'right' : 'left') . '"></i>',
    ));

    if (is_array($pages)) {
        echo '<nav aria-label="' . __('تصفح الصفحات', 'sekaiplus') . '">';
        echo '<ul class="pagination justify-content-center">';

        foreach ($pages as $page) {
            $active = strpos($page, 'current') !== false ? ' active' : '';
            echo '<li class="page-item' . $active . '">';
            echo str_replace(array('page-numbers', 'current'), array('page-link', ''), $page);
            echo '</li>';
        }

        echo '</ul>';
        echo '</nav>';
    }
}

/**
 * Get novel bookmark count
 */
function sekaiplus_get_bookmark_count($post_id) {
    return intval(get_post_meta($post_id, '_bookmark_count', true));
}

/**
 * Check if novel is in user's watchlist
 */
function sekaiplus_is_in_watchlist($post_id) {
    if (!is_user_logged_in()) {
        return false;
    }
    $user_id = get_current_user_id();
    $bookmarks = get_user_meta($user_id, '_novel_bookmarks', true);
    return is_array($bookmarks) && in_array($post_id, $bookmarks);
}

/**
 * Display novel type badge
 */
function sekaiplus_novel_type_badge($type) {
    $badge_class = 'bg-secondary';
    switch ($type) {
        case 'light':
            $badge_class = 'bg-info';
            $label = 'رواية خفيفة';
            break;
        case 'web':
            $badge_class = 'bg-primary';
            $label = 'رواية ويب';
            break;
        case 'novel':
            $badge_class = 'bg-success';
            $label = 'رواية';
            break;
        default:
            $label = $type;
    }
    
    return sprintf(
        '<span class="badge %s">%s</span>',
        esc_attr($badge_class),
        esc_html($label)
    );
}

/**
 * Get novel rating count
 */
function sekaiplus_get_rating_count($post_id) {
    return intval(get_post_meta($post_id, '_rating_count', true));
}

/**
 * Get novel volumes
 */
function sekaiplus_get_novel_volumes($post_id) {
    global $wpdb;
    $volumes = $wpdb->get_results($wpdb->prepare("
        SELECT DISTINCT meta_value as number
        FROM $wpdb->postmeta
        WHERE post_id IN (
            SELECT ID FROM $wpdb->posts 
            WHERE post_parent = %d AND post_type = 'chapter' AND post_status = 'publish'
        )
        AND meta_key = '_volume_number'
        ORDER BY meta_value + 0 ASC
    ", $post_id), ARRAY_A);

    return $volumes ?: array();
}

/**
 * Get volume chapters
 */
function sekaiplus_get_volume_chapters($novel_id, $volume_number) {
    $chapters = get_posts(array(
        'post_type' => 'chapter',
        'post_parent' => $novel_id,
        'posts_per_page' => -1,
        'meta_key' => '_volume_number',
        'meta_value' => $volume_number,
        'orderby' => 'meta_value_num',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'type' => 'NUMERIC'
            )
        )
    ));

    $formatted_chapters = array();
    foreach ($chapters as $chapter) {
        $chapter_data = array(
            'number' => get_post_meta($chapter->ID, '_chapter_number', true),
            'title' => $chapter->post_title,
            'translators' => array(array(
                'name' => get_the_author_meta('display_name', $chapter->post_author),
                'url' => get_permalink($chapter->ID)
            ))
        );
        
        // Check for alternative translations
        $alt_translations = get_post_meta($chapter->ID, '_alt_translations', true);
        if ($alt_translations) {
            foreach ($alt_translations as $translation) {
                $chapter_data['translators'][] = array(
                    'name' => get_the_author_meta('display_name', $translation['translator_id']),
                    'url' => $translation['url']
                );
            }
        }
        
        $formatted_chapters[] = $chapter_data;
    }

    return $formatted_chapters;
}

/**
 * Display breadcrumb navigation
 */
function sekaiplus_breadcrumb() {
    if (is_front_page()) {
        return;
    }

    echo '<nav aria-label="' . __('تصفح الموقع', 'sekaiplus') . '">';
    echo '<ol class="breadcrumb mb-0">';
    
    // Home
    echo '<li class="breadcrumb-item"><a href="' . esc_url(home_url('/')) . '"><i class="fas fa-home"></i></a></li>';

    if (is_singular('novel')) {
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_post_type_archive_link('novel')) . '">' . __('الروايات', 'sekaiplus') . '</a></li>';
        echo '<li class="breadcrumb-item active" aria-current="page">' . get_the_title() . '</li>';
    } elseif (is_singular('chapter')) {
        $parent_novel = get_post_parent();
        if ($parent_novel) {
            echo '<li class="breadcrumb-item"><a href="' . esc_url(get_post_type_archive_link('novel')) . '">' . __('الروايات', 'sekaiplus') . '</a></li>';
            echo '<li class="breadcrumb-item"><a href="' . esc_url(get_permalink($parent_novel)) . '">' . get_the_title($parent_novel) . '</a></li>';
        }
        echo '<li class="breadcrumb-item active" aria-current="page">' . get_the_title() . '</li>';
    } elseif (is_tax('genre')) {
        echo '<li class="breadcrumb-item"><a href="' . esc_url(get_post_type_archive_link('novel')) . '">' . __('الروايات', 'sekaiplus') . '</a></li>';
        echo '<li class="breadcrumb-item active" aria-current="page">' . single_term_title('', false) . '</li>';
    }

    echo '</ol>';
    echo '</nav>';
}
