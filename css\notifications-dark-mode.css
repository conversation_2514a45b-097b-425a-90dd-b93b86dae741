/* ===== دعم الوضع الداكن لصفحة الإشعارات ===== */

/* متغيرات الألوان للوضع الفاتح */
:root {
    --notifications-bg: #f8f9fa;
    --notifications-card-bg: #ffffff;
    --notifications-text: #212529;
    --notifications-text-muted: #6c757d;
    --notifications-border: #dee2e6;
    --notifications-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --notifications-unread-bg: rgba(13, 110, 253, 0.1);
    --notifications-hover-bg: #f8f9fa;
    --notifications-danger: #dc3545;
    --notifications-danger-hover: #c82333;
    --notifications-primary: #0d6efd;
    --notifications-success: #198754;
    --notifications-info: #0dcaf0;
}

/* متغيرات الألوان للوضع الداكن */
.dark-mode {
    --notifications-bg: #1a1a1a;
    --notifications-card-bg: #2d2d2d;
    --notifications-text: #f8f9fa;
    --notifications-text-muted: #adb5bd;
    --notifications-border: #404040;
    --notifications-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --notifications-unread-bg: rgba(102, 176, 255, 0.15);
    --notifications-hover-bg: #363636;
    --notifications-danger: #e74c3c;
    --notifications-danger-hover: #c0392b;
    --notifications-primary: #66b0ff;
    --notifications-success: #2ecc71;
    --notifications-info: #3498db;
}

/* ===== تطبيق الألوان على العناصر الأساسية ===== */

/* الحاوي الرئيسي */
.notifications-page .container {
    background-color: var(--notifications-bg);
    color: var(--notifications-text);
    transition: background-color 0.3s ease, color 0.3s ease;
    min-height: 100vh;
}

/* البطاقات */
.notifications-page .card {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    box-shadow: var(--notifications-shadow) !important;
    color: var(--notifications-text) !important;
    border-radius: 0.75rem;
}

.notifications-page .card-body {
    background-color: var(--notifications-card-bg) !important;
    color: var(--notifications-text) !important;
}

/* النصوص */
.notifications-page .text-muted {
    color: var(--notifications-text-muted) !important;
}

.notifications-page h1, 
.notifications-page h2, 
.notifications-page h3, 
.notifications-page h4, 
.notifications-page h5, 
.notifications-page h6 {
    color: var(--notifications-text) !important;
}

/* ===== عناصر قائمة الإشعارات ===== */

.notifications-page .list-group-item {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
    transition: all 0.3s ease;
}

.notifications-page .list-group-item:hover {
    background-color: var(--notifications-hover-bg) !important;
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.notifications-page .notification-item.unread {
    background-color: var(--notifications-unread-bg) !important;
    border-left: 4px solid var(--notifications-primary);
}

.notifications-page .notification-item.unread:hover {
    background-color: var(--notifications-unread-bg) !important;
    opacity: 0.9;
}

/* محتوى الإشعارات */
.notifications-page .notification-content {
    color: var(--notifications-text) !important;
    font-weight: 400;
    line-height: 1.6;
}

.notifications-page .notification-time {
    color: var(--notifications-text-muted) !important;
    font-size: 0.875rem;
}

/* ===== الأزرار ===== */

.notifications-page .btn-outline-danger {
    color: var(--notifications-danger) !important;
    border-color: var(--notifications-danger) !important;
    background-color: transparent;
    transition: all 0.3s ease;
}

.notifications-page .btn-outline-danger:hover {
    background-color: var(--notifications-danger) !important;
    border-color: var(--notifications-danger-hover) !important;
    color: #ffffff !important;
    transform: translateY(-1px);
}

.notifications-page .btn-link.text-danger {
    color: var(--notifications-danger) !important;
    transition: color 0.3s ease;
}

.notifications-page .btn-link.text-danger:hover {
    color: var(--notifications-danger-hover) !important;
}

.notifications-page .delete-notification {
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.notifications-page .delete-notification:hover {
    opacity: 1;
}

/* ===== الشارات ===== */

.notifications-page .badge {
    color: #ffffff !important;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notifications-page .badge.bg-primary {
    background-color: var(--notifications-primary) !important;
}

.notifications-page .badge.bg-success {
    background-color: var(--notifications-success) !important;
}

.notifications-page .badge.bg-info {
    background-color: var(--notifications-info) !important;
}

.notifications-page .notification-icon .badge {
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

/* ===== التنقل بين الصفحات ===== */

.notifications-page .pagination .page-link {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
    transition: all 0.3s ease;
}

.notifications-page .pagination .page-link:hover {
    background-color: var(--notifications-hover-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
    transform: translateY(-1px);
}

.notifications-page .pagination .page-item.active .page-link {
    background-color: var(--notifications-primary) !important;
    border-color: var(--notifications-primary) !important;
    color: #ffffff !important;
}

.notifications-page .pagination .page-item.disabled .page-link {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text-muted) !important;
}

/* ===== الروابط ===== */

.notifications-page a {
    color: var(--notifications-primary) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.notifications-page a:hover {
    color: var(--notifications-primary) !important;
    opacity: 0.8;
}

/* ===== تحسينات خاصة بالوضع الداكن ===== */

.dark-mode.notifications-page .container {
    background-color: var(--notifications-bg);
}

.dark-mode .notifications-page .card {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
}

.dark-mode .notifications-page .notification-icon .badge {
    box-shadow: 0 0 0 2px var(--notifications-card-bg), 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* ===== تأثيرات التركيز ===== */

.notifications-page .notification-item:focus {
    outline: 2px solid var(--notifications-primary);
    outline-offset: 2px;
    border-radius: 0.5rem;
}

.notifications-page .btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--notifications-primary), 0.25);
}

/* ===== حالة فارغة ===== */

.notifications-page .empty-state {
    background-color: var(--notifications-card-bg) !important;
    color: var(--notifications-text-muted) !important;
    border-radius: 1rem;
    padding: 3rem 2rem;
}

.notifications-page .empty-state i {
    color: var(--notifications-text-muted) !important;
    opacity: 0.5;
}

/* ===== تصميم متجاوب ===== */

@media (max-width: 768px) {
    .notifications-page .container {
        padding: 1rem;
    }
    
    .notifications-page .notification-item {
        padding: 1rem;
    }
    
    .notifications-page .notification-icon .badge {
        width: 2rem;
        height: 2rem;
        font-size: 0.875rem;
    }
    
    .notifications-page .btn-outline-danger {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* ===== تحسينات الأداء ===== */

.notifications-page * {
    will-change: auto;
}

.notifications-page .notification-item {
    contain: layout style;
}

/* ===== تأثيرات الحركة ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.notifications-page .notification-item {
    animation: fadeInUp 0.3s ease-out;
}

.notifications-page .notification-item:nth-child(even) {
    animation-delay: 0.1s;
}

.notifications-page .notification-item:nth-child(odd) {
    animation-delay: 0.05s;
}
