<?php
/**
 * The template for displaying taxonomy archives
 *
 * @package Sekaiplus
 */

// تضمين ملف CSS المخصص
function sekaiplus_taxonomy_styles() {
    wp_enqueue_style('taxonomy-style', get_template_directory_uri() . '/css/taxonomy-style.css');
}
add_action('wp_enqueue_scripts', 'sekaiplus_taxonomy_styles');

get_header();

// Get the current taxonomy term
$term = get_queried_object();
$taxonomy = $term->taxonomy;
$term_id = $term->term_id;

// Set a default title based on taxonomy type
$taxonomy_title = 'تصنيف';
$taxonomy_icon = 'fa-tag';
$taxonomy_color = '#3498db';

if ($taxonomy === 'novel_author') {
    $taxonomy_title = 'أعمال المؤلف';
    $taxonomy_icon = 'fa-pen-fancy';
    $taxonomy_color = '#e74c3c';
} elseif ($taxonomy === 'novel_illustrator') {
    $taxonomy_title = 'أعمال الرسام';
    $taxonomy_icon = 'fa-paint-brush';
    $taxonomy_color = '#9b59b6';
} elseif ($taxonomy === 'novel_genre') {
    $taxonomy_title = 'روايات تصنيف';
    $taxonomy_icon = 'fa-bookmark';
    $taxonomy_color = '#2ecc71';
} elseif ($taxonomy === 'novel_tag') {
    $taxonomy_title = 'روايات وسم';
    $taxonomy_icon = 'fa-hashtag';
    $taxonomy_color = '#f39c12';
}

// Get total novels count
$total_novels = $wp_query->found_posts;

// Get related terms
$related_terms = array();
if ($taxonomy === 'novel_genre') {
    $related_terms = get_terms(array(
        'taxonomy' => 'novel_genre',
        'hide_empty' => true,
        'number' => 10,
        'exclude' => array($term_id)
    ));
} elseif ($taxonomy === 'novel_tag') {
    $related_terms = get_terms(array(
        'taxonomy' => 'novel_tag',
        'hide_empty' => true,
        'number' => 10,
        'exclude' => array($term_id)
    ));
}
?>

<div class="taxonomy-hero" style="--taxonomy-color: <?php echo esc_attr($taxonomy_color); ?>">
    <div class="container">
        <div class="taxonomy-hero-content">
            <div class="taxonomy-icon">
                <i class="fas <?php echo $taxonomy_icon; ?>"></i>
            </div>
            <h1 class="taxonomy-title">
                <?php echo $taxonomy_title; ?>: <span><?php echo esc_html($term->name); ?></span>
            </h1>
            <?php if (!empty($term->description)) : ?>
                <div class="taxonomy-description">
                    <?php echo wpautop($term->description); ?>
                </div>
            <?php endif; ?>
            <div class="taxonomy-meta">
                <div class="taxonomy-count">
                    <i class="fas fa-book"></i>
                    <span><?php echo $total_novels; ?> رواية</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container taxonomy-container">
    <div class="row">
        <div class="col-lg-9">
            <?php
            // Query posts belonging to this taxonomy term
            $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
            $args = array(
                'post_type' => 'novel',
                'posts_per_page' => 12,
                'paged' => $paged,
                'tax_query' => array(
                    array(
                        'taxonomy' => $taxonomy,
                        'field' => 'term_id',
                        'terms' => $term_id,
                    ),
                ),
            );

            $query = new WP_Query($args);

            if ($query->have_posts()) :
            ?>
                <div class="taxonomy-filter">
                    <div class="filter-header">
                        <h3 class="filter-title">
                            <i class="fas fa-filter"></i>
                            الروايات
                        </h3>
                        <div class="filter-count">
                            <?php echo $query->found_posts; ?> رواية
                        </div>
                    </div>
                </div>

                <div class="novels-grid">
                    <?php
                    while ($query->have_posts()) : $query->the_post();
                        $rating = sekaiplus_get_novel_rating(get_the_ID());
                        $genres = get_the_terms(get_the_ID(), 'novel_genre');
                        $chapter_count = sekaiplus_get_chapter_count(get_the_ID());
                    ?>
                        <div class="novel-item">
                            <article class="novel-card">
                                <div class="novel-card-inner">
                                    <div class="novel-cover">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('class' => 'novel-cover-img')); ?>
                                        <?php else : ?>
                                            <div class="novel-cover-placeholder">
                                                <i class="fas fa-book"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="novel-overlay">
                                        <div class="novel-info">
                                            <?php
                                            // الحصول على العنوان الأصلي والعنوان العربي
                                            $title = get_the_title();
                                            $arabic_title = '';

                                            // محاولة الحصول على العنوان العربي من الحقول المخصصة إذا كانت موجودة
                                            $arabic_title = get_post_meta(get_the_ID(), '_novel_arabic_title', true);
                                            if (empty($arabic_title)) {
                                                // إذا لم يكن هناك عنوان عربي مخصص، نستخدم العنوان الأصلي
                                                $arabic_title = $title;
                                            }
                                            ?>

                                            <h3 class="novel-title">
                                                <a href="<?php the_permalink(); ?>">
                                                    <?php echo $title; ?>
                                                </a>
                                            </h3>

                                            <?php if ($arabic_title && $arabic_title !== $title) : ?>
                                            <div class="novel-arabic-title">
                                                <?php echo $arabic_title; ?>
                                            </div>
                                            <?php endif; ?>

                                            <div class="novel-meta">
                                                <div class="novel-chapters">
                                                    <i class="fas fa-book-open"></i>
                                                    <span><?php echo $chapter_count; ?> فصل</span>
                                                </div>
                                                <div class="novel-date">
                                                    <i class="fas fa-calendar-alt"></i>
                                                    <span><?php echo get_the_date('Y/m/d'); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- شارة زرقاء في الزاوية -->
                                    <div class="novel-badge">
                                        <i class="fas fa-bookmark"></i>
                                    </div>
                                </div>
                            </article>
                        </div>
                    <?php endwhile; ?>
                </div>

                <div class="taxonomy-pagination">
                    <?php
                    echo paginate_links(array(
                        'base' => str_replace(*********, '%#%', esc_url(get_pagenum_link(*********))),
                        'format' => '?paged=%#%',
                        'current' => max(1, get_query_var('paged')),
                        'total' => $query->max_num_pages,
                        'prev_text' => '<i class="fas fa-chevron-right"></i>',
                        'next_text' => '<i class="fas fa-chevron-left"></i>',
                        'type' => 'list',
                        'end_size' => 1,
                        'mid_size' => 1
                    ));
                    ?>
                </div>

            <?php else : ?>
                <div class="no-novels">
                    <div class="no-novels-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="no-novels-title">لا توجد روايات</h3>
                    <p class="no-novels-message">لا توجد روايات مرتبطة بـ "<?php echo esc_html($term->name); ?>" حالياً.</p>
                </div>
            <?php endif; ?>

            <?php wp_reset_postdata(); ?>
        </div>

        <div class="col-lg-3">
            <div class="taxonomy-sidebar">
                <?php if (!empty($related_terms)) : ?>
                <div class="sidebar-widget related-terms">
                    <h3 class="widget-title">
                        <i class="fas fa-tags"></i>
                        تصنيفات ذات صلة
                    </h3>
                    <div class="widget-content">
                        <ul class="related-terms-list">
                            <?php foreach ($related_terms as $related_term) : ?>
                                <li>
                                    <a href="<?php echo get_term_link($related_term); ?>">
                                        <span class="term-name"><?php echo $related_term->name; ?></span>
                                        <span class="term-count"><?php echo $related_term->count; ?></span>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
                <?php endif; ?>

                <div class="sidebar-widget popular-novels">
                    <h3 class="widget-title">
                        <i class="fas fa-fire"></i>
                        روايات شائعة
                    </h3>
                    <div class="widget-content">
                        <?php
                        $popular_novels = new WP_Query(array(
                            'post_type' => 'novel',
                            'posts_per_page' => 5,
                            'meta_key' => '_novel_views',
                            'orderby' => 'meta_value_num',
                            'order' => 'DESC',
                            'tax_query' => array(
                                array(
                                    'taxonomy' => $taxonomy,
                                    'field' => 'term_id',
                                    'terms' => $term_id,
                                ),
                            ),
                        ));

                        if ($popular_novels->have_posts()) :
                            while ($popular_novels->have_posts()) : $popular_novels->the_post();
                                $rating = sekaiplus_get_novel_rating(get_the_ID());
                        ?>
                            <div class="popular-novel-item">
                                <a href="<?php the_permalink(); ?>" class="popular-novel-link">
                                    <div class="popular-novel-cover">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('thumbnail', array('class' => 'popular-novel-img')); ?>
                                        <?php else : ?>
                                            <div class="popular-novel-placeholder">
                                                <i class="fas fa-book"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="popular-novel-info">
                                        <h4 class="popular-novel-title"><?php the_title(); ?></h4>
                                        <?php if ($rating > 0) : ?>
                                        <div class="popular-novel-rating">
                                            <i class="fas fa-star"></i>
                                            <span><?php echo number_format($rating, 1); ?></span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            </div>
                        <?php
                            endwhile;
                            wp_reset_postdata();
                        else :
                        ?>
                            <div class="no-popular-novels">
                                <p>لا توجد روايات شائعة حالياً.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
