/**
Theme Name: Sekaiplus
Theme URI: https://sekaiplus.com/
Author: Sekaiplus Team
Author URI: https://sekaiplus.com/
Description: A novel translation theme with RTL support
Version: 1.0.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: sekaiplus
*/

/* Font Loading */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@400;500;700&display=swap');

/* Variables */
:root {
    --primary-color: #0056b3;
    --primary-rgb: 0, 86, 179;
    --secondary-color: #6c757d;
    --bg-color: #ffffff;
    --text-color: #333333;
    --header-bg: #ffffff;
    --card-bg: #ffffff;
    --border-color: #dee2e6;
    --icon-color: #333333;
    --link-color: #0056b3;
    --hover-color: #004085;
    --dropdown-bg: #ffffff;
    --dropdown-text: #333333;
    --dropdown-hover: #f8f9fa;
    --button-bg: #f8f9fa;
    --button-text: #333333;
    --font-family: 'Noto Kufi Arabic', 'Tajawal', -apple-system, BlinkMacSystemFont, sans-serif;
    --card-border-radius: 15px;
    --transition-speed: 0.3s;
    --hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --header-height: 60px;
}

body {
    font-family: var(--font-family);
    direction: rtl;
    text-align: right;
    background-color: var(--bg-color);
    line-height: 1.6;
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --header-bg: #2d2d2d;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    --icon-color: #ffffff;
    --link-color: #66b0ff;
    --hover-color: #99ccff;
    --dropdown-bg: #2d2d2d;
    --dropdown-text: #ffffff;
    --dropdown-hover: #404040;
    --button-bg: #404040;
    --button-text: #ffffff;
}

/* General Styles */
.site-header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
}

a {
    color: var(--link-color);
}

a:hover {
    color: var(--hover-color);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Tajawal', sans-serif;
    font-weight: 700;
}

.arabic-text {
    font-family: 'Noto Kufi Arabic', sans-serif;
    line-height: 2;
}

/* Layout */
.main-content {
    min-height: calc(100vh - 200px);
}

/* Novel Cards */
.novel-card {
    transition: transform 0.2s;
}

.novel-card:hover {
    transform: translateY(-5px);
}

.novel-cover {
    position: relative;
    padding-bottom: 140%;
    overflow: hidden;
}

.novel-cover img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Status Badges */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.status-ongoing {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.status-completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-hiatus {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-dropped {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

/* Chapter Content */
.chapter-content {
    font-size: 18px;
    line-height: 2;
    max-width: 800px;
    margin: 0 auto;
}

.chapter-navigation {
    position: sticky;
    top: 0;
    z-index: 1020;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

/* Quick Search */
.search-results {
    position: absolute;
    top: 100%;
    right: 0;
    left: 0;
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid rgba(0,0,0,.125);
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,.15);
}

.search-result:hover {
    background-color: var(--light-color);
}

/* Dark Mode */
.dark-mode {
    background-color: #1a1a1a;
    color: #f5f5f5;
}

.dark-mode .bg-white {
    background-color: #2d2d2d !important;
}

.dark-mode .bg-light {
    background-color: #363636 !important;
}

.dark-mode .text-dark {
    color: #f5f5f5 !important;
}

.dark-mode .text-muted {
    color: #adb5bd !important;
}

.dark-mode .border-bottom {
    border-color: #404040 !important;
}

.dark-mode .novel-card {
    background-color: #2d2d2d;
}

.dark-mode .search-results {
    background-color: #2d2d2d;
    border-color: #404040;
}

.dark-mode .search-result:hover {
    background-color: #363636;
}

/* Loading Animation */
.loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid currentColor;
    border-radius: 50%;
    border-right-color: transparent;
    display: inline-block;
    animation: spinner-border .75s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .novel-card {
        max-width: 200px;
        margin: 0 auto;
    }

    .chapter-content {
        padding: 1rem;
    }
}

/* RTL Specific */
[dir="rtl"] .me-1 {
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
}

[dir="rtl"] .me-2 {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

[dir="rtl"] .me-3 {
    margin-right: 0 !important;
    margin-left: 1rem !important;
}

[dir="rtl"] .ms-1 {
    margin-left: 0 !important;
    margin-right: 0.25rem !important;
}

[dir="rtl"] .ms-2 {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
}

[dir="rtl"] .ms-3 {
    margin-left: 0 !important;
    margin-right: 1rem !important;
}

/* Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 0;
    background: var(--dark-color);
    color: white;
    padding: 8px;
    z-index: 100;
}

.skip-link:focus {
    top: 0;
}

.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal !important;
}

/* Print Styles */
@media print {
    .chapter-navigation,
    .site-header,
    .site-footer {
        display: none;
    }

    .chapter-content {
        font-size: 12pt;
        line-height: 1.5;
    }
}

/* Genre Tags */
.genre-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    margin: 0.25rem;
    border-radius: 1rem;
    background-color: rgba(0,0,0,.05);
    color: inherit;
    text-decoration: none;
    transition: background-color 0.2s;
}

.genre-tag:hover {
    background-color: rgba(0,0,0,.1);
    color: inherit;
}

.dark-mode .genre-tag {
    background-color: rgba(255,255,255,.1);
}

.dark-mode .genre-tag:hover {
    background-color: rgba(255,255,255,.15);
}

/* Comments */
.comment-list {
    list-style: none;
    padding: 0;
}

.comment {
    margin-bottom: 1.5rem;
}

.comment-meta {
    margin-bottom: 0.5rem;
}

.comment-author {
    font-weight: bold;
}

.comment-metadata {
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.comment-content {
    margin-bottom: 0.5rem;
}

.comment-reply-link {
    font-size: 0.875rem;
}

/* Pagination */
.pagination .page-numbers {
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    border-radius: 0.25rem;
    background-color: #fff;
    color: var(--primary-color);
    text-decoration: none;
}

/* Hero Section */
.hero-section {
    background: var(--primary-gradient);
    min-height: 400px;
    border-radius: 0 0 3rem 3rem;
    position: relative;
    overflow: hidden;
    margin-top: -1.5rem;
    margin-bottom: 3rem;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .row {
    position: relative;
    z-index: 2;
}

.featured-novels-carousel {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    transform: rotate(-5deg) translateY(-20px);
}

.featured-novel-card img {
    transition: transform 0.3s ease;
}

.featured-novel-card:hover img {
    transform: translateY(-10px);
}

/* Quick Search Results */
.quick-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--card-bg);
    border-radius: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    display: none;
    z-index: 1000;
    border: 1px solid var(--border-color);
}

.search-result-item {
    border-color: var(--border-color) !important;
}

.search-result-item:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* Novel Cards */
.novel-card {
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    background-color: var(--card-bg);
    border-color: var(--border-color);
    border-radius: var(--card-border-radius);
    overflow: hidden;
}

/* Rating Stars */
.rating-stars {
    cursor: pointer;
    user-select: none;
}

.rating-stars i {
    margin: 0 1px;
    transition: transform 0.2s, color 0.2s;
}

.rating-stars i:hover {
    transform: scale(1.2);
}

.rating-stars i.fas.text-warning {
    color: #ffc107 !important;
}

.rating-stars i.text-muted {
    color: #dee2e6 !important;
}

/* Volume Sections */
.volume-title {
    cursor: pointer;
    position: relative;
    padding: 1rem;
    background-color: rgba(0,0,0,.03);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    transition: background-color 0.2s;
}

.volume-title:hover {
    background-color: rgba(0,0,0,.05);
}

.volume-title::after {
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    transition: transform 0.3s;
}

.volume-title.collapsed::after {
    transform: translateY(-50%) rotate(-90deg);
}

.chapter-list {
    padding: 0.5rem;
}

.chapter-item {
    background-color: var(--card-bg);
    transition: background-color 0.2s;
}

.chapter-item:hover {
    background-color: rgba(0,0,0,.02);
}

.translator-select-wrapper select {
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    background-color: var(--card-bg);
}

/* Dark Mode Overrides for Novel Elements */
[data-bs-theme="dark"] .volume-title {
    background-color: rgba(255,255,255,.05);
}

[data-bs-theme="dark"] .volume-title:hover {
    background-color: rgba(255,255,255,.08);
}

[data-bs-theme="dark"] .chapter-item:hover {
    background-color: rgba(255,255,255,.03);
}

[data-bs-theme="dark"] .translator-select-wrapper select {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-color: var(--border-color);
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow) !important;
}

.card-img-top {
    height: 250px;
    object-fit: cover;
}

/* Stats Section */
.stat-card {
    transition: transform 0.3s ease;
    background-color: var(--card-bg) !important;
    border-color: var(--border-color);
    border-radius: var(--card-border-radius);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .text-muted {
    color: var(--text-muted) !important;
}

/* Latest Chapters */
.chapter-row {
    transition: all 0.2s ease;
    background: var(--card-bg);
}

.chapter-row:hover {
    transform: translateX(5px);
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
}

.chapter-novel-cover {
    width: 50px;
    height: 70px;
    object-fit: cover;
}

/* Dark Mode Overrides */
[data-bs-theme="dark"] {
    --card-bg: #2b3035;
    --text-color: #e9ecef;
    --text-muted: #adb5bd;
    --border-color: rgba(255, 255, 255, 0.125);
    --hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
}

[data-bs-theme="dark"] .bg-white {
    background-color: rgb(26 26 26) !important;
}

[data-bs-theme="dark"] .bg-light {
    background-color: rgb(33 37 41) !important;
}

/* Notifications */
.notifications-menu {
    max-width: 400px;
    min-width: 300px;
    max-height: 500px;
    overflow-y: auto;
    padding: 0;
}

.notifications-list {
    max-height: 450px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: rgba(0, 123, 255, 0.05);
    text-decoration: none;
}

.notification-item.unread {
    background-color: var(--light-color);
}

/* Translator Notes */
.translator-note {
    text-decoration: underline;
    text-decoration-style: dotted;
    text-decoration-color: var(--primary-color);
    background: rgba(13, 110, 253, 0.1);
    cursor: help;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Novel Details Page */
.novel-cover-wrapper {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}

.novel-cover-wrapper img {
    width: 100%;
    aspect-ratio: 1/1.4285;
    object-fit: cover;
    object-position: center;
    border-radius: var(--card-border-radius);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes moveBackground {
    0% { background-position: 0 0; }
    100% { background-position: 100% 100%; }
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-section {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .card-img-top {
        height: 150px;
    }
    
    .notification-content {
        max-width: calc(100% - 2.5rem);
    }
    
    .notifications-menu {
        position: fixed !important;
        top: 56px !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        height: calc(100vh - 56px) !important;
        max-height: none !important;
        border-radius: 0 !important;
        border: none !important;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 1.5rem;
    }
}

/* Navbar Styles */
.navbar {
    height: var(--header-height);
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    padding: 0 0.5rem;
    height: 100%;
}

.navbar-brand {
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
}

.navbar-brand img {
    max-height: 35px;
    width: auto;
}

/* Navigation Menu Styles */
.navbar-nav-center {
    margin-left: auto;
    margin-right: 0.75rem;
}

.navbar-nav-center ul.nav {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;
    gap: 0.75rem;
}

.navbar-nav-center ul.nav li {
    margin: 0;
}

.navbar-nav-center ul.nav li a,
.navbar-nav-center .menu-item a {
    color: #333 !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.25rem;
    font-size: 0.95rem;
}

.navbar-nav-center ul.nav li a:hover,
.navbar-nav-center .menu-item a:hover {
    color: #666 !important;
}

.navbar-nav-center ul.nav li a i,
.navbar-nav-center .menu-item a i {
    margin-left: 4px;
    color: #666;
    font-size: 0.9em;
}

/* Right side actions */
.navbar-actions {
    margin-right: 0.5rem;
}

/* Actions */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-icons {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: none;
    color: var(--icon-color);
    transition: background-color var(--transition-speed);
}

.btn-icon:hover {
    background-color: var(--light-color);
}

.btn-icon i {
    font-size: 1.1rem;
}

/* Auth Buttons */
.navbar .btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar .btn-light {
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
}

.navbar .btn-primary {
    background-color: var(--primary-color);
    border: none;
    color: white;
}

/* Mobile */
@media (max-width: 991.98px) {
    .navbar-nav-center {
        display: none;
    }
    
    .navbar-brand {
        margin: 0 auto;
    }
    
    .navbar-actions {
        flex: 1;
        justify-content: flex-end;
    }
}

/* Dark Mode Toggle */
#darkModeToggle {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    position: relative;
    color: var(--icon-color);
}

#darkModeToggle i {
    font-size: 1.2rem;
    color: var(--icon-color);
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.3s ease;
}

#darkModeToggle .dark-icon {
    opacity: 0;
}

#darkModeToggle .light-icon {
    opacity: 1;
}

body.dark-mode #darkModeToggle .dark-icon {
    opacity: 1;
}

body.dark-mode #darkModeToggle .light-icon {
    opacity: 0;
}

/* Ensure icons are visible */
.nav-icons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-icons .btn-icon {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.nav-icons .btn-icon:hover {
    background-color: var(--dropdown-hover);
}

/* Notifications Dropdown */
.notifications-dropdown {
    width: 320px;
    padding: 0;
    max-height: 400px;
}

.notifications-dropdown .dropdown-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s;
}

.notification-item:hover {
    background-color: var(--hover-bg);
}

.notification-item.unread {
    background-color: var(--unread-bg);
}

.notification-icon {
    margin-left: 1rem;
    color: var(--primary-color);
}

.notification-content {
    flex: 1;
}

.notification-text {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.notification-time {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* User Menu */
.user-menu-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem;
}

.user-menu-button img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.dropdown-menu {
    min-width: 200px;
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    color: var(--text-color);
}

.dropdown-item:hover {
    background-color: var(--hover-bg);
}

.dropdown-item i {
    width: 1.5rem;
    text-align: center;
    color: var(--text-muted);
}

/* Nav Icons */
.nav-icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: transparent;
    color: var(--icon-color);
    border: none;
    transition: background-color 0.2s;
}

.btn-icon:hover {
    background-color: var(--hover-bg);
}

.btn-icon i {
    font-size: 1.2rem;
}

/* Dropdown Styles */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border: 1px solid var(--border-color);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.2);
}

.dropdown-menu .dropdown-item {
    color: var(--dropdown-text);
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.dropdown-menu .dropdown-item:hover,
.dropdown-menu .dropdown-item:focus {
    background-color: var(--dropdown-hover);
    color: var(--dropdown-text);
}

.dropdown-menu .dropdown-header {
    color: var(--dropdown-text);
    background-color: var(--dropdown-bg);
    border-bottom: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    font-weight: bold;
}

.dropdown-menu .dropdown-divider {
    border-color: var(--border-color);
    margin: 0.5rem 0;
}

/* User Menu Dropdown */
.user-menu-button {
    padding: 0 !important;
    overflow: hidden;
}

.user-menu-button img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* Notifications Dropdown */
.notifications-dropdown {
    min-width: 300px;
    padding: 0;
}

.notifications-list {
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: var(--dropdown-hover);
}

.notification-item.unread {
    background-color: rgba(var(--primary-rgb), 0.1);
}

.notification-content {
    color: var(--dropdown-text);
}

.notification-text {
    margin: 0;
    font-size: 0.9rem;
    color: var(--dropdown-text);
}

.notification-time {
    display: block;
    color: var(--dropdown-text);
    opacity: 0.7;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.no-notifications {
    padding: 1rem;
    text-align: center;
    color: var(--dropdown-text);
}

/* Dark Mode Specific Dropdown Styles */
body.dark-mode .dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
}

body.dark-mode .dropdown-menu .dropdown-item {
    color: var(--dropdown-text);
}

body.dark-mode .dropdown-menu .dropdown-item:hover,
body.dark-mode .dropdown-menu .dropdown-item:focus {
    background-color: var(--dropdown-hover);
}

body.dark-mode .notification-item.unread {
    background-color: rgba(102, 176, 255, 0.1);
}

/* Dark mode styles for profile page */
.dark-mode .profile-container {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

.dark-mode .profile-content {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

.dark-mode .user-details {
    background-color: var(--dark-secondary-bg);
    color: var(--dark-text);
}

.dark-mode .detail-item {
    border-color: var(--dark-border);
}

.dark-mode .detail-item .label {
    color: var(--dark-text-muted);
}

.dark-mode .detail-item .value {
    color: var(--dark-text);
}

.dark-mode .profile-header {
    background-color: var(--dark-secondary-bg);
}

.dark-mode .profile-tabs nav {
    background-color: var(--dark-secondary-bg);
}

.dark-mode .profile-tabs .nav-link {
    color: var(--dark-text);
}

.dark-mode .profile-tabs .nav-link.active {
    background-color: var(--dark-accent);
    color: var(--dark-text);
}

.dark-mode .profile-card {
    background-color: var(--dark-secondary-bg);
    border-color: var(--dark-border);
}

.dark-mode .tab-content {
    background-color: var(--dark-secondary-bg);
    border-color: var(--dark-border);
}

.dark-mode .profile-bio {
    color: var(--dark-text-muted);
}

.dark-mode .tab-pane {
    background-color: var(--dark-secondary-bg);
}

.dark-mode .user-post {
    border-color: var(--dark-border);
}

.dark-mode .user-post a {
    color: var(--dark-link);
}

.dark-mode .user-post .post-meta {
    color: var(--dark-text-muted);
}

.dark-mode .edit-profile-btn {
    background-color: var(--dark-accent);
    color: var(--dark-text);
    border-color: var(--dark-border);
}

.dark-mode .edit-profile-btn:hover {
    background-color: var(--dark-accent-hover);
}

/* ... existing code ... */

/* Fix for profile picture modal text visibility in dark mode */
.dark-mode .gravatar-modal {
    color: #333; /* Dark text color that works in both light and dark modes */
}

.dark-mode .gravatar-modal input[type="email"] {
    color: #333;
    background-color: #fff;
}

.dark-mode .gravatar-modal a {
    color: #0073aa; /* WordPress default link color */
}
.dark-mode .modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: var(--bs-modal-color);
    pointer-events: auto;
    background-color: #161a1b;
    background-clip: padding-box;
    border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
    border-radius: var(--bs-modal-border-radius);
    outline: 0;
}
/* Font Size Controls */
.font-controls {
    --btn-size: 36px;
}

.font-control-btn {
    width: var(--btn-size);
    height: var(--btn-size);
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--button-bg);
    border: 1px solid var(--border-color);
    color: var(--button-text);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.font-control-btn:hover {
    background-color: var(--dropdown-hover);
    color: var(--button-text);
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.font-control-btn i {
    font-size: 14px;
    line-height: 1;
}

/* Footer Styles */
.footer {
    background-color: var(--header-bg);
    border-top: 1px solid var(--border-color);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.footer-text {
    color: var(--text-color);
    opacity: 0.85;
}

.footer .nav-link {
    color: var(--text-color);
    opacity: 0.85;
    transition: opacity 0.3s ease;
    padding: 0.5rem 1rem;
}

.footer .nav-link:hover {
    opacity: 1;
    color: var(--text-color);
}

/* Loading Overlay */
#loadingOverlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 9999;
}

#loadingOverlay .text-white {
    color: var(--text-color) !important;
}

/* Latest Updates Section */
.latest-updates {
    background-color: var(--bg-color);
    color: var(--text-color);
    padding: 2rem 0;
    transition: background-color 0.3s ease;
}

/* Table Styles */
.table {
    background-color: var(--bg-color);
    color: var(--text-color);
    border-color: var(--border-color);
}

.table th,
.table td {
    color: var(--text-color);
    border-color: var(--border-color);
    background-color: var(--bg-color);
}

.table thead th {
    background-color: var(--header-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.table tbody tr:hover {
    background-color: var(--dropdown-hover);
}

/* Load More Button */
.btn-load-more {
    background-color: var(--button-bg);
    color: var(--button-text);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.btn-load-more:hover {
    background-color: var(--dropdown-hover);
    color: var(--button-text);
    transform: translateY(-2px);
}

/* Section Headers */
.section-header {
    color: var(--text-color);
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-header h2 {
    color: var(--text-color);
    margin: 0;
}

/* Card Styles in Dark Mode */
.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.card-header {
    background-color: var(--header-bg);
    border-bottom-color: var(--border-color);
    color: var(--text-color);
}

.card-body {
    color: var(--text-color);
}

/* Icon Colors */
.section-icon {
    color: var(--primary-color);
}

/* Show More Button */
.btn-show-more {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    background-color: var(--button-bg);
    color: var(--button-text);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    transition: all 0.3s ease;
    text-decoration: none;
    margin-top: 1rem;
}

.btn-show-more:hover {
    background-color: var(--dropdown-hover);
    color: var(--button-text);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Latest Items List */
.latest-items {
    background-color: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
}

.latest-items .header {
    background-color: var(--header-bg);
    color: var(--text-color);
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.latest-items .content {
    padding: 1rem;
}

.latest-items table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.latest-items th,
.latest-items td {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    background-color: var(--bg-color);
}

.latest-items th {
    font-weight: 600;
    text-align: right;
}

.latest-items tr:last-child td {
    border-bottom: none;
}

.latest-items tr:hover td {
    background-color: var(--dropdown-hover);
}

/* Fix text colors */
.text-dark {
    color: var(--text-color) !important;
}

.bg-dark {
    background-color: var(--header-bg) !important;
}

/* Theme Switch */
.theme-switch {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-switch i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.dark-theme .theme-switch i {
    color: #ffffff;
}

/* Ensure icon is visible in both themes */
body:not(.dark-theme) .theme-switch i {
    color: #333333;
}

.theme-switch:hover i {
    transform: scale(1.1);
}

/* Dark Mode Toggle Button */
.btn-icon#darkModeToggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-bg);
    color: var(--button-text);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.btn-icon#darkModeToggle:hover {
    background: var(--dropdown-hover);
    transform: scale(1.1);
}

.btn-icon#darkModeToggle .dark-icon,
.btn-icon#darkModeToggle .light-icon {
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.dark-mode .btn-icon#darkModeToggle {
    background: var(--dropdown-bg);
    color: var(--text-color);
}

.dark-mode .btn-icon#darkModeToggle:hover {
    background: var(--dropdown-hover);
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    color: var(--bs-modal-color);
    pointer-events: auto;
    background-color: var(--bs-modal-bg);
    background-clip: padding-box;
    border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
    border-radius: var(--bs-modal-border-radius);
    outline: 0;
}