<?php
/**
 * Template Name: الإشعارات
 */

// التحويل إذا لم يكن المستخدم مسجلاً
if (!is_user_logged_in()) {
    wp_redirect(wp_login_url(get_permalink()));
    exit;
}

// تضمين ملف نظام الإشعارات
require_once get_template_directory() . '/inc/load-notifications.php';

/**
 * دالة مساعدة للحصول على قيمة آمنة من كائن الإشعار
 */
function get_safe_notification_value($notification, $key, $default = '') {
    return isset($notification->$key) && $notification->$key !== null ? $notification->$key : $default;
}

$user_id = get_current_user_id();
$paged = get_query_var('paged') ? get_query_var('paged') : 1;
$items_per_page = 20;
$offset = ($paged - 1) * $items_per_page;

// الحصول على الإشعارات
$notifications = sekai_get_user_notifications($user_id, $items_per_page, $offset);

// الحصول على إجمالي عدد الإشعارات
global $wpdb;
$table_name = $wpdb->prefix . 'sekaiplus_user_notifications'; // تصحيح اسم الجدول
$total_notifications = $wpdb->get_var($wpdb->prepare(
    "SELECT COUNT(*) FROM $table_name WHERE user_id = %d",
    $user_id
));

$total_pages = ceil($total_notifications / $items_per_page);

// تحديث حالة الإشعارات إلى مقروءة
sekai_mark_all_notifications_as_read($user_id);

get_header();
?>

<!-- تحميل CSS الخاص بالإشعارات -->
<link rel="stylesheet" href="<?php echo get_template_directory_uri(); ?>/css/notifications-dark-mode.css?v=<?php echo time(); ?>">

<body class="notifications-page">
<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">الإشعارات</h1>
        
        <?php if (!empty($notifications)) : ?>
        <button id="clearAllNotifications" class="btn btn-sm btn-outline-danger">
            <i class="fas fa-trash-alt"></i> حذف الكل
        </button>
        <?php endif; ?>
    </div>

    <?php if (empty($notifications)) : ?>
    <div class="card shadow-sm">
        <div class="card-body text-center py-5">
            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد إشعارات.</h5>
            <p class="text-muted mb-0">ستظهر هنا الإشعارات الخاصة بالروايات المفضلة والمحتوى الخاص بك.</p>
        </div>
    </div>
    <?php else : ?>
    <div class="card shadow-sm">
        <div class="list-group list-group-flush" id="notificationsList">
            <?php foreach ($notifications as $notification) : ?>
                <?php 
                $icon_class = 'fa-bell';
                $badge_class = 'bg-primary';
                
                // تحديد الأيقونة ولون الشارة بناءً على نوع الإشعار
                if ($notification->type === 'new_chapter') {
                    $icon_class = 'fa-book-open';
                    $badge_class = 'bg-success';
                } elseif ($notification->type === 'approved_content') {
                    $icon_class = 'fa-check-circle';
                    $badge_class = 'bg-info';
                }
                
                // تنسيق التاريخ
                $date = new DateTime($notification->created_at);
                $now = new DateTime();
                $interval = $date->diff($now);
                
                if ($interval->days == 0) {
                    if ($interval->h == 0) {
                        $time_ago = sprintf(_n('%d دقيقة', '%d دقائق', $interval->i), $interval->i);
                    } else {
                        $time_ago = sprintf(_n('%d ساعة', '%d ساعات', $interval->h), $interval->h);
                    }
                } elseif ($interval->days < 7) {
                    $time_ago = sprintf(_n('%d يوم', '%d أيام', $interval->days), $interval->days);
                } else {
                    $time_ago = $date->format('Y-m-d');
                }
                
                // تحديد رابط الإشعار
                $notification_link = '#';
                $reference_id = get_safe_notification_value($notification, 'reference_id');
                $reference_type = get_safe_notification_value($notification, 'reference_type');

                if ($reference_id && $reference_type) {
                    if (in_array($reference_type, array('novel', 'chapter'))) {
                        $notification_link = get_permalink($reference_id);
                    }
                }
                ?>
                <a href="<?php echo esc_url($notification_link); ?>" class="list-group-item list-group-item-action notification-item <?php echo $notification->is_read ? '' : 'unread'; ?>" data-id="<?php echo $notification->id; ?>">
                    <div class="d-flex align-items-center">
                        <div class="notification-icon">
                            <span class="badge rounded-circle <?php echo $badge_class; ?>">
                                <i class="fas <?php echo $icon_class; ?>"></i>
                            </span>
                        </div>
                        <div class="ms-3 flex-grow-1">
                            <div class="notification-content"><?php
                                $content = get_safe_notification_value($notification, 'content', get_safe_notification_value($notification, 'message', 'إشعار'));
                                echo esc_html($content);
                            ?></div>
                            <div class="notification-time text-muted small">
                                <i class="far fa-clock"></i> <?php echo esc_html($time_ago); ?>
                            </div>
                        </div>
                        <div class="notification-actions">
                            <button class="btn btn-sm btn-link text-danger delete-notification" data-id="<?php echo $notification->id; ?>">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <?php if ($total_pages > 1) : ?>
    <div class="pagination-wrapper mt-4">
        <nav aria-label="تنقل بين الصفحات">
            <ul class="pagination justify-content-center">
                <?php
                $current_url = get_pagenum_link(1);
                $current_url = remove_query_arg('paged', $current_url);
                
                // زر الصفحة السابقة
                if ($paged > 1) :
                    $prev_url = add_query_arg('paged', $paged - 1, $current_url);
                ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo esc_url($prev_url); ?>" aria-label="السابق">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php
                // أرقام الصفحات
                $start_page = max(1, $paged - 2);
                $end_page = min($total_pages, $paged + 2);
                
                for ($i = $start_page; $i <= $end_page; $i++) :
                    $page_url = add_query_arg('paged', $i, $current_url);
                    $is_current = $i == $paged;
                ?>
                <li class="page-item <?php echo $is_current ? 'active' : ''; ?>">
                    <a class="page-link" href="<?php echo esc_url($page_url); ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php
                // زر الصفحة التالية
                if ($paged < $total_pages) :
                    $next_url = add_query_arg('paged', $paged + 1, $current_url);
                ?>
                <li class="page-item">
                    <a class="page-link" href="<?php echo esc_url($next_url); ?>" aria-label="التالي">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
    <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-icon .badge {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.notification-item {
    transition: all 0.2s ease;
    border-right: 3px solid transparent;
}

.notification-item.unread {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-right-color: var(--bs-primary);
}

.notification-item:hover {
    background-color: var(--bs-light);
}

.notification-actions {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.notification-item:hover .notification-actions {
    opacity: 1;
}
</style>

<script>
jQuery(document).ready(function($) {
    // حذف إشعار واحد
    $('.delete-notification').on('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const notificationId = $(this).data('id');
        const $notificationItem = $(this).closest('.notification-item');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'delete_notification',
                notification_id: notificationId,
                nonce: '<?php echo wp_create_nonce('sekai_ajax_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $notificationItem.slideUp(300, function() {
                        $(this).remove();
                        
                        // إذا لم يكن هناك إشعارات، قم بتحديث الصفحة
                        if ($('.notification-item').length === 0) {
                            location.reload();
                        }
                    });
                }
            }
        });
    });
    
    // حذف جميع الإشعارات
    $('#clearAllNotifications').on('click', function() {
        if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'delete_all_notifications',
                    nonce: '<?php echo wp_create_nonce('sekai_ajax_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    }
                }
            });
        }
    });
});
</script>

<style>
/* ===== دعم الوضع الداكن لصفحة الإشعارات ===== */

/* متغيرات الألوان للوضع الفاتح */
:root {
    --notifications-bg: #f8f9fa;
    --notifications-card-bg: #ffffff;
    --notifications-text: #212529;
    --notifications-text-muted: #6c757d;
    --notifications-border: #dee2e6;
    --notifications-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --notifications-unread-bg: rgba(13, 110, 253, 0.1);
    --notifications-hover-bg: #f8f9fa;
    --notifications-danger: #dc3545;
    --notifications-danger-hover: #c82333;
}

/* متغيرات الألوان للوضع الداكن */
.dark-mode {
    --notifications-bg: #1a1a1a;
    --notifications-card-bg: #2d2d2d;
    --notifications-text: #f8f9fa;
    --notifications-text-muted: #adb5bd;
    --notifications-border: #404040;
    --notifications-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --notifications-unread-bg: rgba(102, 176, 255, 0.15);
    --notifications-hover-bg: #363636;
    --notifications-danger: #e74c3c;
    --notifications-danger-hover: #c0392b;
}

/* تطبيق الألوان على العناصر */
.container {

    color: var(--notifications-text);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.card {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    box-shadow: var(--notifications-shadow) !important;
    color: var(--notifications-text) !important;
}

.card-body {
    background-color: var(--notifications-card-bg) !important;
    color: var(--notifications-text) !important;
}

.text-muted {
    color: var(--notifications-text-muted) !important;
}

.list-group-item {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
}

.list-group-item:hover {
    background-color: var(--notifications-hover-bg) !important;
}

.notification-item.unread {
    background-color: var(--notifications-unread-bg) !important;
}

.notification-item.unread:hover {
    background-color: var(--notifications-unread-bg) !important;
    opacity: 0.8;
}

.btn-outline-danger {
    color: var(--notifications-danger) !important;
    border-color: var(--notifications-danger) !important;
}

.btn-outline-danger:hover {
    background-color: var(--notifications-danger) !important;
    border-color: var(--notifications-danger-hover) !important;
    color: #ffffff !important;
}

.btn-link.text-danger {
    color: var(--notifications-danger) !important;
}

.btn-link.text-danger:hover {
    color: var(--notifications-danger-hover) !important;
}

/* تنسيق الشارات */
.badge {
    color: #ffffff !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
}

.badge.bg-success {
    background-color: #198754 !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
}

/* تنسيق التنقل بين الصفحات */
.pagination .page-link {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
}

.pagination .page-link:hover {
    background-color: var(--notifications-hover-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text) !important;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.pagination .page-item.disabled .page-link {
    background-color: var(--notifications-card-bg) !important;
    border-color: var(--notifications-border) !important;
    color: var(--notifications-text-muted) !important;
}

/* تحسينات إضافية للوضع الداكن */
.dark-mode .container {
    background-color: var(--notifications-bg);
}

.dark-mode h1, .dark-mode h2, .dark-mode h3, .dark-mode h4, .dark-mode h5, .dark-mode h6 {
    color: var(--notifications-text) !important;
}

.dark-mode .notification-content {
    color: var(--notifications-text) !important;
}

.dark-mode .notification-time {
    color: var(--notifications-text-muted) !important;
}

/* تأثيرات الانتقال */
.notification-item {
    transition: background-color 0.3s ease, opacity 0.3s ease;
}

.btn {
    transition: all 0.3s ease;
}

/* تحسين التباين في الوضع الداكن */
.dark-mode .notification-icon .badge {
    box-shadow: 0 0 0 2px var(--notifications-card-bg);
}

/* تنسيق خاص للأيقونات */
.dark-mode .fas, .dark-mode .far {
    color: inherit;
}

/* تحسين قابلية القراءة */
.dark-mode .small {
    color: var(--notifications-text-muted) !important;
}

/* تنسيق الروابط */
.dark-mode a {
    color: #66b0ff !important;
    text-decoration: none;
}

.dark-mode a:hover {
    color: #99ccff !important;
}

/* تحسين التباين للنص */
.dark-mode .notification-content {
    font-weight: 400;
    line-height: 1.5;
}

/* تأثير التركيز */
.dark-mode .notification-item:focus {
    outline: 2px solid #66b0ff;
    outline-offset: 2px;
}

/* تحسين الظلال في الوضع الداكن */
.dark-mode .card {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
}

/* تنسيق الأزرار في الوضع الداكن */
.dark-mode .btn-outline-danger {
    background-color: transparent;
}

.dark-mode .btn-outline-danger:hover {
    background-color: var(--notifications-danger) !important;
}
</style>

<?php get_footer(); ?>
