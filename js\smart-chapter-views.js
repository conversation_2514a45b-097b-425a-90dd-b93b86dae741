/**
 * نظام ذكي لتتبع مشاهدات الفصول
 * يوفر تحديثات فورية ومؤشرات بصرية للمشاهدات
 */

(function($) {
    'use strict';

    // متغيرات عامة
    let viewUpdateTimer = null;
    let hasViewBeenCounted = false;
    let readingStartTime = null;
    let isPageVisible = true;

    // إعدادات النظام
    const settings = {
        minReadingTime: 10000, // 10 ثوانٍ كحد أدنى للقراءة
        updateInterval: 30000,  // تحديث كل 30 ثانية
        scrollThreshold: 0.3,   // 30% من المحتوى يجب قراءته
        maxRetries: 3
    };

    /**
     * تهيئة النظام عند تحميل الصفحة
     */
    $(document).ready(function() {
        initializeViewTracking();
        setupVisibilityTracking();
        setupScrollTracking();
        setupPerformanceMonitoring();
    });

    /**
     * تهيئة تتبع المشاهدات
     */
    function initializeViewTracking() {
        const chapterElement = $('.stats-item.likes');
        if (!chapterElement.length) return;

        const chapterId = chapterElement.data('chapter-id');
        if (!chapterId) return;

        readingStartTime = Date.now();
        
        // تأخير تسجيل المشاهدة للتأكد من القراءة الفعلية
        setTimeout(() => {
            if (isValidView()) {
                recordChapterView(chapterId);
            }
        }, settings.minReadingTime);

        // تحديث دوري للإحصائيات
        setInterval(() => {
            updateViewStats(chapterId);
        }, settings.updateInterval);
    }

    /**
     * تتبع رؤية الصفحة
     */
    function setupVisibilityTracking() {
        if (typeof document.hidden !== "undefined") {
            document.addEventListener("visibilitychange", function() {
                isPageVisible = !document.hidden;
                
                if (isPageVisible && readingStartTime) {
                    // إعادة تعيين وقت البداية عند العودة للصفحة
                    readingStartTime = Date.now();
                }
            });
        }
    }

    /**
     * تتبع التمرير لقياس مستوى القراءة
     */
    function setupScrollTracking() {
        let maxScrollPercentage = 0;
        
        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const documentHeight = $(document).height();
            const windowHeight = $(window).height();
            const scrollPercentage = scrollTop / (documentHeight - windowHeight);
            
            maxScrollPercentage = Math.max(maxScrollPercentage, scrollPercentage);
            
            // حفظ نسبة القراءة في localStorage
            const chapterElement = $('.stats-item.likes');
            if (chapterElement.length) {
                const chapterId = chapterElement.data('chapter-id');
                if (chapterId) {
                    localStorage.setItem(`reading_progress_${chapterId}`, maxScrollPercentage);
                }
            }
        });
    }

    /**
     * مراقبة الأداء
     */
    function setupPerformanceMonitoring() {
        // تتبع وقت التحميل
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`Chapter loaded in ${loadTime.toFixed(2)}ms`);
        });

        // تتبع أخطاء JavaScript
        window.addEventListener('error', function(e) {
            console.error('Chapter view tracking error:', e.error);
        });
    }

    /**
     * التحقق من صحة المشاهدة
     */
    function isValidView() {
        if (!isPageVisible) return false;
        if (!readingStartTime) return false;
        
        const readingTime = Date.now() - readingStartTime;
        if (readingTime < settings.minReadingTime) return false;

        // التحقق من نسبة القراءة
        const chapterElement = $('.stats-item.likes');
        if (chapterElement.length) {
            const chapterId = chapterElement.data('chapter-id');
            const readingProgress = localStorage.getItem(`reading_progress_${chapterId}`) || 0;
            if (parseFloat(readingProgress) < settings.scrollThreshold) return false;
        }

        return true;
    }

    /**
     * تسجيل مشاهدة الفصل
     */
    function recordChapterView(chapterId, retryCount = 0) {
        if (hasViewBeenCounted) return;

        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'record_smart_chapter_view',
                chapter_id: chapterId,
                reading_time: Date.now() - readingStartTime,
                scroll_percentage: localStorage.getItem(`reading_progress_${chapterId}`) || 0,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    hasViewBeenCounted = true;
                    updateViewDisplay(response.data);
                    showViewConfirmation();
                } else {
                    console.warn('Failed to record view:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('View recording error:', error);
                
                // إعادة المحاولة
                if (retryCount < settings.maxRetries) {
                    setTimeout(() => {
                        recordChapterView(chapterId, retryCount + 1);
                    }, 2000 * (retryCount + 1));
                }
            }
        });
    }

    /**
     * تحديث عرض المشاهدات
     */
    function updateViewDisplay(data) {
        const viewsElement = $('.stats-item.views .stats-count');
        if (viewsElement.length && data.new_count) {
            // تأثير بصري للتحديث
            viewsElement.addClass('updating');
            
            setTimeout(() => {
                viewsElement.text(formatNumber(data.new_count));
                viewsElement.removeClass('updating');
            }, 300);
        }

        // تحديث مؤشر المشاهدات اليومية
        if (data.today_views) {
            updateTodayViewsIndicator(data.today_views);
        }
    }

    /**
     * تحديث إحصائيات المشاهدات
     */
    function updateViewStats(chapterId) {
        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_chapter_view_stats',
                chapter_id: chapterId,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateStatsDisplay(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Stats update error:', error);
            }
        });
    }

    /**
     * تحديث عرض الإحصائيات
     */
    function updateStatsDisplay(stats) {
        // تحديث العدد الإجمالي
        const totalElement = $('.stats-item.views .stats-count');
        if (totalElement.length) {
            totalElement.text(formatNumber(stats.total_views));
        }

        // تحديث مؤشر اليوم
        updateTodayViewsIndicator(stats.today_views);

        // تحديث إحصائيات المسؤولين
        updateAdminStats(stats);
    }

    /**
     * تحديث مؤشر مشاهدات اليوم
     */
    function updateTodayViewsIndicator(todayViews) {
        let indicator = $('.today-views-indicator');
        
        if (todayViews > 0) {
            if (!indicator.length) {
                indicator = $('<span class="today-views-indicator"></span>');
                $('.stats-item.views').append(indicator);
            }
            
            indicator.text(`+${todayViews}`)
                    .attr('title', `مشاهدات اليوم: ${todayViews}`)
                    .addClass('pulse-animation');
            
            setTimeout(() => {
                indicator.removeClass('pulse-animation');
            }, 1000);
        } else if (indicator.length) {
            indicator.fadeOut(300, function() {
                $(this).remove();
            });
        }
    }

    /**
     * تحديث إحصائيات المسؤولين
     */
    function updateAdminStats(stats) {
        const adminStats = $('.admin-view-stats');
        if (!adminStats.length) return;

        const statsHtml = `
            <strong>إحصائيات المشاهدات (للمسؤولين فقط):</strong><br>
            إجمالي المشاهدات: ${formatNumber(stats.total_views)}<br>
            المشاهدات الفريدة: ${formatNumber(stats.unique_views)}<br>
            مشاهدات اليوم: ${formatNumber(stats.today_views)}<br>
            مشاهدات الأسبوع: ${formatNumber(stats.week_views)}<br>
            مشاهدات الشهر: ${formatNumber(stats.month_views)}<br>
        `;
        
        adminStats.html(statsHtml);
    }

    /**
     * عرض تأكيد تسجيل المشاهدة
     */
    function showViewConfirmation() {
        // إضافة تأثير بصري خفيف لتأكيد التسجيل
        const viewsItem = $('.stats-item.views');
        viewsItem.addClass('view-recorded');
        
        setTimeout(() => {
            viewsItem.removeClass('view-recorded');
        }, 2000);
    }

    /**
     * تنسيق الأرقام
     */
    function formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    // إضافة CSS للتأثيرات البصرية
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .stats-count.updating {
                animation: countUpdate 0.6s ease-in-out;
            }
            
            @keyframes countUpdate {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); color: var(--chapter-primary); }
                100% { transform: scale(1); }
            }
            
            .today-views-indicator.pulse-animation {
                animation: indicatorPulse 1s ease-in-out;
            }
            
            @keyframes indicatorPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }
            
            .stats-item.views.view-recorded {
                box-shadow: 0 0 10px rgba(var(--bs-success-rgb), 0.3);
                transition: box-shadow 0.3s ease;
            }
        `)
        .appendTo('head');

})(jQuery);
