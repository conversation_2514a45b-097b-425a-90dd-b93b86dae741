<?php
get_header();

while (have_posts()) : the_post();
    $novel_id = get_the_ID();
    $user_id = get_current_user_id();

    // Get novel metadata
    $japanese_title = get_post_meta($novel_id, '_japanese_title', true);
    $romaji_title = get_post_meta($novel_id, '_romaji_title', true);
    $english_title = get_post_meta($novel_id, '_english_title', true);
    $release_date = get_post_meta($novel_id, '_release_date', true);
    $novel_type = get_post_meta($novel_id, '_novel_type', true);
    $mangaupdates_id = get_post_meta($novel_id, '_mangaupdates_id', true);
    $anilist_id = get_post_meta($novel_id, '_anilist_id', true);
    $volume_covers = get_post_meta($novel_id, '_volume_covers', true) ?: array();

    // Get novel status
    $status = wp_get_object_terms($novel_id, 'status', array('fields' => 'names'));
    $status = !empty($status) ? $status[0] : '';

    // Get authors and illustrators
    $authors = wp_get_object_terms($novel_id, 'novel_author');
    $illustrators = wp_get_object_terms($novel_id, 'novel_illustrator');

    // Get rating data
    $user_ratings = get_post_meta($novel_id, '_user_ratings', true);
    $rating_count = is_array($user_ratings) ? count($user_ratings) : 0;
    $total_rating = 0;

    if (is_array($user_ratings) && !empty($user_ratings)) {
        foreach ($user_ratings as $user_rating) {
            $total_rating += $user_rating['rating'];
        }
        $rating = round($total_rating / $rating_count, 1);
    } else {
        $rating = 0;
    }

    // Check if novel is bookmarked
    $bookmarked = false;
    if (is_user_logged_in()) {
        $bookmarks = get_user_meta($user_id, '_novel_bookmarks', true);
        $bookmarked = is_array($bookmarks) && in_array($novel_id, $bookmarks);
    }

    // Get bookmark count
    $bookmark_count = intval(get_post_meta($novel_id, '_bookmark_count', true));

    // Get chapter count
    $chapter_count = sekaiplus_get_chapter_count($novel_id);
?>
<style>
/* ===== متغيرات CSS ===== */
:root {
    --novel-primary: var(--bs-primary, #0d6efd);
    --novel-secondary: var(--bs-secondary, #6c757d);
    --novel-success: var(--bs-success, #198754);
    --novel-danger: var(--bs-danger, #dc3545);
    --novel-warning: var(--bs-warning, #ffc107);
    --novel-info: var(--bs-info, #0dcaf0);
    --novel-light: var(--bs-light, #f8f9fa);
    --novel-dark: var(--bs-dark, #212529);
    --novel-bg: var(--bs-body-bg, #fff);
    --novel-text: var(--bs-body-color, #212529);
    --novel-border: rgba(0, 0, 0, 0.125);
    --novel-shadow: rgba(0, 0, 0, 0.1);
    --novel-hover-bg: rgba(0, 0, 0, 0.03);
    --novel-transition: all 0.3s ease;
    --novel-gradient-primary: linear-gradient(90deg, #6d8cff, #f49ca0);
    --novel-gradient-hover: linear-gradient(90deg, #4361ee, #ff6b6b);
}

.dark-mode {
    --novel-bg: var(--bs-dark, #212529);
    --novel-text: var(--bs-light, #f8f9fa);
    --novel-border: rgba(255, 255, 255, 0.125);
    --novel-shadow: rgba(0, 0, 0, 0.25);
    --novel-hover-bg: rgba(255, 255, 255, 0.05);
    --novel-gradient-primary: linear-gradient(90deg, #233466, #7c4a4a);
}

/* ===== شريط التنقل العلوي ===== */
.novel-navbar {
    position: sticky;
    top: 0;
    background-color: var(--novel-bg);
    box-shadow: 0 2px 10px var(--novel-shadow);
    padding: 0.75rem 0;
    z-index: 1000;
    transition: var(--novel-transition);
}

.novel-navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.novel-nav-left,
.novel-nav-right {
    flex: 0 0 auto;
}

.novel-nav-center {
    flex: 1;
    text-align: center;
    padding: 0 1rem;
}

.novel-nav-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.novel-nav-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--novel-bg);
    color: var(--novel-primary);
    border: 1px solid var(--novel-primary);
    transition: var(--novel-transition);
    text-decoration: none;
    cursor: pointer;
}

.novel-nav-btn:hover {
    background-color: var(--novel-primary);
    color: white;
    transform: scale(1.05);
}

.novel-nav-btn.active {
    background-color: var(--novel-primary);
    color: white;
}

/* ===== بطاقة معلومات الرواية ===== */
.novel-header-card {
    background-color: var(--novel-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--novel-shadow);
    padding: 1.5rem 0;
    margin: 2rem 0;
    transition: var(--novel-transition);
}

.novel-cover-wrapper {
    position: relative;
    margin-bottom: 1.5rem;
}

.novel-cover {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.15);
    transition: var(--novel-transition);
}

.novel-header-content {
    padding: 0 1rem;
}

.novel-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--novel-text);
}

.novel-alt-titles {
    margin-bottom: 1.25rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--novel-border);
}

.alt-title {
    margin-bottom: 0.5rem;
    font-size: 1rem;
    color: var(--novel-secondary);
}

.alt-title.japanese {
    font-family: 'Noto Sans JP', sans-serif;
}

.alt-title.english {
    font-style: italic;
}

/* ===== وصف الرواية ===== */
.novel-description-content {
    max-height: 160px;
    overflow: hidden;
    position: relative;
    transition: max-height 0.5s cubic-bezier(.4,2,.6,1);
    direction: rtl;
    white-space: pre-line;
}

.novel-description-content.expanded {
    max-height: 2000px;
}

.show-more-btn {
    margin: 18px auto 0 auto;
    display: block;
    background: var(--novel-gradient-primary);
    color: #fff;
    border: none;
    border-radius: 16px;
    padding: 8px 34px;
    font-size: 1.08rem;
    font-family: inherit;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(67,97,238,0.08);
    transition: var(--novel-transition);
    letter-spacing: 1px;
    text-align: center;
    outline: none;
}

.show-more-btn:hover {
    background: var(--novel-gradient-hover);
    color: #fff;
    box-shadow: 0 4px 18px rgba(67,97,238,0.14);
}

.novel-description-content::after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0; right: 0; left: 0;
    height: 60px;
    background: linear-gradient(to bottom, rgba(255,255,255,0) 0%, var(--novel-bg) 90%);
    pointer-events: none;
    transition: opacity .3s;
}

.novel-description-content.expanded::after {
    opacity: 0;
}

/* ===== قسم البيانات الوصفية ===== */
.novel-meta-section {
    margin: 1.5rem 0;
}

.novel-meta-container {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.05);
}

.novel-meta-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 0.25rem;
}

.meta-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.meta-icon {
    flex-shrink: 0;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--novel-bg);
    color: var(--novel-primary);
    border-radius: 50%;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
}

.meta-icon i {
    font-size: 1.1rem;
}

.meta-content {
    flex: 1;
}

.meta-label {
    font-size: 0.85rem;
    color: var(--novel-secondary);
    margin-bottom: 0.25rem;
}

.meta-value {
    font-weight: 500;
    color: var(--novel-text);
}

.meta-value a {
    color: var(--novel-primary);
    text-decoration: none;
    transition: var(--novel-transition);
}

.meta-value a:hover {
    color: rgba(var(--bs-primary-rgb), 0.8);
    text-decoration: underline;
}

.rating-stars {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.rating-count {
    margin-left: 0.5rem;
    color: var(--novel-secondary);
}

/* ===== أزرار الإجراءات ===== */
.novel-actions {
    margin: 1.5rem 0;
    padding-top: 1.25rem;
    border-top: 1px solid var(--novel-border);
}

.actions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border-radius: 2rem;
    background-color: var(--novel-bg);
    color: var(--novel-primary);
    border: 1px solid var(--novel-primary);
    text-decoration: none;
    transition: var(--novel-transition);
    font-weight: 500;
    cursor: pointer;
}

.action-btn:hover {
    background-color: var(--novel-primary);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.2);
}

.action-btn i,
.action-btn .icon,
.action-btn img {
    margin-right: 0.75rem;
}

.action-btn.bookmark-btn {
    background-color: var(--novel-bg);
    color: var(--novel-primary);
}

.action-btn.bookmark-btn:hover,
.action-btn.bookmark-btn.active {
    background-color: var(--novel-primary);
    color: white;
}

.action-btn.external-btn {
    background-color: var(--novel-bg);
    color: var(--novel-info);
    border-color: var(--novel-info);
}

.action-btn.external-btn:hover {
    background-color: var(--novel-info);
    color: white;
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-info-rgb), 0.2);
}

.action-btn.external-btn.mangaupdates .icon {
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    font-style: italic;
    font-size: 1.1rem;
}

.action-btn.external-btn.anilist img {
    width: 1.25rem;
    height: 1.25rem;
}

/* ===== بطاقات المحتوى ===== */
.novel-content-card,
.novel-sidebar-card {
    background-color: var(--novel-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--novel-shadow);
    transition: var(--novel-transition);
    border: none;
    overflow: hidden;
}

.novel-content-card .card-header,
.novel-sidebar-card .card-header {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-bottom: 1px solid var(--novel-border);
    padding: 1rem 1.25rem;
}

.novel-content-card .card-title,
.novel-sidebar-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--novel-text);
    display: flex;
    align-items: center;
}

.novel-content-card .card-title i,
.novel-sidebar-card .card-title i {
    margin-left: 0.75rem;
    color: var(--novel-primary);
}

.novel-content-card .card-body,
.novel-sidebar-card .card-body {
    padding: 1.5rem;
}

/* ===== التصنيفات ===== */
.genres-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.genre-badge {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    background-color: var(--novel-bg);
    color: var(--novel-secondary);
    border: 1px solid var(--novel-secondary);
    text-decoration: none;
    transition: var(--novel-transition);
    font-size: 0.9rem;
}

.genre-badge:hover {
    background-color: var(--novel-secondary);
    color: white;
    transform: translateY(-2px);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 991.98px) {
    .novel-header-card {
        padding: 1rem 0;
    }

    .novel-meta-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .novel-content-card .card-body,
    .novel-sidebar-card .card-body {
        padding: 1.25rem;
    }
}

@media (max-width: 767.98px) {
    .novel-title {
        font-size: 1.5rem;
    }

    .novel-meta-grid {
        grid-template-columns: 1fr;
    }

    .actions-container {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 575.98px) {
    .novel-header-card,
    .novel-content-card,
    .novel-sidebar-card {
        border-radius: 0.375rem;
    }

    .novel-content-card .card-header,
    .novel-sidebar-card .card-header {
        padding: 0.75rem 1rem;
    }

    .novel-content-card .card-body,
    .novel-sidebar-card .card-body {
        padding: 1rem;
    }

    .novel-content-card .card-title,
    .novel-sidebar-card .card-title {
        font-size: 1.1rem;
    }
}
</style>
<!-- شريط التنقل العلوي الثابت -->
<div class="novel-navbar">
    <div class="container">
        <div class="novel-navbar-content">
            <div class="novel-nav-left">
                <a href="<?php echo home_url('/library'); ?>" class="novel-nav-btn" title="العودة لقائمة الروايات">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </div>

            <div class="novel-nav-center">
                <h1 class="novel-nav-title">
                    <span class="novel-title"><?php the_title(); ?></span>
                </h1>
            </div>

            <div class="novel-nav-right">
                <?php if (is_user_logged_in()): ?>
                    <button type="button"
                            class="novel-nav-btn bookmark-toggle <?php echo $bookmarked ? 'active' : ''; ?>"
                            data-novel-id="<?php echo $novel_id; ?>"
                            onclick="sekaiplus_toggle_bookmark(<?php echo $novel_id; ?>)"
                            title="<?php echo $bookmarked ? 'إزالة من القائمة' : 'إضافة للقائمة'; ?>">
                        <i class="fa-bookmark <?php echo $bookmarked ? 'fas' : 'far'; ?>"></i>
                    </button>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- بطاقة معلومات الرواية -->
<div class="novel-header-card">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-3">
                <div class="novel-cover-wrapper">
                    <?php if (has_post_thumbnail()): ?>
                        <?php the_post_thumbnail('novel-cover', array(
                            'class' => 'novel-cover img-fluid rounded shadow',
                            'alt' => get_the_title()
                        )); ?>
                    <?php else: ?>
                        <img src="<?php echo get_template_directory_uri(); ?>/assets/images/default-cover.png"
                             class="novel-cover img-fluid rounded shadow" alt="غلاف افتراضي">
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-9">
                <div class="novel-header-content">
                    <h1 class="novel-title"><?php the_title(); ?></h1>

                    <?php if ($japanese_title || $romaji_title || $english_title): ?>
                    <div class="novel-alt-titles">
                        <?php if ($japanese_title): ?>
                            <div class="alt-title japanese"><?php echo esc_html($japanese_title); ?></div>
                        <?php endif; ?>
                        <?php if ($romaji_title): ?>
                            <div class="alt-title romaji"><?php echo esc_html($romaji_title); ?></div>
                        <?php endif; ?>
                        <?php if ($english_title): ?>
                            <div class="alt-title english"><?php echo esc_html($english_title); ?></div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <div class="novel-meta-section">
                        <div class="novel-meta-container">
                            <div class="novel-meta-grid">
                                <?php if (!empty($authors)): ?>
                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-pen-fancy"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">المؤلف</div>
                                        <div class="meta-value">
                                            <?php
                                            $author_links = array();
                                            foreach ($authors as $author) {
                                                $author_links[] = '<a href="' . get_term_link($author) . '">' . esc_html($author->name) . '</a>';
                                            }
                                            echo implode('، ', $author_links);
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($illustrators)): ?>
                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-paint-brush"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">الرسام</div>
                                        <div class="meta-value">
                                            <?php
                                            $illustrator_links = array();
                                            foreach ($illustrators as $illustrator) {
                                                $illustrator_links[] = '<a href="' . get_term_link($illustrator) . '">' . esc_html($illustrator->name) . '</a>';
                                            }
                                            echo implode('، ', $illustrator_links);
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($release_date): ?>
                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="far fa-calendar-alt"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">تاريخ الإصدار</div>
                                        <div class="meta-value">
                                            <?php echo date_i18n(get_option('date_format'), strtotime($release_date)); ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($novel_type): ?>
                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">النوع</div>
                                        <div class="meta-value">
                                            <span class="badge bg-primary">
                                                <?php echo $novel_type === 'light_novel' ? 'رواية خفيفة' : 'رواية ويب'; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <?php if ($status): ?>
                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">الحالة</div>
                                        <div class="meta-value">
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($status) {
                                                case 'مكتملة':
                                                    $status_class = 'bg-success';
                                                    $status_text = 'مكتملة';
                                                    break;
                                                case 'متوقفة':
                                                    $status_class = 'bg-warning';
                                                    $status_text = 'متوقفة';
                                                    break;
                                                case 'مستمرة':
                                                    $status_class = 'bg-info';
                                                    $status_text = 'مستمرة';
                                                    break;
                                                case 'ملغية':
                                                    $status_class = 'bg-danger';
                                                    $status_text = 'ملغية';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    $status_text = $status;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <?php echo $status_text; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-bookmark"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">المتابعون</div>
                                        <div class="meta-value">
                                            <span class="bookmark-count"><?php echo $bookmark_count; ?></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-star text-warning"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">التقييم</div>
                                        <div class="meta-value">
                                            <div class="rating-stars">
                                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                                    <i class="fas fa-star <?php echo $i <= $rating ? 'text-warning' : 'text-muted'; ?>"
                                                       data-rating="<?php echo $i; ?>"
                                                       <?php if (is_user_logged_in()): ?>
                                                           style="cursor: pointer;"
                                                       <?php endif; ?>></i>
                                                <?php endfor; ?>
                                                <small class="rating-count">(<?php echo $rating_count; ?>)</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="meta-item">
                                    <div class="meta-icon">
                                        <i class="fas fa-book-open"></i>
                                    </div>
                                    <div class="meta-content">
                                        <div class="meta-label">عدد الفصول</div>
                                        <div class="meta-value">
                                            <span class="chapter-count"><?php echo $chapter_count; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Toast Container for Notifications -->
                    <div class="toast-container position-fixed bottom-0 end-0 p-3">
                        <div id="ratingToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="toast-header">
                                <i class="fas fa-star text-warning me-2"></i>
                                <strong class="me-auto">التقييم</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                            <div class="toast-body"></div>
                        </div>
                    </div>

                    <div class="novel-actions">
                        <div class="actions-container">
                            <?php if (is_user_logged_in()): ?>
                                <button type="button"
                                        class="action-btn bookmark-btn <?php echo $bookmarked ? 'active' : ''; ?>"
                                        data-novel-id="<?php echo $novel_id; ?>"
                                        onclick="sekaiplus_toggle_bookmark(<?php echo $novel_id; ?>)">
                                    <i class="fa-bookmark <?php echo $bookmarked ? 'fas' : 'far'; ?>"></i>
                                    <span><?php echo $bookmarked ? 'إزالة من القائمة' : 'إضافة للقائمة'; ?></span>
                                </button>
                            <?php endif; ?>

                            <?php if ($mangaupdates_id): ?>
                                <a href="https://www.mangaupdates.com/series/<?php echo esc_attr($mangaupdates_id); ?>"
                                   class="action-btn external-btn mangaupdates" target="_blank">
                                    <span class="icon">M</span>
                                    <span class="text">MangaUpdates</span>
                                </a>
                            <?php endif; ?>

                            <?php if ($anilist_id): ?>
                                <a href="https://anilist.co/manga/<?php echo esc_attr($anilist_id); ?>"
                                   class="action-btn external-btn anilist" target="_blank" title="AniList">
                                    <img src="https://anilist.co/img/icons/icon.svg" alt="AniList" class="anilist-icon">
                                    <span class="text">AniList</span>
                                </a>
                            <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <div class="row">
        <div class="col-lg-8">
            <!-- Description -->
            <div class="novel-content-card mb-4">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-align-left"></i>
                        القصة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="novel-description-content" id="novel-description-content">
                        <?php the_content(); ?>
                    </div>
                    <button class="show-more-btn" id="show-more-btn" style="display:none;">أفتح</button>
                    <button class="show-more-btn" id="hide-more-btn" style="display:none;">أقفل</button>
                </div>
            </div>

            <!-- Chapters Section -->
            <?php include(get_template_directory() . '/template-parts/novel-chapters.php'); ?>
        </div>

        <div class="col-lg-4">
            <?php
            // Check if volumes exist
            $has_volumes = false;

            // Check if volume covers exist
            if (!empty($volume_covers)) {
                $has_volumes = true;
            } else {
                // Check if any chapters have volume numbers
                $chapters_with_volumes = get_posts(array(
                    'post_type' => 'chapter',
                    'posts_per_page' => 1,
                    'meta_query' => array(
                        array(
                            'key' => '_novel_id',
                            'value' => $novel_id
                        ),
                        array(
                            'key' => '_volume_number',
                            'compare' => 'EXISTS'
                        )
                    )
                ));

                if (!empty($chapters_with_volumes)) {
                    $has_volumes = true;
                }
            }

            // Only display the Volume Covers section if volumes exist
            if ($has_volumes):
            ?>
            <!-- Volume Covers -->
            <div class="novel-sidebar-card mb-4">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-book-open"></i>
                        المجلدات
                    </h5>
                </div>
                <div class="card-body">
                    <?php get_template_part('template-parts/novel-volumes'); ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Genres -->
            <?php
            $genres = get_the_terms($novel_id, 'genre');
            if ($genres && !is_wp_error($genres)):
            ?>
            <div class="novel-sidebar-card mb-4">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-tags"></i>
                        التصنيفات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="genres-container">
                        <?php foreach ($genres as $genre): ?>
                            <a href="<?php echo get_term_link($genre); ?>"
                               class="genre-badge">
                                <?php echo esc_html($genre->name); ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
			
			
            <!-- Novel Relations -->
            <?php
            $novel_relations = get_post_meta($novel_id, '_novel_relations', true);
            if (!empty($novel_relations) && is_array($novel_relations)):
            ?>
            <div class="novel-sidebar-card mb-4">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-link"></i>
                        صِلات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="novel-relations-grid">
                        <?php foreach ($novel_relations as $relation): ?>
                            <?php
                            $related_novel = get_post($relation['novel_id']);
                            if ($related_novel && $related_novel->post_status === 'publish'):
                                $related_cover = get_the_post_thumbnail_url($related_novel->ID, 'thumbnail');
                                if (!$related_cover) {
                                    $related_cover = get_template_directory_uri() . '/assets/images/default-cover.png';
                                }
                            ?>
                            <div class="novel-relation-item">
                                <a href="<?php echo get_permalink($related_novel->ID); ?>" class="relation-link">
                                    <div class="relation-cover">
                                        <img src="<?php echo esc_url($related_cover); ?>"
                                             alt="<?php echo esc_attr($related_novel->post_title); ?>"
                                             class="img-fluid rounded">
                                        <div class="relation-type-badge">
                                            <?php echo esc_html($relation['relation_type']); ?>
                                        </div>
                                    </div>
                                    <div class="relation-title">
                                        <?php echo esc_html($related_novel->post_title); ?>
                                    </div>
                                </a>
                            </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php endwhile; ?>

<script>
jQuery(document).ready(function($) {
    // Initialize toast
    var ratingToast = new bootstrap.Toast(document.getElementById('ratingToast'));

    // Show notification
    function showNotification(message, type) {
        var toast = $('#ratingToast');
        toast.find('.toast-body').text(message);

        // Remove previous classes
        toast.removeClass('bg-success bg-danger bg-warning bg-info text-white');

        // Add appropriate class based on type
        switch(type) {
            case 'success':
                toast.addClass('bg-success text-white');
                break;
            case 'error':
                toast.addClass('bg-danger text-white');
                break;
            case 'warning':
                toast.addClass('bg-warning');
                break;
            case 'info':
                toast.addClass('bg-info text-white');
                break;
        }

        ratingToast.show();
    }

    // Handle star hover
    $('.rating-stars i').hover(
        function() {
            if (!isRating) {
                var rating = $(this).data('rating');
                $('.rating-stars i').each(function() {
                    if ($(this).data('rating') <= rating) {
                        $(this).addClass('text-warning').removeClass('text-muted');
                    } else {
                        $(this).addClass('text-muted').removeClass('text-warning');
                    }
                });
            }
        },
        function() {
            if (!isRating) {
                updateStarsDisplay(currentRating);
            }
        }
    );

    // Handle rating click
    var currentRating = <?php
        echo (is_array($user_ratings) && isset($user_ratings[get_current_user_id()]['rating'])) ?
             $user_ratings[get_current_user_id()]['rating'] : 0;
    ?>;
    var isRating = false;

    $('.rating-stars i').on('click', function() {
        if (isRating) return;

        var rating = $(this).data('rating');
        isRating = true;

        $.ajax({
            url: sekaiplus.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_rate_novel',
                novel_id: <?php echo $novel_id; ?>,
                rating: rating,
                nonce: sekaiplus.nonce
            },
            success: function(response) {
                isRating = false;
                if (response.success) {
                    currentRating = rating;
                    updateStarsDisplay(rating);
                    // Only update rating count on successful NEW rating
                    $('.rating-count').text('(' + response.data.ratingCount + ' تقييم)');
                    showNotification(response.data.message, response.data.type);
                } else {
                    // Don't update rating count on cooldown or other errors
                    if (response.data && response.data.currentRating) {
                        currentRating = response.data.currentRating;
                        updateStarsDisplay(response.data.currentRating);
                    }
                    showNotification(response.data.message, response.data.type || 'error');
                }
            },
            error: function() {
                isRating = false;
                showNotification('حدث خطأ أثناء التقييم', 'error');
                updateStarsDisplay(currentRating);
            }
        });
    });

    function updateStarsDisplay(rating) {
        if (typeof rating === 'undefined' || rating === null) {
            rating = 0;
        }
        $('.rating-stars i').each(function() {
            if ($(this).data('rating') <= rating) {
                $(this).addClass('text-warning').removeClass('text-muted');
            } else {
                $(this).addClass('text-muted').removeClass('text-warning');
            }
        });
    }

    // Initialize stars display
    updateStarsDisplay(currentRating);
});

document.addEventListener('DOMContentLoaded', function() {
    var desc = document.getElementById('novel-description-content');
    var btnShow = document.getElementById('show-more-btn');
    var btnHide = document.getElementById('hide-more-btn');
    // أظهر زر (أكمل القراءة) فقط إذا كان المحتوى أطول من max-height
    if (desc.scrollHeight > desc.clientHeight + 20) {
        btnShow.style.display = 'block';
        btnShow.onclick = function() {
            desc.classList.add('expanded');
            btnShow.style.display = 'none';
            btnHide.style.display = 'block';
        }
        btnHide.onclick = function() {
            desc.classList.remove('expanded');
            btnShow.style.display = 'block';
            btnHide.style.display = 'none';
            // إعادة التمرير للأعلى إذا احتجت
            desc.scrollIntoView({behavior: 'smooth', block: 'center'});
        }
    }
});
</script>
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- قسم التعليقات -->
            <div class="novel-content-card comments-card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-comments"></i>
                        التعليقات
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    // تأكد من تفعيل التعليقات للمنشور الحالي
                    if (comments_open() || get_comments_number()) {
                        comments_template();
                    } else {
                        echo '<div class="alert alert-info text-center">التعليقات مغلقة لهذا المحتوى.</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>
	
<style>
/* Novel Relations Styles */
.novel-relations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    padding: 10px 0;
}

.novel-relation-item {
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.novel-relation-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.relation-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.relation-cover {
    position: relative;
    margin-bottom: 8px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.relation-cover img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.novel-relation-item:hover .relation-cover img {
    transform: scale(1.05);
}

.relation-type-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.relation-title {
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    line-height: 1.3;
    color: var(--text-color, #333);
    padding: 0 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 32px;
}

/* Dark mode support */
[data-theme="dark"] .relation-title {
    color: var(--text-color-dark, #e0e0e0);
}

[data-theme="dark"] .relation-type-badge {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

[data-theme="dark"] .relation-cover {
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .novel-relation-item:hover {
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.15);
}

/* Responsive design */
@media (max-width: 768px) {
    .novel-relations-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 12px;
    }

    .relation-cover img {
        height: 140px;
    }

    .relation-title {
        font-size: 11px;
    }

    .relation-type-badge {
        font-size: 9px;
        padding: 3px 6px;
    }
}

@media (max-width: 480px) {
    .novel-relations-grid {
        grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
        gap: 10px;
    }

    .relation-cover img {
        height: 120px;
    }
}

/* Animation for loading */
.novel-relation-item {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.novel-relation-item:nth-child(1) { animation-delay: 0.1s; }
.novel-relation-item:nth-child(2) { animation-delay: 0.2s; }
.novel-relation-item:nth-child(3) { animation-delay: 0.3s; }
.novel-relation-item:nth-child(4) { animation-delay: 0.4s; }
.novel-relation-item:nth-child(5) { animation-delay: 0.5s; }
.novel-relation-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?php get_footer(); ?>
