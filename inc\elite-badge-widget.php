<?php
/**
 * Elite Badge Widget
 * 
 * يعرض شارة النخبة للمستخدمين المسجلين قبل مارس 2025
 */

// تسجيل الـ Widget
function register_elite_badge_widget() {
    register_widget('Elite_Badge_Widget');
}
add_action('widgets_init', 'register_elite_badge_widget');

// تعريف فئة الـ Widget
class Elite_Badge_Widget extends WP_Widget {
    
    /**
     * إعداد الـ Widget
     */
    public function __construct() {
        parent::__construct(
            'elite_badge_widget', // قاعدة ID للـ widget
            'شارة النخبة', // اسم الـ widget
            array(
                'description' => 'يعرض شارة النخبة للمستخدمين المسجلين قبل مارس 2025',
                'classname' => 'elite-badge-widget',
            )
        );
    }
    
    /**
     * عرض الـ Widget في الواجهة الأمامية
     */
    public function widget($args, $instance) {
        // استخراج الـ args
        extract($args);
        
        // استخراج إعدادات الـ widget
        $title = apply_filters('widget_title', empty($instance['title']) ? 'شارة النخبة' : $instance['title']);
        $show_description = isset($instance['show_description']) ? (bool) $instance['show_description'] : true;
        $custom_description = isset($instance['custom_description']) ? $instance['custom_description'] : 'أعضاء النخبة هم من سجلوا قبل مارس 2025';
        $show_count = isset($instance['show_count']) ? (bool) $instance['show_count'] : true;
        $show_users = isset($instance['show_users']) ? (bool) $instance['show_users'] : true;
        $users_count = isset($instance['users_count']) ? intval($instance['users_count']) : 5;
        
        // التحقق مما إذا كان المستخدم الحالي مؤهلاً للحصول على شارة النخبة
        $current_user_id = get_current_user_id();
        $is_current_user_elite = is_user_logged_in() && is_elite_user($current_user_id);
        
        // الحصول على عدد مستخدمي النخبة
        $elite_users_count = $this->get_elite_users_count();
        
        // الحصول على قائمة مستخدمي النخبة
        $elite_users = $show_users ? $this->get_elite_users($users_count) : array();
        
        // بداية الـ widget
        echo $before_widget;
        
        // عنوان الـ widget
        if (!empty($title)) {
            echo $before_title . $title . $after_title;
        }
        
        // محتوى الـ widget
        echo '<div class="elite-badge-widget-content">';
        
        // عرض الوصف إذا كان مفعلاً
        if ($show_description) {
            echo '<div class="elite-badge-description">' . esc_html($custom_description) . '</div>';
        }
        
        // عرض عدد مستخدمي النخبة إذا كان مفعلاً
        if ($show_count) {
            echo '<div class="elite-users-count"><i class="fas fa-users"></i> ' . sprintf(_n('%s عضو نخبة', '%s أعضاء نخبة', $elite_users_count, 'sekaiplus'), number_format($elite_users_count)) . '</div>';
        }
        
        // عرض حالة المستخدم الحالي
        if (is_user_logged_in()) {
            echo '<div class="current-user-status">';
            if ($is_current_user_elite) {
                echo '<div class="elite-status-positive"><i class="fas fa-check-circle"></i> أنت من أعضاء النخبة! <span class="badge elite-badge">النخبة <i class="fas fa-crown"></i></span></div>';
            } else {
                echo '<div class="elite-status-negative"><i class="fas fa-times-circle"></i> أنت لست من أعضاء النخبة</div>';
            }
            echo '</div>';
        }
        
        // عرض قائمة مستخدمي النخبة إذا كان مفعلاً
        if ($show_users && !empty($elite_users)) {
            echo '<div class="elite-users-list">';
            echo '<h4 class="elite-users-heading">أعضاء النخبة</h4>';
            echo '<ul>';
            foreach ($elite_users as $user) {
                echo '<li class="elite-user-item">';
                echo '<a href="' . esc_url(get_author_posts_url($user->ID)) . '" class="elite-user-link">';
                echo get_avatar($user->ID, 40, '', '', array('class' => 'elite-user-avatar'));
                echo '<span class="elite-user-name">' . esc_html($user->display_name) . '</span>';
                echo '</a>';
                echo '<span class="badge elite-badge">النخبة <i class="fas fa-crown"></i></span>';
                echo '</li>';
            }
            echo '</ul>';
            echo '</div>';
        }
        
        echo '</div>'; // نهاية محتوى الـ widget
        
        // نهاية الـ widget
        echo $after_widget;
        
        // إضافة التنسيقات
        $this->add_inline_styles();
    }
    
    /**
     * نموذج الإعدادات في لوحة التحكم
     */
    public function form($instance) {
        // الإعدادات الافتراضية
        $defaults = array(
            'title' => 'شارة النخبة',
            'show_description' => true,
            'custom_description' => 'أعضاء النخبة هم من سجلوا قبل مارس 2025',
            'show_count' => true,
            'show_users' => true,
            'users_count' => 5
        );
        
        // دمج الإعدادات المحفوظة مع الافتراضية
        $instance = wp_parse_args((array) $instance, $defaults);
        
        // استخراج الإعدادات
        $title = $instance['title'];
        $show_description = isset($instance['show_description']) ? (bool) $instance['show_description'] : true;
        $custom_description = isset($instance['custom_description']) ? $instance['custom_description'] : 'أعضاء النخبة هم من سجلوا قبل مارس 2025';
        $show_count = isset($instance['show_count']) ? (bool) $instance['show_count'] : true;
        $show_users = isset($instance['show_users']) ? (bool) $instance['show_users'] : true;
        $users_count = isset($instance['users_count']) ? intval($instance['users_count']) : 5;
        
        // عرض نموذج الإعدادات
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>"><?php _e('العنوان:', 'sekaiplus'); ?></label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($title); ?>" />
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_description); ?> id="<?php echo $this->get_field_id('show_description'); ?>" name="<?php echo $this->get_field_name('show_description'); ?>" />
            <label for="<?php echo $this->get_field_id('show_description'); ?>"><?php _e('عرض الوصف', 'sekaiplus'); ?></label>
        </p>
        
        <p>
            <label for="<?php echo $this->get_field_id('custom_description'); ?>"><?php _e('الوصف المخصص:', 'sekaiplus'); ?></label>
            <textarea class="widefat" id="<?php echo $this->get_field_id('custom_description'); ?>" name="<?php echo $this->get_field_name('custom_description'); ?>" rows="3"><?php echo esc_textarea($custom_description); ?></textarea>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_count); ?> id="<?php echo $this->get_field_id('show_count'); ?>" name="<?php echo $this->get_field_name('show_count'); ?>" />
            <label for="<?php echo $this->get_field_id('show_count'); ?>"><?php _e('عرض عدد المستخدمين', 'sekaiplus'); ?></label>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_users); ?> id="<?php echo $this->get_field_id('show_users'); ?>" name="<?php echo $this->get_field_name('show_users'); ?>" />
            <label for="<?php echo $this->get_field_id('show_users'); ?>"><?php _e('عرض قائمة المستخدمين', 'sekaiplus'); ?></label>
        </p>
        
        <p>
            <label for="<?php echo $this->get_field_id('users_count'); ?>"><?php _e('عدد المستخدمين المعروضين:', 'sekaiplus'); ?></label>
            <input class="tiny-text" id="<?php echo $this->get_field_id('users_count'); ?>" name="<?php echo $this->get_field_name('users_count'); ?>" type="number" min="1" max="20" value="<?php echo esc_attr($users_count); ?>" />
        </p>
        <?php
    }
    
    /**
     * حفظ إعدادات الـ Widget
     */
    public function update($new_instance, $old_instance) {
        $instance = $old_instance;
        
        // تحديث الإعدادات
        $instance['title'] = sanitize_text_field($new_instance['title']);
        $instance['show_description'] = isset($new_instance['show_description']) ? (bool) $new_instance['show_description'] : false;
        $instance['custom_description'] = sanitize_textarea_field($new_instance['custom_description']);
        $instance['show_count'] = isset($new_instance['show_count']) ? (bool) $new_instance['show_count'] : false;
        $instance['show_users'] = isset($new_instance['show_users']) ? (bool) $new_instance['show_users'] : false;
        $instance['users_count'] = intval($new_instance['users_count']);
        
        // التأكد من أن عدد المستخدمين ضمن النطاق المسموح
        if ($instance['users_count'] < 1) {
            $instance['users_count'] = 1;
        } elseif ($instance['users_count'] > 20) {
            $instance['users_count'] = 20;
        }
        
        return $instance;
    }
    
    /**
     * الحصول على عدد مستخدمي النخبة
     */
    private function get_elite_users_count() {
        $cutoff_date = strtotime('2025-03-01 00:00:00');
        
        $args = array(
            'date_query' => array(
                array(
                    'before' => array(
                        'year' => date('Y', $cutoff_date),
                        'month' => date('m', $cutoff_date),
                        'day' => date('d', $cutoff_date),
                    ),
                ),
            ),
            'fields' => 'ID',
            'count_total' => true,
        );
        
        $user_query = new WP_User_Query($args);
        return $user_query->get_total();
    }
    
    /**
     * الحصول على قائمة مستخدمي النخبة
     */
    private function get_elite_users($count = 5) {
        $cutoff_date = strtotime('2025-03-01 00:00:00');
        
        $args = array(
            'date_query' => array(
                array(
                    'before' => array(
                        'year' => date('Y', $cutoff_date),
                        'month' => date('m', $cutoff_date),
                        'day' => date('d', $cutoff_date),
                    ),
                ),
            ),
            'number' => $count,
            'orderby' => 'registered',
            'order' => 'ASC',
        );
        
        $user_query = new WP_User_Query($args);
        return $user_query->get_results();
    }
    
    /**
     * إضافة التنسيقات الخاصة بالـ Widget
     */
    private function add_inline_styles() {
        // التحقق مما إذا كان الوضع الداكن مفعلاً
        $dark_mode = isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true';
        
        // تحديد ألوان الخلفية والنص بناءً على الوضع
        $bg_color = $dark_mode ? '#2a2d3a' : '#ffffff';
        $text_color = $dark_mode ? '#e4e6eb' : '#333333';
        $border_color = $dark_mode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)';
        $hover_bg = $dark_mode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)';
        
        // إضافة التنسيقات
        echo '<style>
            .elite-badge-widget-content {
                background-color: ' . $bg_color . ';
                color: ' . $text_color . ';
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            }
            
            .elite-badge-description {
                margin-bottom: 15px;
                font-size: 14px;
                line-height: 1.5;
            }
            
            .elite-users-count {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 15px;
                color: var(--bs-primary);
            }
            
            .elite-users-count i {
                margin-right: 5px;
            }
            
            .current-user-status {
                background-color: ' . $hover_bg . ';
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                border: 1px solid ' . $border_color . ';
            }
            
            .elite-status-positive {
                color: #28a745;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .elite-status-negative {
                color: #dc3545;
                font-weight: 600;
            }
            
            .elite-status-positive i,
            .elite-status-negative i {
                margin-right: 5px;
            }
            
            .elite-users-heading {
                font-size: 16px;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid ' . $border_color . ';
            }
            
            .elite-users-list ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }
            
            .elite-user-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px 0;
                border-bottom: 1px solid ' . $border_color . ';
            }
            
            .elite-user-item:last-child {
                border-bottom: none;
            }
            
            .elite-user-link {
                display: flex;
                align-items: center;
                text-decoration: none;
                color: ' . $text_color . ';
                transition: all 0.2s ease;
            }
            
            .elite-user-link:hover {
                opacity: 0.8;
            }
            
            .elite-user-avatar {
                border-radius: 50%;
                margin-right: 10px;
            }
            
            .elite-user-name {
                font-weight: 600;
            }
        </style>';
    }
}
