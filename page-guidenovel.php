<?php
/**
 * Template Name: شرح إضافة رواية جديدة
 */
get_header();
?>

<style>
:root {
    --primary-color: #4361ee;
    --primary-light: #eaefff;
    --primary-dark: #3a56d4;
    --secondary-color: #ff6b6b;
    --text-dark: #2d3748;
    --text-medium: #4a5568;
    --text-light: #718096;
    --bg-light: #f8fafc;
    --bg-white: #ffffff;
    --success-color: #38b2ac;
    --warning-color: #f6ad55;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.08);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 2px 4px rgba(0,0,0,0.06);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --transition: all 0.3s ease;
}

.guide-container {
    max-width: 1000px;
    margin: 50px auto;
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: 40px;
    font-family: 'Cairo', 'Tajawal', sans-serif;
    direction: rtl;
    position: relative;
    overflow: hidden;
}

.guide-container::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.guide-title {
    text-align: center;
    font-size: 2.4rem;
    margin-bottom: 30px;
    color: var(--primary-color);
    font-weight: 800;
    position: relative;
    padding-bottom: 15px;
}

.guide-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    transform: translateX(50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 2px;
}

.guide-steps-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 30px;
}

.guide-step {
    background: var(--bg-white);
    border-radius: var(--radius-md);
    padding: 25px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
}

.guide-step:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-3px);
}

.guide-step-number {
    position: absolute;
    top: -15px;
    right: 20px;
    width: 36px;
    height: 36px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
    box-shadow: var(--shadow-sm);
}

.guide-step-title {
    font-size: 1.4rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-right: 25px;
}

.guide-step-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.guide-step-desc {
    color: var(--text-medium);
    font-size: 1.1rem;
    margin-bottom: 15px;
    line-height: 1.8;
}

.guide-step img {
    display: block;
    margin: 20px auto 0 auto;
    max-width: 100%;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.guide-step img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
}

.guide-note {
    background: var(--primary-light);
    color: var(--primary-dark);
    border-right: 4px solid var(--primary-color);
    border-radius: var(--radius-sm);
    padding: 15px 20px;
    margin: 15px 0;
    font-size: 1rem;
    line-height: 1.6;
}

.guide-warning {
    background: #fff8e6;
    color: #b58b00;
    border-right: 4px solid #f6ad55;
}

.guide-success {
    background: #e6fffa;
    color: #2c7a7b;
    border-right: 4px solid #38b2ac;
}

.guide-list {
    margin: 15px 0 15px 20px;
    padding: 0;
    list-style-type: none;
}

.guide-list li {
    position: relative;
    padding-right: 25px;
    margin-bottom: 10px;
    line-height: 1.6;
    color: black;
}

.guide-list li::before {
    content: '';
    position: absolute;
    right: 0;
    top: 10px;
    width: 8px;
    height: 8px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.guide-field {
    font-weight: bold;
    color: var(--primary-color);
    background: rgba(67, 97, 238, 0.08);
    padding: 2px 6px;
    border-radius: 4px;
}

.guide-summary {
    background: linear-gradient(to left, var(--primary-light), #f0f9ff);
    border-radius: var(--radius-md);
    padding: 25px;
    margin-top: 40px;
    position: relative;
    overflow: hidden;
}

.guide-summary::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.guide-summary-title {
    font-size: 1.3rem;
    color: var(--primary-dark);
    margin-bottom: 15px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
}

.guide-summary-content {
    color: var(--text-medium);
    font-size: 1.05rem;
    line-height: 1.7;
}

@media (max-width: 768px) {
    .guide-container {
        padding: 30px 20px;
        margin: 30px 15px;
    }

    .guide-title {
        font-size: 2rem;
    }

    .guide-step {
        padding: 20px 15px;
    }

    .guide-step-title {
        font-size: 1.2rem;
    }
}
</style>

<div class="guide-container">
    <div class="guide-title">دليل إضافة رواية جديدة</div>

    <div class="guide-steps-container">
        <div class="guide-step">
            <div class="guide-step-number">١</div>
            <div class="guide-step-title">
                <i class="fas fa-heading"></i>
                العنوان العربي للرواية
            </div>
            <div class="guide-step-desc">
                أدخل اسم الرواية بالعربية بشكل واضح وجذاب. سيظهر هذا العنوان في جميع صفحات الموقع وهو أول ما سيراه القراء.
            </div>
            <div class="guide-note">
                <strong>نصيحة:</strong> اختر عنوانًا مميزًا وسهل التذكر. مثال: <span class="guide-field">رحلة إلى عالم آخر</span>
            </div>
            <img src="https://i.imgur.com/jI4iqSR.png" alt="حقل العنوان العربي">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٢</div>
            <div class="guide-step-title">
                <i class="fas fa-book-open"></i>
                قصة الرواية
            </div>
            <div class="guide-step-desc">
                اكتب ملخصًا مشوقًا لأحداث الرواية. هذا الملخص سيكون بمثابة الجذب الأساسي للقراء المحتملين.
            </div>
            <div class="guide-note">
                <strong>نصائح لكتابة ملخص جذاب:</strong>
                <ul class="guide-list">
                    <li>اجعل الوصف مختصرًا وواضحًا (150-300 كلمة)</li>
                    <li>ركز على العناصر المميزة في القصة</li>
                    <li>تجنب كشف الأحداث المهمة (Spoilers)</li>
                    <li>استخدم أدوات التنسيق (عريض، مائل، فقرات) لتحسين العرض</li>
                </ul>
            </div>
            <img src="https://i.imgur.com/Gp4XpHP.png" alt="حقل قصة الرواية">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٣</div>
            <div class="guide-step-title">
                <i class="fas fa-language"></i>
                عناوين الرواية بلغات أخرى
            </div>
            <div class="guide-step-desc">
                أضف العناوين الأصلية والمترجمة للرواية. هذه المعلومات تساعد في تحسين نتائج البحث وتسهل على القراء العثور على الرواية.
            </div>
            <ul class="guide-list">
                <li><span class="guide-field">العنوان الياباني:</span> اكتب الاسم الأصلي باللغة اليابانية إن توفر</li>
                <li><span class="guide-field">العنوان الروماني:</span> كتابة العنوان الياباني بالحروف اللاتينية (Romaji)</li>
                <li><span class="guide-field">العنوان الإنجليزي:</span> الترجمة الرسمية أو الشائعة باللغة الإنجليزية</li>
            </ul>
            <img src="https://i.imgur.com/7kNOy9M.png" alt="حقول العناوين بلغات أخرى">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٤</div>
            <div class="guide-step-title">
                <i class="fas fa-calendar-alt"></i>
                تاريخ الإصدار ونوع الرواية
            </div>
            <div class="guide-step-desc">
                حدد تاريخ إصدار الرواية الأصلي ونوعها. هذه المعلومات تساعد في تصنيف الرواية وتنظيمها في الموقع.
            </div>
            <ul class="guide-list">
                <li><span class="guide-field">تاريخ الإصدار:</span> اختر التاريخ من التقويم المتاح</li>
                <li><span class="guide-field">نوع الرواية:</span> حدد النوع المناسب (رواية خفيفة، رواية ويب،...)</li>
            </ul>
            <div class="guide-note">
                <strong>ملاحظة:</strong> التصنيف الدقيق يساعد القراء في العثور على الرواية المناسبة لاهتماماتهم.
            </div>
            <img src="https://i.imgur.com/xlAOysf.png" alt="تاريخ الإصدار ونوع الرواية">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٥</div>
            <div class="guide-step-title">
                <i class="fas fa-user-edit"></i>
                المؤلف والرسام
            </div>
            <div class="guide-step-desc">
                حدد مؤلف الرواية والرسام (إن وجد) من القائمة المنسدلة. هذه المعلومات مهمة لتوثيق العمل وتسهيل البحث.
            </div>
            <div class="guide-note">
                <strong>إضافة مؤلف أو رسام جديد:</strong> إذا لم تجد الاسم في القائمة، يمكنك النقر على زر <span class="guide-field">إضافة مؤلف جديد</span> أو <span class="guide-field">إضافة رسام جديد</span> لإضافة اسم جديد.
            </div>
            <div class="guide-note guide-warning">
                <strong>تنبيه:</strong> يرجى التأكد من كتابة الأسماء بشكل صحيح وتجنب التكرار. تحقق من الإملاء قبل الإضافة.
            </div>
            <img src="https://i.imgur.com/QZK3Ga8.png" alt="حقل المؤلف والرسام">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٦</div>
            <div class="guide-step-title">
                <i class="fas fa-book"></i>
                أغلفة المجلدات
            </div>
            <div class="guide-step-desc">
                أضف معلومات وصور أغلفة المجلدات المختلفة للرواية. هذا يساعد في تنظيم الفصول وإضفاء مظهر احترافي للصفحة.
            </div>
            <ul class="guide-list">
                <li><span class="guide-field">رقم المجلد:</span> أدخل رقم المجلد (1، 2، 3، ...)</li>
                <li><span class="guide-field">عنوان المجلد:</span> أدخل عنوان المجلد إن وجد</li>
                <li><span class="guide-field">صورة الغلاف:</span> قم بتحميل صورة غلاف المجلد بجودة عالية</li>
            </ul>
            <div class="guide-note">
                <strong>نصيحة:</strong> يمكنك إضافة مجلدات متعددة بالنقر على زر <span class="guide-field">إضافة مجلد</span>. أضف جميع المجلدات المتوفرة لتسهيل تصفح الفصول.
            </div>
            <img src="https://i.imgur.com/XDAwVyI.png" alt="حقل أغلفة المجلدات">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٧</div>
            <div class="guide-step-title">
                <i class="fas fa-link"></i>
                روابط خارجية
            </div>
            <div class="guide-step-desc">
                أضف معرفات الرواية في المواقع الخارجية الشهيرة. هذه الروابط تساعد القراء في العثور على معلومات إضافية عن الرواية.
            </div>
            <ul class="guide-list">
                <li><span class="guide-field">معرف MangaUpdates:</span> أدخل معرف الرواية في موقع MangaUpdates (مثال: klbld7a)</li>
                <li><span class="guide-field">معرف AniList:</span> أدخل معرف الرواية في موقع AniList (مثال: 31414)</li>
            </ul>
            <div class="guide-note">
                <strong>ملاحظة هامة:</strong> أدخل المعرف فقط (ID) وليس الرابط الكامل. النظام سيقوم بإنشاء الرابط تلقائيًا.
            </div>
            <img src="https://i.imgur.com/mSla6Wo.png alt="حقل الروابط الخارجية">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٨</div>
            <div class="guide-step-title">
                <i class="fas fa-tags"></i>
                الحالة والتصنيفات
            </div>
            <div class="guide-step-desc">
                حدد حالة الرواية الحالية واختر التصنيفات المناسبة لها. هذه المعلومات تساعد في تنظيم المكتبة وتسهيل عملية البحث.
            </div>
            <ul class="guide-list">
                <li><span class="guide-field">الحالة:</span> اختر من بين (مستمرة، مكتملة، متوقفة، ملغية)</li>
                <li><span class="guide-field">التصنيفات:</span> اختر التصنيفات المناسبة (أكشن، مغامرة، خيال، ...)</li>
            </ul>
            <div class="guide-note">
                <strong>نصيحة:</strong> اختر التصنيفات الأكثر دقة وتجنب اختيار عدد كبير من التصنيفات. التصنيف الدقيق يساعد في وصول الرواية للجمهور المناسب.
            </div>
            <img src="https://i.imgur.com/DzdhfFP.png" alt="حقل الحالة والتصنيفات">
        </div>

        <div class="guide-step">
            <div class="guide-step-number">٩</div>
            <div class="guide-step-title">
                <i class="fas fa-check-circle"></i>
                مراجعة وإرسال
            </div>
            <div class="guide-step-desc">
                بعد الانتهاء من تعبئة جميع المعلومات، راجع البيانات المدخلة للتأكد من دقتها واكتمالها قبل النشر.
            </div>
            <div class="guide-note guide-success">
                <strong>قائمة المراجعة النهائية:</strong>
                <ul class="guide-list">
                    <li>تأكد من صحة العناوين بجميع اللغات</li>
                    <li>تحقق من جودة ملخص القصة وخلوه من الأخطاء</li>
                    <li>تأكد من إضافة المؤلف والرسام بشكل صحيح</li>
                    <li>تحقق من صور أغلفة المجلدات وجودتها</li>
                    <li>تأكد من اختيار التصنيفات المناسبة</li>
                </ul>
            </div>
            <div class="guide-step-desc">
                بعد التأكد من جميع المعلومات، انقر على زر <span class="guide-field">إضافة الرواية</span> لإرسالها للمراجعة أو النشر.
            </div>
        </div>
    </div>

    <div class="guide-summary">
        <div class="guide-summary-title">
            <i class="fas fa-lightbulb"></i>
            نصائح هامة لإضافة رواية ناجحة
        </div>
        <div class="guide-summary-content">
            <ul class="guide-list">
                <li>كلما كانت المعلومات دقيقة وواضحة، زادت فرص ظهور روايتك في نتائج البحث وجذب القراء</li>
                <li>استخدم صورًا عالية الجودة لأغلفة المجلدات لإضفاء مظهر احترافي</li>
                <li>اكتب ملخصًا جذابًا يشجع القراء على متابعة الرواية</li>
                <li>حافظ على تحديث حالة الرواية وإضافة المجلدات الجديدة أولًا بأول</li>
                <li>تأكد من اختيار التصنيفات المناسبة لمساعدة القراء في العثور على روايتك</li>
            </ul>
        </div>
    </div>
</div>

<?php get_footer(); ?>