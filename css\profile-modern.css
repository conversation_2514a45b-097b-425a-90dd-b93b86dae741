/* Modern Profile Page Styles */
:root {
    --profile-bg: #f8f9fa;
    --profile-card-bg: #ffffff;
    --profile-text: #2d3436;
    --profile-text-muted: #6c757d;
    --profile-border: #dee2e6;
    --profile-hover: #e9ecef;
    --profile-primary: #007bff;
    --profile-primary-dark: #0056b3;
    --profile-card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --profile-input-bg: #ffffff;
    --profile-input-border: #ced4da;
    --profile-secondary-bg: #e9ecef;
    --profile-success: #28a745;
    --profile-info: #17a2b8;
}

.dark-mode {
    --profile-bg: #2d3436;
    --profile-card-bg: #343a40;
    --profile-text: #f8f9fa;
    --profile-text-muted: #adb5bd;
    --profile-border: #495057;
    --profile-hover: #495057;
    --profile-input-bg: #343a40;
    --profile-input-border: #495057;
    --profile-secondary-bg: #343a40;
    --profile-card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

/* Profile Container */
.profile-modern {
    padding: 2rem 1rem;
    background-color: var(--profile-bg);
    min-height: 100vh;
}

/* Profile Header */
.profile-header-modern {
    position: relative;
    background: linear-gradient(135deg, var(--profile-primary), var(--profile-info));
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: #fff;
}

.profile-cover-modern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 200px;
    border-radius: 15px 15px 0 0;
    background-size: cover;
    background-position: center;
}

.profile-info-modern {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.profile-avatar-modern {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid #fff;
    box-shadow: var(--profile-card-shadow);
    overflow: hidden;
}

.profile-avatar-modern img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Stats Cards */
.profile-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: var(--profile-card-bg);
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: var(--profile-card-shadow);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--profile-primary);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--profile-text);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--profile-text-muted);
    font-size: 0.9rem;
}

/* Tab System */
.tab-pane-modern {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-pane-modern.active {
    display: block;
    opacity: 1;
}

.tabs-nav-modern {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--profile-border);
    margin-bottom: 2rem;
    overflow-x: auto;
}

.tab-link-modern {
    padding: 0.75rem 1.5rem;
    color: var(--profile-text-muted);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
}

.tab-link-modern:hover {
    background: var(--profile-hover);
    color: var(--profile-primary);
}

.tab-link-modern.active {
    background: var(--profile-primary);
    color: #fff;
}

.tab-link-modern.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--profile-primary);
    border-radius: 2px;
}

/* Content Styling */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.content-card {
    background: var(--profile-card-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--profile-card-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 200px;
}

.content-card-image {
    height: 180px;
    background-size: cover;
    background-position: center;
    border-bottom: 1px solid var(--profile-border);
}

.content-card-body {
    padding: 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.content-card-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.content-card-title a {
    color: var(--profile-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.content-card-title a:hover {
    color: var(--profile-primary);
}

.content-card-meta {
    font-size: 0.9rem;
    color: var(--profile-text-muted);
    display: flex;
    gap: 1rem;
    align-items: center;
}

.content-card-meta i {
    color: var(--profile-primary);
}

/* No Content Message */
.no-content-message {
    text-align: center;
    padding: 3rem;
    background: var(--profile-card-bg);
    border-radius: 12px;
    box-shadow: var(--profile-card-shadow);
}

.no-content-message i {
    font-size: 3rem;
    color: var(--profile-text-muted);
    margin-bottom: 1rem;
}

.no-content-message p {
    color: var(--profile-text-muted);
    font-size: 1.1rem;
    margin: 0;
}

/* Settings Form */
.settings-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-border);
    border-radius: 8px;
    box-shadow: var(--profile-card-shadow);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--profile-text);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--profile-input-border);
    border-radius: 4px;
    background: var(--profile-input-bg);
    color: var(--profile-text);
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--profile-primary);
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--profile-text-muted);
}

.btn-primary {
    background: var(--profile-primary);
    color: #ffffff;
  
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-primary:hover {
    background: var(--profile-primary-dark);
}

/* Dark Mode Adjustments */
.dark-mode .tab-link-modern {
    color: var(--profile-text-muted);
}

.dark-mode .tab-link-modern:hover {
    background: var(--profile-hover);
}

.dark-mode .tab-link-modern.active {
    background: var(--profile-primary);
    color: #fff;
}

.dark-mode .form-group input,
.dark-mode .form-group textarea {
    background: var(--profile-input-bg);
    border-color: var(--profile-input-border);
    color: var(--profile-text);
}

.dark-mode .no-content-message {
    background: var(--profile-card-bg);
}

.dark-mode .content-card-title a {
    color: var(--profile-text);
}

.dark-mode .content-card-title a:hover {
    color: var(--profile-primary);
}

.dark-mode .content-card {
    border: 1px solid var(--profile-border);
}

/* Comments Section */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.comment-card {
    background: var(--profile-card-bg);
    border-radius: 10px;
    padding: 1rem;
    border-left: 3px solid var(--profile-primary);
}

.comment-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.comment-novel {
    color: var(--profile-primary);
    font-weight: 500;
}

.comment-date {
    color: var(--profile-text-muted);
    font-size: 0.9rem;
}

.comment-content {
    color: var(--profile-text);
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-info-modern {
        flex-direction: column;
        text-align: center;
    }
    
    .profile-stats-modern {
        grid-template-columns: 1fr;
    }
    
    .tabs-nav-modern {
        overflow-x: auto;
        white-space: nowrap;
    }
}

/* Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease forwards;
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Avatar Section Styles */
.profile-avatar-section {
    position: relative;
    margin-bottom: 2rem;
    text-align: center;
}

.avatar-container {
    position: relative;
    display: inline-block;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--profile-card-bg);
    box-shadow: var(--profile-card-shadow);
    transition: all 0.3s ease;
}

.btn-update-avatar {
    position: absolute;
    bottom: 0;
    right: 0;
    background: var(--profile-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.btn-update-avatar:hover {
    background: var(--profile-primary-dark);
    transform: scale(1.1);
}

/* Gravatar Modal */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-content {
    background: var(--profile-card-bg);
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    margin: auto;
    position: relative;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal.show .modal-content {
    transform: translateY(0);
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--profile-text-muted);
    cursor: pointer;
    font-size: 1.5rem;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--profile-text);
}

.gravatar-instructions {
    margin-top: 1.5rem;
}

.gravatar-instructions ol {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
}

.gravatar-instructions li {
    margin-bottom: 1rem;
    color: var(--profile-text);
    line-height: 1.5;
}

.gravatar-email {
    background: var(--profile-secondary-bg);
    padding: 0.75rem 1rem;
    border-radius: 6px;
    margin: 1rem 0;
    font-family: monospace;
    color: var(--profile-text);
}

.gravatar-direct-link {
    margin-top: 2rem;
    text-align: center;
}

.btn-gravatar {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--profile-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-gravatar:hover {
    background: var(--profile-primary-dark);
    transform: translateY(-2px);
}

.btn-gravatar i {
    font-size: 1.1em;
}


/* Fix for profile picture modal text visibility in dark mode */
.gravatar-modal {
    color: #333; /* Dark text color that works in both light and dark modes */
}

.gravatar-modal input[type="email"] {
    color: #333;
    background-color: #fff;
}

.gravatar-modal a {
    color: #0073aa; /* WordPress default link color */
}
