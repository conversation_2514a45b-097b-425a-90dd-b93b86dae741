/* ===== أنماط صفحة التصنيف ===== */
:root {
    --taxonomy-primary: var(--taxonomy-color, #3498db);
    --taxonomy-secondary: #2c3e50;
    --taxonomy-accent: #f39c12;
    --taxonomy-bg: #f8f9fa;
    --taxonomy-card: #ffffff;
    --taxonomy-text: #333333;
    --taxonomy-border: #e9ecef;
    --taxonomy-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    --taxonomy-radius: 12px;
    --taxonomy-transition: all 0.3s ease;
}

/* دعم الوضع الداكن */
.dark-mode {
    --taxonomy-bg: #1a1a2e;
    --taxonomy-card: #2d2d2d;
    --taxonomy-text: #e3e3e3;
    --taxonomy-border: #404040;
    --taxonomy-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* ===== قسم الرأس ===== */
.taxonomy-hero {
    background: linear-gradient(135deg, var(--taxonomy-primary) 0%, rgba(52, 152, 219, 0.8) 100%);
    color: #fff;
    padding: 3rem 0;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--taxonomy-shadow);
}

.taxonomy-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.3;
}

.taxonomy-hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.taxonomy-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: inline-block;
    width: 100px;
    height: 100px;
    line-height: 100px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.taxonomy-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.taxonomy-title span {
    display: inline-block;
    position: relative;
}

.taxonomy-title span::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #fff;
    border-radius: 3px;
}

.taxonomy-description {
    max-width: 800px;
    margin: 0 auto 1.5rem;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

.taxonomy-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
}

.taxonomy-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 50px;
}

/* ===== حاوية المحتوى ===== */
.taxonomy-container {
    padding: 2rem 0;
    background-color: var(--taxonomy-bg);
}

/* ===== فلتر الروايات ===== */
.taxonomy-filter {
    background-color: var(--taxonomy-card);
    border-radius: var(--taxonomy-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: var(--taxonomy-shadow);
    border: 1px solid var(--taxonomy-border);
}

.filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.filter-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0;
    color: var(--taxonomy-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-title i {
    color: var(--taxonomy-primary);
}

.filter-count {
    background-color: var(--taxonomy-primary);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* ===== شبكة الروايات ===== */
.novels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1.2rem;
    margin-bottom: 2rem;
}

.novel-item {
    transition: var(--taxonomy-transition);
}

.novel-card {
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: var(--taxonomy-transition);
    height: 100%;
    position: relative;
    aspect-ratio: 0.7;
}

/* تصميم الوضع الفاتح */
.light-mode .novel-card {
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.light-mode .novel-overlay {
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 40%, rgba(0, 0, 0, 0.1) 100%);
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.novel-card-inner {
    position: relative;
    height: 100%;
    width: 100%;
}

.novel-cover {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.novel-cover-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--taxonomy-transition);
}

.novel-card:hover .novel-cover-img {
    transform: scale(1.05);
}

.novel-cover-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #333;
    color: #666;
    font-size: 3rem;
}

.novel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.5) 40%, rgba(0, 0, 0, 0.2) 100%);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 1rem;
    z-index: 1;
}

.novel-info {
    color: #fff;
    z-index: 2;
    text-align: right;
}

.novel-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.novel-title a {
    color: #fff;
    text-decoration: none;
    transition: var(--taxonomy-transition);
}

.novel-title a:hover {
    color: rgba(255, 255, 255, 0.8);
}

.novel-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
}

.novel-date {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.novel-date i {
    font-size: 0.7rem;
}

.novel-chapters {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.novel-chapters i {
    font-size: 0.7rem;
}

/* تصميم الشارة الزرقاء */
.novel-badge {
    position: absolute;
    bottom: 0.5rem;
    left: 0.5rem;
    z-index: 3;
    width: 1.5rem;
    height: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0078ff;
    color: white;
    border-radius: 4px;
    font-size: 0.7rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* تصميم العنوان العربي */
.novel-arabic-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    line-height: 1.3;
    color: #fff;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== ترقيم الصفحات ===== */
.taxonomy-pagination {
    margin-top: 2rem;
}

.taxonomy-pagination .page-numbers {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    margin: 0 0.2rem;
    border-radius: 50%;
    background-color: var(--taxonomy-card);
    color: var(--taxonomy-text);
    text-decoration: none;
    transition: var(--taxonomy-transition);
    border: 1px solid var(--taxonomy-border);
}

.taxonomy-pagination .page-numbers:hover {
    background-color: var(--taxonomy-primary);
    color: #fff;
}

.taxonomy-pagination .page-numbers.current {
    background-color: var(--taxonomy-primary);
    color: #fff;
}

.taxonomy-pagination .page-numbers.prev,
.taxonomy-pagination .page-numbers.next {
    font-size: 0.8rem;
}

/* ===== رسالة عدم وجود روايات ===== */
.no-novels {
    background-color: var(--taxonomy-card);
    border-radius: var(--taxonomy-radius);
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: var(--taxonomy-shadow);
    border: 1px solid var(--taxonomy-border);
}

.no-novels-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1.5rem;
}

.dark-mode .no-novels-icon {
    color: #444;
}

.no-novels-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--taxonomy-text);
}

.no-novels-message {
    color: #777;
    font-size: 1.1rem;
}

/* ===== الشريط الجانبي ===== */
.taxonomy-sidebar {
    position: sticky;
    top: 20px;
}

.sidebar-widget {
    background-color: var(--taxonomy-card);
    border-radius: var(--taxonomy-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--taxonomy-shadow);
    border: 1px solid var(--taxonomy-border);
}

.widget-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.2rem;
    color: var(--taxonomy-text);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.widget-title i {
    color: var(--taxonomy-primary);
}

.related-terms-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.related-terms-list li {
    margin-bottom: 0.8rem;
}

.related-terms-list a {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem 1rem;
    background-color: rgba(52, 152, 219, 0.05);
    border-radius: 8px;
    color: var(--taxonomy-text);
    text-decoration: none;
    transition: var(--taxonomy-transition);
}

.related-terms-list a:hover {
    background-color: var(--taxonomy-primary);
    color: #fff;
}

.term-count {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--taxonomy-primary);
    padding: 0.2rem 0.6rem;
    border-radius: 50px;
    font-size: 0.8rem;
    transition: var(--taxonomy-transition);
}

.related-terms-list a:hover .term-count {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

.popular-novel-item {
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--taxonomy-border);
    padding-bottom: 1rem;
}

.popular-novel-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

.popular-novel-link {
    display: flex;
    gap: 1rem;
    text-decoration: none;
    color: var(--taxonomy-text);
}

.popular-novel-cover {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.popular-novel-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.popular-novel-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #aaa;
    font-size: 1.5rem;
}

.dark-mode .popular-novel-placeholder {
    background-color: #333;
    color: #666;
}

.popular-novel-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.popular-novel-title {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    line-height: 1.4;
    color: var(--taxonomy-text);
}

.popular-novel-link:hover .popular-novel-title {
    color: var(--taxonomy-primary);
}

.popular-novel-rating {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    color: #777;
}

.popular-novel-rating i {
    color: #f1c40f;
}

.no-popular-novels {
    text-align: center;
    padding: 1rem;
    color: #777;
}

/* ===== تحسينات الوضع الداكن ===== */
.dark-mode .taxonomy-hero {
    background: linear-gradient(135deg, var(--taxonomy-primary) 0%, rgba(52, 152, 219, 0.6) 100%);
}

.dark-mode .novel-genre {
    background-color: rgba(52, 152, 219, 0.2);
}

.dark-mode .related-terms-list a {
    background-color: rgba(52, 152, 219, 0.1);
}

.dark-mode .term-count {
    background-color: rgba(52, 152, 219, 0.2);
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 992px) {
    .taxonomy-sidebar {
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .taxonomy-hero {
        padding: 2rem 0;
    }

    .taxonomy-title {
        font-size: 2rem;
    }

    .taxonomy-icon {
        width: 80px;
        height: 80px;
        line-height: 80px;
        font-size: 2.5rem;
    }

    .novels-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .novels-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }

    .novel-title {
        font-size: 0.9rem;
    }

    .novel-arabic-title {
        font-size: 0.8rem;
    }

    .novel-meta {
        font-size: 0.7rem;
    }

    .taxonomy-meta {
        flex-direction: column;
        gap: 1rem;
    }
}
