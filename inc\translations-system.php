<?php
/**
 * نظام ترجمات الفصول - SekaiPLU
 *
 * يوفر هذا الملف وظائف وميزات لإدارة ترجمات متعددة لنفس الفصل
 * ويدعم عرضها بطريقة منظمة دون تداخل
 *
 * @package SekaiPLU
 */

if (!defined('ABSPATH')) {
    exit; // منع الوصول المباشر
}

/**
 * إضافة حقول البيانات الوصفية للفصول
 *
 * @param string $chapter_id معرف الفصل
 * @param array $data بيانات الفصل
 */
function sekaiplu_update_chapter_translation_meta($chapter_id, $data) {
    if (!isset($data['translation_unique_id']) || empty($data['translation_unique_id'])) {
        // إنشاء معرف فريد عالميًا إذا لم يكن موجودًا
        $data['translation_unique_id'] = wp_generate_uuid4();
    }
    
    // تحديث البيانات الوصفية للفصل
    update_post_meta($chapter_id, '_translation_unique_id', $data['translation_unique_id']);
    update_post_meta($chapter_id, '_translation_id', sanitize_text_field($data['translation_id'] ?? ''));
    update_post_meta($chapter_id, '_translation_notes', wp_kses_post($data['translation_notes'] ?? ''));
    update_post_meta($chapter_id, '_translation_status', sanitize_text_field($data['translation_status'] ?? 'published'));
}

/**
 * الحصول على معرف الفصل الأساسي (المرتبط بالفصل الأصلي)
 * 
 * @param int $chapter_id معرف الفصل
 * @return string معرف الفصل الفريد
 */
function sekaiplu_get_chapter_unique_id($chapter_id) {
    $unique_id = get_post_meta($chapter_id, '_chapter_unique_id', true);
    
    // إذا لم يكن هناك معرف فريد، إنشاء واحد جديد
    if (empty($unique_id)) {
        $unique_id = wp_generate_uuid4();
        update_post_meta($chapter_id, '_chapter_unique_id', $unique_id);
    }
    
    return $unique_id;
}

/**
 * ربط ترجمات الفصول ببعضها عبر تسجيل metadata مشتركة
 *
 * @param int $chapter_id معرف الفصل الأصلي
 * @param int $translation_id معرف ترجمة الفصل
 * @return bool نجاح العملية
 */
function sekaiplu_link_chapter_translations($chapter_id, $translation_id) {
    $unique_id = sekaiplu_get_chapter_unique_id($chapter_id);
    
    // تأكد من أن chapter_id و translation_id صالحان
    if (!get_post($chapter_id) || !get_post($translation_id)) {
        return false;
    }
    
    // ربط الترجمة بالفصل الأصلي
    update_post_meta($translation_id, '_chapter_unique_id', $unique_id);
    
    // نسخ البيانات الأساسية من الفصل الأصلي
    $novel_id = get_post_meta($chapter_id, '_novel_id', true);
    $volume_number = get_post_meta($chapter_id, '_volume_number', true);
    $chapter_number = get_post_meta($chapter_id, '_chapter_number', true);
    
    update_post_meta($translation_id, '_novel_id', $novel_id);
    update_post_meta($translation_id, '_volume_number', $volume_number);
    update_post_meta($translation_id, '_chapter_number', $chapter_number);
    
    return true;
}
