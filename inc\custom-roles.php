<?php
/**
 * Custom user roles and capabilities
 */

// Register custom roles on theme activation
function sekai_register_custom_roles() {
    // Add Translator role
    add_role(
        'translator',
        __('Translator', 'sekaiplus'),
        array(
            'read' => true,
            'edit_posts' => true,
            'delete_posts' => true,
            'upload_files' => true,
            'publish_posts' => false,
            'edit_published_posts' => true,
            'delete_published_posts' => false,
            'edit_others_posts' => false,
            'delete_others_posts' => false,
            'manage_categories' => false,
            'moderate_comments' => false,
            'edit_theme_options' => false,
            'manage_options' => false,
            'view_admin_dashboard' => true,
            'edit_private_posts' => true,
            'read_private_posts' => true,
            'edit_draft_posts' => true
        )
    );

    // Modify the default subscriber role to be our Reader role
    $subscriber = get_role('subscriber');
    if ($subscriber) {
        // Remove any extra capabilities if they exist
        $subscriber->remove_cap('edit_posts');
        $subscriber->remove_cap('delete_posts');
        $subscriber->remove_cap('publish_posts');
        $subscriber->remove_cap('upload_files');
    }
}

// Hook for theme activation
add_action('after_setup_theme', 'sekai_register_custom_roles', 10);

// Add filter to force pending status for translator posts
function sekai_force_pending_status($data, $postarr) {
    // u0627u0644u062au062du0642u0642 u0645u0646 u0627u0644u0645u0633u062au062eu062fu0645 u0627u0644u062du0627u0644u064a
    $user = wp_get_current_user();
    $user_roles = (array) $user->roles;
    
    // u0625u0630u0627 u0643u0627u0646 u0627u0644u0645u0633u062au062eu062fu0645 u0645u062au0631u062cu0645u064bu0627
    if (in_array('translator', $user_roles)) {
        // u0627u0644u062au062du0642u0642 u0645u0646 u0623u0646 u0627u0644u0645u0646u0634u0648u0631 u0644u064au0633 u0641u064a u062du0627u0644u0629 u0645u0633u0648u062fu0629 u0623u0648 u0645u0631u0627u062cu0639u0629 u0628u0627u0644u0641u0639u0644
        if ($data['post_status'] != 'draft' && $data['post_status'] != 'pending' && $data['post_status'] != 'auto-draft') {
            // u062au0639u064au064au0646 u062du0627u0644u0629 u0627u0644u0645u0646u0634u0648u0631 u0625u0644u0649 u0628u0627u0646u062au0638u0627u0631 u0627u0644u0645u0631u0627u062cu0639u0629
            $data['post_status'] = 'pending';
        }
    }
    
    return $data;
}
add_filter('wp_insert_post_data', 'sekai_force_pending_status', 10, 2);

// u0625u0636u0627u0641u0629 u0641u0644u062au0631 u0625u0636u0627u0641u064a u0644u0645u0646u0639 u0627u0644u0646u0634u0631 u0627u0644u0645u0628u0627u0634u0631 u0644u0644u0645u062au0631u062cu0645u064au0646
function sekai_prevent_translator_publishing($post_id, $post, $update) {
    // u0627u0644u062au062du0642u0642 u0645u0646 u0627u0644u0645u0633u062au062eu062fu0645 u0627u0644u062du0627u0644u064a
    $user = wp_get_current_user();
    $user_roles = (array) $user->roles;
    
    // u0625u0630u0627 u0643u0627u0646 u0627u0644u0645u0633u062au062eu062fu0645 u0645u062au0631u062cu0645u064bu0627 u0648u0627u0644u0645u0646u0634u0648u0631 u0645u0646u0634u0648u0631
    if (in_array('translator', $user_roles) && $post->post_status === 'publish') {
        // u062au063au064au064au0631 u062du0627u0644u0629 u0627u0644u0645u0646u0634u0648u0631 u0625u0644u0649 u0628u0627u0646u062au0638u0627u0631 u0627u0644u0645u0631u0627u062cu0639u0629
        wp_update_post(array(
            'ID' => $post_id,
            'post_status' => 'pending'
        ));
    }
}
add_action('save_post', 'sekai_prevent_translator_publishing', 99, 3);

// Helper function to get current user role
function get_current_user_role() {
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        $roles = ( array ) $user->roles;
        return $roles[0];
    }
    return false;
}

// Redirect non-admin users to home page when accessing wp-admin
function sekai_restrict_admin_access() {
    if (is_admin() && !current_user_can('manage_options') && !(defined('DOING_AJAX') && DOING_AJAX)) {
        $user = wp_get_current_user();
        if (in_array('translator', (array) $user->roles)) {
            // Allow translators to access specific admin pages
            global $pagenow;
            $allowed_pages = array(
                'post-new.php',
                'post.php',
                'edit.php',
                'upload.php',
                'profile.php',
                'index.php',
                'admin-ajax.php',
                'admin.php',
            );
            // Also allow team system pages specifically
if ($pagenow === 'admin.php' && isset($_GET['page'])) {
    $allowed_admin_pages = array('team-system', 'team-system-add');
    if (in_array($_GET['page'], $allowed_admin_pages)) {
        return; // Allow access to team system pages
    }
}
            if (!in_array($pagenow, $allowed_pages)) {
                wp_safe_redirect(home_url());
                exit;
            }
        } else {
            wp_safe_redirect(home_url());
            exit;
        }
    }
}
add_action('init', 'sekai_restrict_admin_access');

// Add Team System capabilities to translator role
function sekai_add_team_system_capabilities() {
    $translator_role = get_role('translator');
    if ($translator_role) {
        // Add team system capabilities
        $translator_role->add_cap('access_team_system');
        $translator_role->add_cap('create_teams');
        $translator_role->add_cap('join_teams');
        $translator_role->add_cap('edit_own_team');
        $translator_role->add_cap('manage_team_members');
    }
    
    // Also add to other roles
    $roles_with_access = array('administrator', 'editor', 'author');
    foreach ($roles_with_access as $role_name) {
        $role = get_role($role_name);
        if ($role) {
            $role->add_cap('access_team_system');
            $role->add_cap('create_teams');
            $role->add_cap('join_teams');
        }
    }
}
add_action('init', 'sekai_add_team_system_capabilities', 999);

// Customize admin bar for translators
function sekai_customize_admin_bar() {
    if (current_user_can('translator')) {
        global $wp_admin_bar;
        // Remove unnecessary menu items
        $wp_admin_bar->remove_menu('new-content');
        $wp_admin_bar->remove_menu('comments');
        $wp_admin_bar->remove_menu('customize');
        
        // Add custom new post link
        $wp_admin_bar->add_menu(array(
            'id' => 'new-translation',
            'title' => __('New Translation', 'sekaiplus'),
            'href' => admin_url('post-new.php')
        ));
    }
}
add_action('wp_before_admin_bar_render', 'sekai_customize_admin_bar');