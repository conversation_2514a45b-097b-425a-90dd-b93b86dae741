<?php
/**
 * Template Name: Latest Chapters
 */

get_header();
?>

<style>
/* ===== متغيرات CSS ===== */
:root {
    --latest-primary: var(--bs-primary, #0d6efd);
    --latest-secondary: var(--bs-secondary, #6c757d);
    --latest-success: var(--bs-success, #198754);
    --latest-danger: var(--bs-danger, #dc3545);
    --latest-warning: var(--bs-warning, #ffc107);
    --latest-info: var(--bs-info, #0dcaf0);
    --latest-light: var(--bs-light, #f8f9fa);
    --latest-dark: var(--bs-dark, #212529);
    --latest-bg: var(--bs-body-bg, #fff);
    --latest-text: var(--bs-body-color, #212529);
    --latest-border: rgba(0, 0, 0, 0.125);
    --latest-shadow: rgba(0, 0, 0, 0.1);
    --latest-hover-bg: rgba(0, 0, 0, 0.03);
    --latest-transition: all 0.3s ease;
    --latest-gradient-primary: linear-gradient(135deg, #6d8cff, #f49ca0);
    --latest-gradient-hover: linear-gradient(135deg, #4361ee, #ff6b6b);
    --latest-card-radius: 0.75rem;
    --latest-btn-radius: 2rem;
    --latest-cover-radius: 0.5rem;
    --latest-badge-radius: 2rem;
}

body.dark-mode {
    --latest-bg: var(--bs-dark, #212529);
    --latest-text: var(--bs-light, #f8f9fa);
    --latest-border: rgba(255, 255, 255, 0.125);
    --latest-shadow: rgba(0, 0, 0, 0.25);
    --latest-hover-bg: rgba(255, 255, 255, 0.05);
    --latest-gradient-primary: linear-gradient(135deg, #233466, #7c4a4a);
    --latest-gradient-hover: linear-gradient(135deg, #1a1a2e, #16213e);
}

/* ===== تنسيقات الوضع الداكن ===== */
body.dark-mode .latest-navbar {
    background-color: var(--latest-bg);
    border-bottom: 1px solid var(--latest-border);
}

body.dark-mode .latest-nav-title {
    color: var(--latest-text);
}

body.dark-mode .latest-nav-title span {
    color: var(--latest-text);
}

body.dark-mode .latest-nav-btn {
    background-color: var(--latest-bg);
    color: #8ab4f8;
    border-color: #8ab4f8;
}

body.dark-mode .latest-nav-btn:hover {
    background-color: #8ab4f8;
    color: var(--latest-bg);
}

body.dark-mode .latest-content-card {
    background-color: var(--latest-bg);
    border: 1px solid var(--latest-border);
}

body.dark-mode .latest-content-card .card-header {
    background-color: rgba(138, 180, 248, 0.1);
    border-bottom: 1px solid var(--latest-border);
}

body.dark-mode .latest-content-card .card-title {
    color: var(--latest-text);
}

body.dark-mode .latest-content-card .card-title i {
    color: #8ab4f8;
}

body.dark-mode .latest-chapter-card {
    background-color: rgba(255, 255, 255, 0.03);
    border: 1px solid var(--latest-border);
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2);
}

body.dark-mode .latest-chapter-card:hover {
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.3);
    border-color: rgba(138, 180, 248, 0.3);
}

body.dark-mode .latest-chapter-card::before {
    background: linear-gradient(to bottom, #8ab4f8, #4285f4);
}

body.dark-mode .novel-cover-container {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(138, 180, 248, 0.1);
}

body.dark-mode .novel-cover-container:hover {
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.5);
    border-color: rgba(138, 180, 248, 0.3);
}

body.dark-mode .novel-cover-container::before {
    opacity: 0.5;
}

body.dark-mode .novel-cover-img {
    filter: brightness(0.9);
}

body.dark-mode .novel-title {
    color: var(--latest-text);
}

body.dark-mode .novel-link {
    color: #8ab4f8;
}

body.dark-mode .novel-link:hover {
    color: #aecbfa;
}

body.dark-mode .novel-link::after {
    background: linear-gradient(90deg, #4285f4, #8ab4f8);
}

body.dark-mode .chapter-link {
    color: var(--latest-text);
}

body.dark-mode .chapter-link:hover {
    color: #8ab4f8;
}

body.dark-mode .chapter-link::before {
    background-color: #8ab4f8;
}

body.dark-mode .translator-info {
    color: rgba(255, 255, 255, 0.6);
    border-top-color: rgba(255, 255, 255, 0.1);
}

body.dark-mode .translator-avatar {
    border-color: rgba(138, 180, 248, 0.2);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

body.dark-mode .translator-link {
    color: rgba(255, 255, 255, 0.6);
}

body.dark-mode .translator-link:hover {
    color: #8ab4f8;
}

body.dark-mode .translator-link:hover + .translator-avatar {
    border-color: rgba(138, 180, 248, 0.5);
}

body.dark-mode .time-ago {
    background-color: rgba(138, 180, 248, 0.1);
    color: #8ab4f8;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode .latest-loading-spinner {
    border-color: rgba(138, 180, 248, 0.2);
    border-top-color: #8ab4f8;
}

body.dark-mode .no-chapters-message {
    color: rgba(255, 255, 255, 0.6);
}

body.dark-mode .latest-hero {
    background: var(--latest-gradient-primary);
}

body.dark-mode .latest-hero .hero-title {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

body.dark-mode .latest-hero .hero-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

body.dark-mode .latest-hero .hero-illustration i {
    color: rgba(255, 255, 255, 0.2);
}

body.dark-mode .latest-filter-btn {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--latest-text);
    border-color: var(--latest-border);
}

body.dark-mode .latest-filter-btn:hover,
body.dark-mode .latest-filter-btn.active {
    background-color: #8ab4f8;
    color: var(--latest-bg);
    border-color: #8ab4f8;
}

/* ===== شريط التنقل العلوي ===== */
.latest-navbar {
    position: sticky;
    top: 0;
    background-color: var(--latest-bg);
    box-shadow: 0 4px 20px var(--latest-shadow);
    padding: 0.75rem 0;
    z-index: 1000;
    transition: var(--latest-transition);
}

.latest-navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.latest-nav-left,
.latest-nav-right {
    flex: 0 0 auto;
    display: flex;
    gap: 0.5rem;
}

.latest-nav-center {
    flex: 1;
    text-align: center;
    padding: 0 1rem;
}

.latest-nav-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.latest-nav-title .subtitle {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

.latest-nav-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--latest-bg);
    color: var(--latest-primary);
    border: 1px solid var(--latest-primary);
    transition: var(--latest-transition);
    text-decoration: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.latest-nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--latest-gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.latest-nav-btn:hover {
    color: white;
    border-color: transparent;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(var(--bs-primary-rgb), 0.3);
}

.latest-nav-btn:hover::before {
    opacity: 1;
}

.latest-nav-btn.active {
    background-color: var(--latest-primary);
    color: white;
}

/* ===== قسم الهيرو ===== */
.latest-hero {
    background: var(--latest-gradient-primary);
    padding: 5rem 0;
    color: white;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px var(--latest-shadow);
    border-radius: 0 0 var(--latest-card-radius) var(--latest-card-radius);
}

.latest-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 Z" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 100% 100%;
}

.latest-hero::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        rgba(255,255,255,0) 0%,
        rgba(255,255,255,0.3) 50%,
        rgba(255,255,255,0) 100%);
}

.latest-hero .hero-content {
    position: relative;
    z-index: 2;
}

.latest-hero .hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    display: inline-block;
}

.latest-hero .hero-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 4px;
    background: white;
    border-radius: 2px;
}

.latest-hero .hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 80%;
    line-height: 1.6;
    position: relative;
}

.latest-hero .hero-illustration {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    position: relative;
}

.latest-hero .hero-illustration i {
    font-size: 8rem;
    color: rgba(255, 255, 255, 0.3);
    animation: float 3s ease-in-out infinite;
}

.latest-hero .hero-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.latest-hero .hero-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--latest-btn-radius);
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-decoration: none;
    transition: var(--latest-transition);
    font-weight: 500;
    backdrop-filter: blur(5px);
}

.latest-hero .hero-btn:hover {
    background-color: white;
    color: var(--latest-primary);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.latest-hero .hero-btn i {
    margin-left: 0.5rem;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
    100% { transform: translateY(0px); }
}

/* ===== بطاقات الفصول ===== */
.latest-content-card {
    background-color: var(--latest-bg);
    border-radius: var(--latest-card-radius);
    box-shadow: 0 0.5rem 2rem var(--latest-shadow);
    transition: var(--latest-transition);
    border: none;
    overflow: hidden;
    margin-bottom: 2rem;
    position: relative;
}

.latest-content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--latest-gradient-primary);
    z-index: 1;
}

.latest-content-card .card-header {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-bottom: 1px solid var(--latest-border);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.latest-content-card .card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0;
    color: var(--latest-text);
    display: flex;
    align-items: center;
}

.latest-content-card .card-title i {
    margin-left: 0.75rem;
    color: var(--latest-primary);
    font-size: 1.2rem;
}

.latest-content-card .card-body {
    padding: 1.5rem;
}

.latest-filter-bar {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--latest-border);
}

.latest-filter-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: var(--latest-btn-radius);
    background-color: var(--latest-bg);
    color: var(--latest-text);
    border: 1px solid var(--latest-border);
    transition: var(--latest-transition);
    font-size: 0.9rem;
    cursor: pointer;
}

.latest-filter-btn:hover,
.latest-filter-btn.active {
    background-color: var(--latest-primary);
    color: white;
    border-color: var(--latest-primary);
    transform: translateY(-2px);
}

.latest-filter-btn i {
    margin-left: 0.5rem;
    font-size: 0.85rem;
}

.latest-chapter-item {
    margin-bottom: 1.5rem;
}

.latest-chapter-card {
    background-color: var(--latest-bg);
    border-radius: var(--latest-card-radius);
    box-shadow: 0 0.25rem 1rem var(--latest-shadow);
    transition: var(--latest-transition);
    border: 1px solid var(--latest-border);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
}

.latest-chapter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--latest-gradient-primary);
    opacity: 0;
    transition: var(--latest-transition);
}

.latest-chapter-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 2rem var(--latest-shadow);
    border-color: rgba(var(--bs-primary-rgb), 0.3);
}

.latest-chapter-card:hover::before {
    opacity: 1;
}

.latest-chapter-card .card-body {
    padding: 1.25rem;
}

.novel-cover-container {
    flex-shrink: 0;
    width: 70px;
    height: 100px;
    overflow: hidden;
    border-radius: var(--latest-cover-radius);
    box-shadow: 0 0.5rem 1rem var(--latest-shadow);
    transition: var(--latest-transition);
    position: relative;
    transform: perspective(800px) rotateY(0deg);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.novel-cover-link {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
}

.novel-cover-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(0,0,0,0) 30%,
        rgba(255,255,255,0.15) 50%,
        rgba(0,0,0,0) 70%);
    opacity: 0;
    transition: var(--latest-transition);
    z-index: 2;
    pointer-events: none;
}

.novel-cover-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom,
        rgba(0,0,0,0) 50%,
        rgba(0,0,0,0.7) 100%);
    opacity: 0.7;
    transition: var(--latest-transition);
    z-index: 1;
    pointer-events: none;
}

.novel-cover-container:hover {
    transform: perspective(800px) rotateY(5deg) translateY(-5px) scale(1.05);
    box-shadow: 0 1rem 2rem var(--latest-shadow);
    border-color: rgba(var(--bs-primary-rgb), 0.3);
}

.novel-cover-container:hover::after {
    opacity: 1;
    animation: shine 1.5s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) }
    100% { transform: translateX(100%) }
}

.novel-cover-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--latest-transition);
    transform: scale(1);
}

.novel-cover-container:hover .novel-cover-img {
    transform: scale(1.1);
}

.chapter-content {
    flex: 1;
    min-width: 0; /* Prevents text overflow */
    padding-right: 0.5rem;
}

.novel-title {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
}

.novel-link {
    color: var(--latest-primary);
    text-decoration: none;
    transition: var(--latest-transition);
    position: relative;
    display: inline-block;
}

.novel-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--latest-gradient-primary);
    transition: width 0.3s ease;
}

.novel-link:hover {
    color: var(--latest-primary);
}

.novel-link:hover::after {
    width: 100%;
}

.chapter-info {
    margin-bottom: 0.5rem;
    position: relative;
    padding-right: 0.25rem;
}

.chapter-link {
    color: var(--latest-text);
    font-weight: 500;
    text-decoration: none;
    transition: var(--latest-transition);
    position: relative;
    display: inline-block;
    padding: 0.15rem 0;
}

.chapter-link::before {
    content: '';
    position: absolute;
    right: -0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: var(--latest-primary);
    opacity: 0;
    transition: var(--latest-transition);
}

.chapter-link:hover {
    color: var(--latest-primary);
    transform: translateX(-3px);
}

.chapter-link:hover::before {
    opacity: 1;
}

.translator-info {
    color: var(--latest-secondary);
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
    padding-top: 0.5rem;
    border-top: 1px dashed rgba(var(--bs-secondary-rgb), 0.15);
}

.translator-avatar {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    margin-left: 0.5rem;
    object-fit: cover;
    border: 2px solid rgba(var(--bs-primary-rgb), 0.2);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: var(--latest-transition);
}

.translator-link {
    color: var(--latest-secondary);
    text-decoration: none;
    transition: var(--latest-transition);
    font-weight: 500;
}

.translator-link:hover {
    color: var(--latest-primary);
}

.translator-link:hover + .translator-avatar {
    border-color: rgba(var(--bs-primary-rgb), 0.5);
    transform: scale(1.1);
}

.time-ago {
    font-size: 0.8rem;
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--latest-primary);
    padding: 0.35rem 0.75rem;
    border-radius: var(--latest-badge-radius);
    white-space: nowrap;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
    transition: var(--latest-transition);
    font-weight: 500;
    letter-spacing: 0.02em;
}

.time-ago:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.15);
    transform: translateY(-2px);
}

/* ===== قسم التحميل ===== */
.latest-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 3rem 0;
}

.latest-loading-spinner {
    position: relative;
    width: 4rem;
    height: 4rem;
}

.latest-loading-spinner::before,
.latest-loading-spinner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 0.25rem solid transparent;
}

.latest-loading-spinner::before {
    border-top-color: var(--latest-primary);
    border-right-color: var(--latest-primary);
    animation: spinner-1 1.5s linear infinite;
}

.latest-loading-spinner::after {
    border-bottom-color: var(--latest-primary);
    border-left-color: var(--latest-primary);
    animation: spinner-2 1.5s linear infinite 0.3s;
}

.latest-loading-text {
    margin-top: 1.5rem;
    font-size: 1rem;
    color: var(--latest-secondary);
    font-weight: 500;
}

@keyframes spinner-1 {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes spinner-2 {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(-360deg); }
}

.no-chapters-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 4rem 0;
    color: var(--latest-secondary);
}

.no-chapters-message i {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    opacity: 0.5;
}

.no-chapters-message p {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.no-chapters-message .sub-message {
    font-size: 0.9rem;
    opacity: 0.7;
    max-width: 80%;
    margin: 0 auto;
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 991.98px) {
    .latest-hero {
        padding: 4rem 0;
    }

    .latest-hero .hero-title {
        font-size: 2.5rem;
    }

    .latest-hero .hero-subtitle {
        font-size: 1.15rem;
        max-width: 90%;
    }

    .latest-nav-title {
        font-size: 1rem;
    }

    .latest-content-card .card-header {
        padding: 1rem 1.25rem;
    }

    .latest-content-card .card-body {
        padding: 1.25rem;
    }

    .latest-filter-bar {
        gap: 0.4rem;
        margin-bottom: 1.25rem;
    }

    .latest-filter-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 767.98px) {
    .latest-hero {
        padding: 3.5rem 0;
        border-radius: 0 0 calc(var(--latest-card-radius) * 0.75) calc(var(--latest-card-radius) * 0.75);
    }

    .latest-hero .hero-title {
        font-size: 2.25rem;
    }

    .latest-hero .hero-subtitle {
        font-size: 1.1rem;
        max-width: 100%;
    }

    .latest-hero .hero-actions {
        flex-wrap: wrap;
    }

    .latest-content-card {
        border-radius: calc(var(--latest-card-radius) * 0.75);
    }

    .latest-content-card .card-body {
        padding: 1.25rem;
    }

    .latest-chapter-card {
        border-radius: calc(var(--latest-card-radius) * 0.75);
    }

    .novel-cover-container {
        width: 60px;
        height: 85px;
    }

    .latest-loading-spinner {
        width: 3.5rem;
        height: 3.5rem;
    }

    .latest-loading-text {
        font-size: 0.95rem;
    }

    .no-chapters-message i {
        font-size: 2.5rem;
    }

    .no-chapters-message p {
        font-size: 1.1rem;
    }
}

@media (max-width: 575.98px) {
    .latest-navbar {
        padding: 0.6rem 0;
    }

    .latest-nav-btn {
        width: 2.25rem;
        height: 2.25rem;
    }

    .latest-nav-title {
        font-size: 0.9rem;
    }

    .latest-nav-title .subtitle {
        font-size: 0.7rem;
    }

    .latest-hero {
        padding: 2.5rem 0;
        border-radius: 0 0 calc(var(--latest-card-radius) * 0.5) calc(var(--latest-card-radius) * 0.5);
    }

    .latest-hero .hero-title {
        font-size: 1.75rem;
    }

    .latest-hero .hero-title::after {
        width: 60px;
        height: 3px;
        bottom: -8px;
    }

    .latest-hero .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .latest-hero .hero-btn {
        padding: 0.6rem 1.25rem;
        font-size: 0.9rem;
    }

    .latest-content-card {
        border-radius: calc(var(--latest-card-radius) * 0.5);
        margin-bottom: 1.5rem;
    }

    .latest-content-card::before {
        height: 2px;
    }

    .latest-content-card .card-header {
        padding: 0.75rem 1rem;
    }

    .latest-content-card .card-title {
        font-size: 1.1rem;
    }

    .latest-content-card .card-body {
        padding: 1rem;
    }

    .latest-filter-bar {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .latest-filter-btn {
        padding: 0.35rem 0.7rem;
        font-size: 0.8rem;
    }

    .latest-chapter-card {
        border-radius: calc(var(--latest-card-radius) * 0.5);
    }

    .latest-chapter-card .card-body {
        padding: 1rem;
    }

    .novel-cover-container {
        width: 50px;
        height: 75px;
        border-width: 1px;
    }

    .novel-title {
        font-size: 0.95rem;
    }

    .chapter-info {
        font-size: 0.85rem;
    }

    .chapter-link::before {
        width: 3px;
        height: 3px;
        right: -0.4rem;
    }

    .translator-info {
        font-size: 0.75rem;
        margin-top: 0.5rem;
        padding-top: 0.4rem;
    }

    .translator-avatar {
        width: 18px;
        height: 18px;
        border-width: 1px;
    }

    .time-ago {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }

    .latest-loading {
        padding: 2rem 0;
    }

    .latest-loading-spinner {
        width: 3rem;
        height: 3rem;
    }

    .latest-loading-text {
        font-size: 0.9rem;
        margin-top: 1rem;
    }

    .no-chapters-message {
        padding: 2.5rem 0;
    }

    .no-chapters-message i {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .no-chapters-message p {
        font-size: 1rem;
    }

    .no-chapters-message .sub-message {
        font-size: 0.8rem;
        max-width: 90%;
    }
}
</style>

<!-- شريط التنقل العلوي الثابت -->
<div class="latest-navbar">
    <div class="container">
        <div class="latest-navbar-content">
            <div class="latest-nav-left">
                <a href="<?php echo home_url(); ?>" class="latest-nav-btn" title="العودة للرئيسية">
                    <i class="fas fa-home"></i>
                </a>
                <a href="<?php echo home_url('/novels'); ?>" class="latest-nav-btn" title="قائمة الروايات">
                    <i class="fas fa-book"></i>
                </a>
            </div>

            <div class="latest-nav-center">
                <h1 class="latest-nav-title">
                    <span>آخر الفصول المضافة</span>
                    <span class="subtitle">أحدث الترجمات من مترجمينا</span>
                </h1>
            </div>

            <div class="latest-nav-right">
                <!-- Dark mode toggle removed - using the main header toggle instead -->
            </div>
        </div>
    </div>
</div>

<!-- قسم الهيرو -->
<div class="latest-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">آخر الفصول المضافة</h1>
                    <p class="hero-subtitle">تصفح أحدث الفصول المترجمة من رواياتك المفضلة وتابع المترجمين المفضلين لديك</p>

                    <div class="hero-actions">
                        <a href="<?php echo home_url('/novels'); ?>" class="hero-btn">
                            <span>استعرض الروايات</span>
                            <i class="fas fa-book"></i>
                        </a>
                        <?php if (is_user_logged_in()): ?>
                        <a href="<?php echo home_url('/bookmarks'); ?>" class="hero-btn">
                            <span>المفضلة</span>
                            <i class="fas fa-bookmark"></i>
                        </a>
                        <?php else: ?>
                        <a href="<?php echo home_url('/login'); ?>" class="hero-btn">
                            <span>تسجيل الدخول</span>
                            <i class="fas fa-sign-in-alt"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 d-none d-lg-block">
                <div class="hero-illustration">
                    <i class="fas fa-book-open"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قسم الفصول -->
<div class="container py-4">
    <div class="latest-content-card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-list-ul"></i>
                أحدث الفصول
            </h5>
        </div>
        <div class="card-body">
            <div class="latest-filter-bar">
                <button type="button" class="latest-filter-btn active" data-filter="all">
                    <i class="fas fa-globe"></i>
                    <span>الكل</span>
                </button>
                <button type="button" class="latest-filter-btn" data-filter="today">
                    <i class="fas fa-calendar-day"></i>
                    <span>اليوم</span>
                </button>
                <button type="button" class="latest-filter-btn" data-filter="week">
                    <i class="fas fa-calendar-week"></i>
                    <span>هذا الأسبوع</span>
                </button>
                <?php if (is_user_logged_in()): ?>
                    <button type="button" class="latest-filter-btn" data-filter="following">
                        <i class="fas fa-user-friends"></i>
                        <span>الأكثر متابعة</span>
                    </button>
                    <button type="button" class="latest-filter-btn" data-filter="bookmarked">
                        <i class="fas fa-bookmark"></i>
                        <span>المفضلة</span>
                    </button>
                <?php endif; ?>
            </div>

            <div id="latest-chapters">
                <!-- سيتم تحديث هذا القسم تلقائياً -->
                <div class="latest-loading">
                    <div class="latest-loading-spinner"></div>
                    <div class="latest-loading-text">جاري تحميل الفصول...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قالب الفصل -->
<template id="chapter-template">
    <div class="latest-chapter-item">
        <div class="latest-chapter-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="d-flex">
                        <div class="novel-cover-container me-3">
                            <a href="" class="novel-cover-link">
                                <img src="" alt="غلاف الرواية" class="novel-cover-img">
                            </a>
                        </div>
                        <div class="chapter-content">
                            <h5 class="novel-title mb-1">
                                <a href="" class="novel-link"></a>
                            </h5>
                            <div class="chapter-info">
                                <a href="" class="chapter-link"></a>
                            </div>
                            <div class="translator-info mt-2">
                                <img src="" alt="" class="translator-avatar">
                                <a href="" class="translator-link"></a>
                            </div>
                        </div>
                    </div>
                    <span class="time-ago"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<?php
// Enqueue the latest chapters script
wp_enqueue_script(
    'sekaiplus-latest-chapters',
    get_template_directory_uri() . '/js/latest-chapters.js',
    array('jquery'),
    SEKAIPLUS_VERSION,
    true
);

// Localize the script with site URL and nonce
wp_localize_script('sekaiplus-latest-chapters', 'sekaiplus_latest', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('sekaiplus_latest_chapters'),
    'default_cover' => get_template_directory_uri() . '/assets/images/default-cover.png'
));

get_footer();
?>
