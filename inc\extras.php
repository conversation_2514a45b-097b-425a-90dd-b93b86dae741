<?php
/**
 * Extra functions and utilities
 */

/**
 * Add custom user roles
 */
function sekaiplus_add_roles() {
    add_role('translator', __('مترجم', 'sekaiplus'), array(
        'read' => true,
        'edit_posts' => true,
        'upload_files' => true,
        'translate_novels' => true,
        'publish_chapters' => true
    ));

    add_role('proofreader', __('مدقق', 'sekaiplus'), array(
        'read' => true,
        'edit_posts' => true,
        'edit_others_posts' => true,
        'proofread_chapters' => true
    ));
}
add_action('after_switch_theme', 'sekaiplus_add_roles');

/**
 * Remove custom roles on theme deactivation
 */
function sekaiplus_remove_roles() {
    remove_role('translator');
    remove_role('proofreader');
}
add_action('switch_theme', 'sekaiplus_remove_roles');

/**
 * Add custom capabilities
 */
function sekaiplus_add_caps() {
    $admin = get_role('administrator');
    $admin->add_cap('translate_novels');
    $admin->add_cap('publish_chapters');
    $admin->add_cap('proofread_chapters');
}
add_action('admin_init', 'sekaiplus_add_caps');

/**
 * Rate limiting for chapter views
 */
function sekaiplus_rate_limit_check($action, $limit = 60) {
    if (!is_user_logged_in()) {
        return true;
    }

    $user_id = get_current_user_id();
    $key = 'rate_limit_' . $action . '_' . $user_id;
    $last_time = get_transient($key);

    if ($last_time) {
        $time_passed = time() - $last_time;
        if ($time_passed < $limit) {
            return false;
        }
    }

    set_transient($key, time(), $limit);
    return true;
}

/**
 * Format number with Arabic numerals
 */
function sekaiplus_format_number($number) {
    $arabic = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
    $english = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
    return str_replace($english, $arabic, number_format($number));
}

/**
 * Get user reading progress
 */
function sekaiplus_get_reading_progress($novel_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    if (!$user_id) {
        return 0;
    }

    $total_chapters = sekaiplus_get_chapter_count($novel_id);
    if ($total_chapters === 0) {
        return 0;
    }

    $read_chapters = get_user_meta($user_id, 'read_chapters_' . $novel_id, true);
    if (!$read_chapters) {
        return 0;
    }

    $read_count = count(explode(',', $read_chapters));
    return round(($read_count / $total_chapters) * 100);
}

/**
 * Mark chapter as read
 */
function sekaiplus_mark_chapter_read($chapter_id, $user_id = null) {
    if (!$user_id) {
        $user_id = get_current_user_id();
    }

    if (!$user_id) {
        return false;
    }

    $novel_id = wp_get_post_parent_id($chapter_id);
    if (!$novel_id) {
        return false;
    }

    $read_chapters = get_user_meta($user_id, 'read_chapters_' . $novel_id, true);
    $read_array = $read_chapters ? explode(',', $read_chapters) : array();

    if (!in_array($chapter_id, $read_array)) {
        $read_array[] = $chapter_id;
        update_user_meta($user_id, 'read_chapters_' . $novel_id, implode(',', $read_array));

        // Update chapter views count
        $views = (int) get_post_meta($chapter_id, '_views_count', true);
        update_post_meta($chapter_id, '_views_count', $views + 1);
    }

    return true;
}

/**
 * Update novel views count
 */
function sekaiplus_update_novel_views($novel_id) {
    if (!is_singular('novel')) {
        return;
    }

    $views = (int) get_post_meta($novel_id, '_views_count', true);
    update_post_meta($novel_id, '_views_count', $views + 1);
}
add_action('wp', 'sekaiplus_update_novel_views');

/**
 * Get reading time estimate
 */
function sekaiplus_reading_time($content) {
    $words = str_word_count(strip_tags($content));
    $minutes = ceil($words / 200); // Average reading speed
    return sprintf(_n('%d دقيقة', '%d دقائق', $minutes, 'sekaiplus'), $minutes);
}

/**
 * Clean up chapter content
 */
function sekaiplus_clean_chapter_content($content) {
    // Remove multiple line breaks
    $content = preg_replace("/[\r\n]+/", "\n\n", $content);
    
    // Clean up spaces
    $content = preg_replace('/ +/', ' ', $content);
    
    // Add paragraph breaks
    $content = wpautop($content);
    
    return $content;
}
add_filter('the_content', 'sekaiplus_clean_chapter_content', 9);

/**
 * Add body classes
 */
function sekaiplus_body_classes($classes) {
    if (is_rtl()) {
        $classes[] = 'rtl';
    }

    if (get_theme_mod('sekaiplus_dark_mode_default', false)) {
        $classes[] = 'dark-mode';
    }

    return $classes;
}
add_filter('body_class', 'sekaiplus_body_classes');

/**
 * Add async/defer attributes to scripts
 */
function sekaiplus_script_loader_tag($tag, $handle) {
    $scripts_to_defer = array('sekaiplus-scripts');
    $scripts_to_async = array('');

    if (in_array($handle, $scripts_to_defer)) {
        return str_replace(' src', ' defer src', $tag);
    }

    if (in_array($handle, $scripts_to_async)) {
        return str_replace(' src', ' async src', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'sekaiplus_script_loader_tag', 10, 2);
