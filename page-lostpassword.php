<?php
/**
 * Template Name: Lost Password Page
 */

// Redirect if user is logged in
if (is_user_logged_in()) {
    wp_redirect(home_url());
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['lostpassword_nonce']) && wp_verify_nonce($_POST['lostpassword_nonce'], 'lostpassword_request')) {
    $user_login = sanitize_text_field($_POST['user_login']);
    $turnstile_token = isset($_POST['cf-turnstile-response']) ? $_POST['cf-turnstile-response'] : '';
    
    if (empty($user_login)) {
        $error = 'الرجاء إدخال اسم المستخدم أو البريد الإلكتروني';
    } elseif (empty($turnstile_token)) {
        $error = 'الرجاء التحقق من أنك لست روبوت';
    } elseif (!sekaiplus_verify_turnstile($turnstile_token)) {
        $error = 'فشل التحقق من أنك لست روبوت. الرجاء المحاولة مرة أخرى';
    } else {
        // Check if it's an email or username
        if (is_email($user_login)) {
            $user_data = get_user_by('email', $user_login);
        } else {
            $user_data = get_user_by('login', $user_login);
        }
        
        if (!$user_data) {
            $error = 'لا يوجد مستخدم مسجل بهذا البريد الإلكتروني أو اسم المستخدم';
        } else {
            // Generate reset key
            $key = get_password_reset_key($user_data);
            if (is_wp_error($key)) {
                $error = 'حدث خطأ أثناء إنشاء رابط إعادة تعيين كلمة المرور. الرجاء المحاولة مرة أخرى';
            } else {
                // Get the reset page URL
                $reset_page = get_page_by_path('lostpassword');
                $reset_page_url = $reset_page ? get_permalink($reset_page->ID) : wp_login_url();
                
                // Build reset link
                $reset_url = add_query_arg(array(
                    'action' => 'rp',
                    'key' => $key,
                    'login' => rawurlencode($user_data->user_login)
                ), $reset_page_url);
                
                // Email content
                $site_name = get_bloginfo('name');
                $site_url = get_bloginfo('url');
                $logo_url = get_template_directory_uri() . '/img/logo.png'; // Update with your logo path
                
                // Create a modern HTML email template
                $message = '<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #6b73ff 0%, #000dff 100%);
        }
        .header img {
            max-width: 180px;
            height: auto;
        }
        .content {
            padding: 40px 30px;
            text-align: center;
        }
        h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 24px;
        }
        p {
            margin-bottom: 15px;
            font-size: 16px;
            color: #555;
        }
        .button {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #6b73ff 0%, #000dff 100%);
            color: #fff !important;
            text-decoration: none;
            border-radius: 50px;
            font-weight: bold;
            margin: 25px 0;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }
        .user-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .footer {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            color: #888;
            font-size: 14px;
        }
        .divider {
            height: 1px;
            background-color: #eee;
            margin: 20px 0;
        }
        .reset-link {
            word-break: break-all;
            font-size: 14px;
            color: #666;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="' . $logo_url . '" alt="' . $site_name . '">
        </div>
        <div class="content">
            <h2>طلب إعادة تعيين كلمة المرور</h2>
            <p>مرحباً ' . $user_data->display_name . '،</p>
            <p>لقد تلقينا طلباً لإعادة تعيين كلمة المرور للحساب الخاص بك.</p>
            
            <div class="user-info">
                <p><strong>اسم المستخدم:</strong> ' . $user_data->user_login . '</p>
            </div>
            
            <p>إذا كنت لم تطلب إعادة تعيين كلمة المرور، يمكنك تجاهل هذا البريد الإلكتروني ولن يحدث أي شيء.</p>
            <p>لإعادة تعيين كلمة المرور الخاصة بك، انقر على الزر أدناه:</p>
            
            <a href="' . $reset_url . '" class="button">إعادة تعيين كلمة المرور</a>
            
            <div class="divider"></div>
            
            <p>أو يمكنك نسخ الرابط التالي ولصقه في متصفحك:</p>
            <div class="reset-link">' . $reset_url . '</div>
        </div>
        <div class="footer">
            <p>&copy; ' . date('Y') . ' ' . $site_name . ' - جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>';
                
                $title = sprintf('[%s] طلب إعادة تعيين كلمة المرور', $site_name);
                
                // Set content type to HTML
                add_filter('wp_mail_content_type', function() {
                    return 'text/html';
                });
                
                // Send email
                if (wp_mail($user_data->user_email, $title, $message)) {
                    $success = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
                } else {
                    $error = 'حدث خطأ أثناء إرسال البريد الإلكتروني. الرجاء المحاولة مرة أخرى';
                }
                
                // Reset content type
                remove_filter('wp_mail_content_type', function() {
                    return 'text/html';
                });
            }
        }
    }
}

// Handle password reset
if (isset($_GET['action']) && $_GET['action'] === 'rp' && isset($_GET['key']) && isset($_GET['login'])) {
    $user_login = sanitize_text_field($_GET['login']);
    $key = sanitize_text_field($_GET['key']);
    $user = get_user_by('login', $user_login);
    
    if (!$user || is_wp_error($user)) {
        $error = 'المستخدم غير موجود';
    } else {
        $check_key = check_password_reset_key($key, $user_login);
        
        if (is_wp_error($check_key)) {
            $error = 'رابط إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية. الرجاء طلب رابط جديد.';
        } else {
            // Show reset password form
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['reset_password_nonce']) && wp_verify_nonce($_POST['reset_password_nonce'], 'reset_password')) {
                $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
                $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
                $turnstile_token = isset($_POST['cf-turnstile-response']) ? $_POST['cf-turnstile-response'] : '';
                
                if (empty($new_password)) {
                    $error = 'الرجاء إدخال كلمة مرور جديدة';
                } elseif ($new_password !== $confirm_password) {
                    $error = 'كلمات المرور غير متطابقة';
                } elseif (strlen($new_password) < 6) {
                    $error = 'كلمة المرور يجب أن تكون على الأقل 6 أحرف';
                } elseif (empty($turnstile_token)) {
                    $error = 'الرجاء التحقق من أنك لست روبوت';
                } elseif (!sekaiplus_verify_turnstile($turnstile_token)) {
                    $error = 'فشل التحقق من أنك لست روبوت. الرجاء المحاولة مرة أخرى';
                } else {
                    // Reset password using WordPress API
                    wp_set_password($new_password, $user->ID);
                    
                    // Get login page URL
                    $login_page = get_page_by_path('login');
                    $login_url = $login_page ? get_permalink($login_page->ID) : wp_login_url();
                    
                    // Set success message in session
                    $success = 'تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.';
                }
            }
        }
    }
}

get_header();
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-4">
                    <?php if (isset($_GET['action']) && $_GET['action'] === 'rp' && !is_wp_error($check_key ?? null)): ?>
                        <h2 class="text-center mb-4">إعادة تعيين كلمة المرور</h2>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo esc_html($error); ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo esc_html($success); ?></div>
                            <div class="text-center mt-3">
                                <a href="<?php echo esc_url($login_url); ?>" class="btn btn-primary">تسجيل الدخول</a>
                            </div>
                        <?php else: ?>
                        <form method="POST" action="">
                            <?php wp_nonce_field('reset_password', 'reset_password_nonce'); ?>
                            
                            <div class="mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            
                            <div class="cf-turnstile" data-sitekey="<?php echo sekaiplus_turnstile_site_key(); ?>"></div>
                            <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">إعادة تعيين كلمة المرور</button>
                            </div>
                        </form>
                        <?php endif; ?>
                    <?php else: ?>
                        <h2 class="text-center mb-4">استعادة كلمة المرور</h2>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?php echo esc_html($error); ?></div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success"><?php echo esc_html($success); ?></div>
                        <?php endif; ?>
                        
                        <p class="mb-4">الرجاء إدخال اسم المستخدم أو البريد الإلكتروني الخاص بك. ستتلقى رابطًا لإنشاء كلمة مرور جديدة عبر البريد الإلكتروني.</p>
                        
                        <form method="POST" action="">
                            <?php wp_nonce_field('lostpassword_request', 'lostpassword_nonce'); ?>
                            
                            <div class="mb-3">
                                <label for="user_login" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                                <input type="text" class="form-control" id="user_login" name="user_login" required>
                            </div>
                            
                            <div class="cf-turnstile" data-sitekey="<?php echo sekaiplus_turnstile_site_key(); ?>"></div>
                            <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">استعادة كلمة المرور</button>
                            </div>
                        </form>
                    <?php endif; ?>
                    
                    <div class="text-center mt-3">
                        <?php 
                        $login_page = get_page_by_path('login');
                        $register_page = get_page_by_path('register');
                        $login_url = $login_page ? get_permalink($login_page->ID) : wp_login_url();
                        $register_url = $register_page ? get_permalink($register_page->ID) : wp_registration_url();
                        ?>
                        <p class="mb-0">
                            <a href="<?php echo esc_url($login_url); ?>">تسجيل الدخول</a> | 
                            <a href="<?php echo esc_url($register_url); ?>">إنشاء حساب جديد</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
