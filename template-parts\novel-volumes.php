<?php
global $post;
$novel_id = $post->ID;

// الحصول على معرف الرواية الفريد
$novel_unique_id = get_post_meta($novel_id, '_novel_unique_id', true);

// الحصول على أغلفة المجلدات من الرواية
$volume_covers = get_post_meta($novel_id, '_volume_covers', true);
if (!is_array($volume_covers)) {
    $volume_covers = array();
}

// الحصول على الفصول المرتبطة بالرواية
$chapters = get_posts(array(
    'post_type' => 'chapter',
    'posts_per_page' => -1,
    'meta_query' => array(
        array(
            'key' => '_novel_id',
            'value' => $novel_id
        )
    )
));

// تنظيم الفصول حسب المجلدات
$volume_chapters = array();
foreach ($chapters as $chapter) {
    $volume_number = get_post_meta($chapter->ID, '_volume_number', true);
    if (empty($volume_number)) $volume_number = '1';

    if (!isset($volume_chapters[$volume_number])) {
        $volume_chapters[$volume_number] = array();
    }
    $volume_chapters[$volume_number][] = $chapter;
}

// دمج بيانات الأغلفة مع الفصول
$volumes = array();
foreach ($volume_covers as $index => $cover) {
    $volume_number = $index + 1;
    $volumes[$volume_number] = array(
        'number' => $volume_number,
        'image' => isset($cover['image']) ? $cover['image'] : '',
        'has_chapters' => isset($volume_chapters[$volume_number])
    );

    if (isset($volume_chapters[$volume_number])) {
        $volumes[$volume_number]['chapters'] = $volume_chapters[$volume_number];
    }
}

// إضافة المجلدات التي لها فصول ولكن ليس لها غلاف
foreach ($volume_chapters as $volume_number => $chapters) {
    if (!isset($volumes[$volume_number])) {
        $volumes[$volume_number] = array(
            'number' => $volume_number,
            'image' => '',
            'chapters' => $chapters,
            'has_chapters' => true
        );
    }
}

// ترتيب المجلدات تصاعدياً
ksort($volumes);

if (!empty($volumes) || !empty($volume_covers)): ?>
<div class="volumes-container">
    <div class="volume-grid-scrollable">
        <div class="volume-grid">
            <?php
            // إضافة مؤشر للتمرير إذا كان عدد المجلدات أكثر من 4
            $volumes_count = count($volumes);
            $show_scroll_indicator = $volumes_count > 4;
            ?>
            <?php foreach ($volumes as $volume_number => $volume):
                $cover_image = !empty($volume['image']) ? $volume['image'] : get_the_post_thumbnail_url($novel_id, 'full');
                // التحقق من وجود فصول في المجلد
                $has_chapters = !empty($volume['has_chapters']) && $volume['has_chapters'];
                ?>
                <div class="volume-item">
                    <?php if ($has_chapters): ?>
                        <a href="<?php echo esc_url(home_url('/novel/?r=' . $novel_unique_id . '/' . $volume_number)); ?>"
                           class="volume-link">
                    <?php else: ?>
                        <div class="volume-wrapper">
                    <?php endif; ?>
                        <div class="volume-cover">
                            <img src="<?php echo esc_url($cover_image); ?>"
                                 alt="المجلد <?php echo esc_attr($volume_number); ?>"
                                 class="img-fluid">
                            <?php if ($has_chapters): ?>
                                <div class="volume-hover-overlay">
                                    <span class="view-chapters">عرض الفصول</span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="volume-info">
                            <div class="volume-number">المجلد <?php echo esc_html($volume_number); ?></div>
                            <?php if ($has_chapters):
                                // جمع الفصول حسب رقم الفصل لعدم تكرار الفصول
                                $unique_chapters = [];
                                foreach ($volume['chapters'] as $chapter) {
                                    $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                    if (!isset($unique_chapters[$chapter_number])) {
                                        $unique_chapters[$chapter_number] = true;
                                    }
                                }
                                $unique_chapter_count = count($unique_chapters);
                            ?>
                                <div class="chapter-count">
                                    <i class="fas fa-book-open"></i>
                                    <span><?php echo $unique_chapter_count; ?> فصل</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php if ($has_chapters): ?>
                        </a>
                    <?php else: ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php if ($show_scroll_indicator && $volumes_count > 4): ?>
    <div class="scroll-indicator" id="volumes-scroll-indicator">
        <i class="fas fa-chevron-down"></i>
        <span>المزيد من المجلدات</span>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        var scrollIndicator = document.getElementById('volumes-scroll-indicator');
        var volumeScroller = document.querySelector('.volume-grid-scrollable');

        if (scrollIndicator && volumeScroller) {
            scrollIndicator.addEventListener('click', function() {
                // التمرير لأسفل بسلاسة
                volumeScroller.scrollBy({
                    top: 200,
                    behavior: 'smooth'
                });
            });

            // إخفاء المؤشر عند التمرير للأسفل
            volumeScroller.addEventListener('scroll', function() {
                var maxScroll = volumeScroller.scrollHeight - volumeScroller.clientHeight;
                if (volumeScroller.scrollTop > maxScroll * 0.7) {
                    scrollIndicator.style.opacity = '0';
                } else {
                    scrollIndicator.style.opacity = '1';
                }
            });
        }
    });
    </script>
    <?php endif; ?>
</div>

<style>
/* ===== أنماط المجلدات ===== */
.volumes-container {
    width: 100%;
}

.volume-grid-scrollable {
    max-height: 340px; /* ارتفاع يكفي لعرض صفين من المجلدات (4 مجلدات) */
    overflow-y: auto;
    padding-right: 5px; /* مساحة للتمرير */
    scrollbar-width: thin; /* للمتصفحات التي تدعم هذه الخاصية */
    margin-bottom: 5px; /* مساحة أسفل المربع */
}

/* تخصيص شريط التمرير */
.volume-grid-scrollable::-webkit-scrollbar {
    width: 6px;
}

.volume-grid-scrollable::-webkit-scrollbar-track {
    background: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 10px;
}

.volume-grid-scrollable::-webkit-scrollbar-thumb {
    background: rgba(var(--bs-primary-rgb), 0.3);
    border-radius: 10px;
}

.volume-grid-scrollable::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--bs-primary-rgb), 0.5);
}

.volume-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.25rem;
    margin-top: 2%;
    gap: 3%;
}

.volume-item {
    position: relative;
    transition: var(--novel-transition);
}

.volume-link,
.volume-wrapper {
    display: block;
    text-decoration: none;
    color: var(--novel-text);
    height: 100%;
}

.volume-cover {
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 0.25rem 0.75rem var(--novel-shadow);
    margin-bottom: 0.75rem;
    aspect-ratio: 3/4;
}

.volume-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--novel-transition);
}

.volume-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(var(--bs-primary-rgb), 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--novel-transition);
}

.view-chapters {
    color: white;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border: 2px solid white;
    border-radius: 2rem;
    font-size: 0.9rem;
}

.volume-link:hover .volume-hover-overlay {
    opacity: 1;
}

.volume-link:hover .volume-cover img {
    transform: scale(1.05);
}

.volume-info {
    text-align: center;
}

.volume-number {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: var(--novel-text);
}

.chapter-count {
    font-size: 0.85rem;
    color: var(--novel-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.35rem;
}

/* مؤشر التمرير */
.scroll-indicator {
    text-align: center;
    padding: 0.5rem;
    color: var(--novel-secondary);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounce 1.5s infinite;
    cursor: pointer;
    transition: color 0.3s, opacity 0.5s, background-color 0.3s;
    opacity: 1;
    border-radius: 0.5rem;
}

.scroll-indicator:hover {
    color: var(--novel-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

/* دعم الوضع الداكن */
.dark-mode .scroll-indicator {
    color: rgba(255, 255, 255, 0.7);
    background-color: transparent;
    border: 1px solid transparent;
}

.dark-mode .scroll-indicator:hover {
    color: var(--novel-primary);
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(var(--bs-primary-rgb), 0.2);
    box-shadow: 0 0 10px rgba(var(--bs-primary-rgb), 0.1);
}

.scroll-indicator i {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(3px);
    }
}

/* تأثير الحركة في الوضع الداكن */
.dark-mode .scroll-indicator {
    animation: bounce-dark 1.5s infinite;
}

@keyframes bounce-dark {
    0%, 100% {
        transform: translateY(0);
        text-shadow: 0 0 0 rgba(var(--bs-primary-rgb), 0);
    }
    50% {
        transform: translateY(3px);
        text-shadow: 0 0 5px rgba(var(--bs-primary-rgb), 0.3);
    }
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 767.98px) {
    .volume-grid-scrollable {
        max-height: 320px; /* تقليل الارتفاع قليلاً للشاشات المتوسطة */
    }

    .volume-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 1rem;
    }

    .volume-number {
        font-size: 0.9rem;
    }

    .chapter-count {
        font-size: 0.8rem;
    }
}

@media (max-width: 575.98px) {
    .volume-grid-scrollable {
        max-height: 300px; /* تقليل الارتفاع أكثر للشاشات الصغيرة */
    }

    .volume-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
}
</style>
<?php endif; ?>
