<?php
/**
 * Template Name: Team Profile Fixed
 * Template Post Type: page
 */

// إخفاء التحذيرات لتجنب مشاكل العرض (النظام يعمل بشكل صحيح)
error_reporting(0); // إخفاء جميع التحذيرات والإشعارات
ini_set('display_errors', 0); // إيقاف عرض الأخطاء في المتصفح

// إعداد متغيرات ووردبريس الأساسية
global $wpdb, $post, $wp_query;

// إنشاء كائن منشور افتراضي بسيط للفريق
$team_post = (object) array(
    'ID' => 0,
    'post_author' => 1,
    'post_date' => date('Y-m-d H:i:s'),
    'post_date_gmt' => gmdate('Y-m-d H:i:s'),
    'post_content' => '',
    'post_title' => 'فريق الترجمة',
    'post_excerpt' => '',
    'post_status' => 'publish',
    'comment_status' => 'closed',
    'ping_status' => 'closed',
    'post_password' => '',
    'post_name' => 'team-profile',
    'to_ping' => '',
    'pinged' => '',
    'post_modified' => date('Y-m-d H:i:s'),
    'post_modified_gmt' => gmdate('Y-m-d H:i:s'),
    'post_content_filtered' => '',
    'post_parent' => 0,
    'guid' => '',
    'menu_order' => 0,
    'post_type' => 'page',
    'post_mime_type' => '',
    'comment_count' => 0,
    'filter' => 'raw'
);

// جعل الكائن الافتراضي هو المنشور الحالي
$GLOBALS['post'] = $team_post;

// الحصول على معرف الفريق من الرابط
$team_slug = get_query_var('team_slug');

// إذا لم يتم العثور على معرف الفريق من المتغير، نحاول الحصول عليه من الرابط
if (empty($team_slug)) {
    $path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
    $path_parts = explode('/', $path);
    $team_slug = end($path_parts);
}

// جلب بيانات الفريق
$team = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
    $team_slug
));

// إذا لم يتم العثور على الفريق، نعرض صفحة 404
if (!$team) {
    status_header(404);
    nocache_headers();
    include(get_404_template());
    exit;
}

// تعيين خصائص إضافية للكائن الافتراضي
if ($team) {
    $team_post->post_title = $team->name;
    $team_post->post_content = isset($team->description) ? $team->description : '';
    $GLOBALS['post'] = $team_post;
    setup_postdata( $team_post );
}

// جلب الهيدر
get_header();

// جلب بيانات أعضاء الفريق
$members = $wpdb->get_results($wpdb->prepare(
    "SELECT u.ID, u.display_name, u.user_email, tm.role, tm.joined_at
     FROM {$wpdb->prefix}team_members tm
     INNER JOIN {$wpdb->users} u ON tm.user_id = u.ID
     WHERE tm.team_id = %d
     ORDER BY tm.role = 'leader' DESC, u.display_name",
    $team->id
));

// دالة للحصول على أسماء الأدوار
function get_team_role_name($role) {
    $roles = array(
        'leader' => 'قائد الفريق',
        'translator' => 'مترجم',
        'editor' => 'مدقق لغوي',
        'reviewer' => 'مراجع جودة',
        'designer' => 'مصمم رسوم',
        'member' => 'عضو'
    );
    return isset($roles[$role]) ? $roles[$role] : $role;
}

// Enqueue necessary styles
wp_enqueue_style('profile-modern', get_template_directory_uri() . '/css/profile-modern.css');
wp_enqueue_style('modern-badges', get_template_directory_uri() . '/css/modern-badges.css');
wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

// Get team's cover image or default - التحقق من وجود الخاصية أولاً
$cover_image = (isset($team->cover_url) && !empty($team->cover_url)) ? $team->cover_url : '';
$cover_style = $cover_image ? "background-image: url('" . esc_url($cover_image) . "')" : "background-color: #007bff";

// إعداد روابط التواصل الاجتماعي مع التحقق من وجودها
$social_links = array(
    'website' => isset($team->website_url) ? $team->website_url : '',
    'facebook' => isset($team->facebook_url) ? $team->facebook_url : '',
    'twitter' => isset($team->twitter_url) ? $team->twitter_url : '',
    'discord' => isset($team->discord_url) ? $team->discord_url : ''
);
?>

<style>
:root {
    --profile-primary: #3498db;
    --profile-secondary: #2ecc71;
    --profile-accent: #f39c12;
    --profile-dark: #343a40;
    --profile-light: #ecf0f1;
    --profile-bg: #f8f9fa;
    --profile-card: #ffffff;
    --profile-text: #2c3e50;
    --profile-text-light: #7f8c8d;
    --profile-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --profile-admin: #e74c3c;
    --profile-translator: #27ae60;
    --profile-reader: #7f8c8d;
    --profile-border-radius: 15px;
}

/* Dark Mode Support */
.dark-mode {
    --profile-bg: #1a1a2e;
    --profile-card: #343a40;
    --profile-text: #e3e3e3;
    --profile-text-light: #a8a8a8;
    --profile-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.profile-modern {
    background-color: var(--profile-bg);
    padding: 30px 0;
    font-family: 'Cairo', sans-serif;
    color: var(--profile-text);
}

.profile-header-modern {
    position: relative;
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: var(--profile-shadow);
}

.profile-cover-modern {
    height: 300px;
    width: 100%;
    background-size: cover;
    background-position: center;
}

.profile-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.6) 100%);
    z-index: 1;
}

.profile-info-modern {
    position: relative;
    z-index: 2;
    display: flex;
    padding: 30px;
    color: #fff;
    align-items: center;
}

.avatar-container {
    position: relative;
    margin-right: 30px;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid var(--profile-card);
    box-shadow: var(--profile-shadow);
    object-fit: cover;
}

.profile-cover-modern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100% !important;
    border-radius: 15px 15px 0 0;
    background-size: cover;
    background-position: center;
}

.team-badge-overlay {
    position: absolute;
    bottom: 0;
    right: 10px;
    width: 35px;
    height: 35px;
    background-color: var(--profile-secondary);
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--profile-shadow);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(46, 204, 113, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
    }
}

.profile-details-modern {
    flex: 1;
}

.profile-name-section {
    margin-bottom: 15px;
}

.profile-name {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.profile-meta {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
}

.profile-meta i {
    margin-left: 5px;
}

.join-date {
    display: inline-flex;
    align-items: center;
}

.profile-social {
    margin-top: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 18px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-icon:hover {
    transform: translateY(-5px);
    color: #fff;
}

.website-icon {
    background: linear-gradient(135deg, #2980b9, #3498db);
}

.twitter-icon {
    background: linear-gradient(135deg, #1da1f2, #0d8eda);
}

.facebook-icon {
    background: linear-gradient(135deg, #3b5998, #4267B2);
}

.discord-icon {
    background: linear-gradient(135deg, #7289da, #5865f2);
}

/* Statistics Cards */
.profile-stats-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    padding: 20px;
    box-shadow: var(--profile-shadow);
    text-align: center;
    transition: all 0.3s ease;
    border-top: 4px solid var(--profile-primary);
}

.stat-card:nth-child(2) {
    border-top-color: var(--profile-secondary);
}

.stat-card:nth-child(3) {
    border-top-color: var(--profile-accent);
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 36px;
    color: var(--profile-primary);
    margin-bottom: 15px;
}

.stat-card:nth-child(2) .stat-icon {
    color: var(--profile-secondary);
}

.stat-card:nth-child(3) .stat-icon {
    color: var(--profile-accent);
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--profile-text);
}

.stat-label {
    color: var(--profile-text-light);
    font-size: 14px;
}

/* Content Tabs Styling */
.profile-tabs-modern {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    overflow: hidden;
}

.tabs-nav-modern {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    background-color: var(--profile-card);
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding: 0;
    position: sticky;
    top: 60px;
    z-index: 10;
}

.tab-link-modern {
    padding: 15px 25px;
    color: var(--profile-text-light);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    border-bottom: 3px solid transparent;
}

.tab-link-modern i {
    margin-left: 8px;
    font-size: 16px;
}

.tab-link-modern:hover {
    color: var(--profile-primary);
}

.tab-link-modern.active {
    color: var(--profile-primary);
    border-bottom: 3px solid var(--profile-primary);
}

.tab-content-modern {
    padding: 30px;
}

.tab-pane-modern {
    display: none;
}

.tab-pane-modern.active {
    display: block;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Member Cards */
.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

/* Chapter Cards */
.chapters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.chapter-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    padding: 20px;
}

.chapter-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.chapter-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.chapter-title {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.4;
}

.chapter-title a {
    color: var(--profile-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.chapter-title a:hover {
    color: var(--profile-primary);
}

.chapter-date {
    color: var(--profile-text-light);
    font-size: 12px;
    display: flex;
    align-items: center;
}

.chapter-date i {
    margin-left: 5px;
}

.chapter-contributors h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: var(--profile-text);
    font-weight: 600;
}

.contributors-mini {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.contributor-mini {
    display: flex;
    align-items: center;
    background: rgba(52, 152, 219, 0.1);
    padding: 6px 10px;
    border-radius: 20px;
    font-size: 12px;
}

.contributor-avatar-mini {
    margin-left: 8px;
}

.contributor-avatar-mini img {
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.1);
}

.contributor-details {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.contributor-name-mini a {
    color: var(--profile-text);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.contributor-name-mini a:hover {
    color: var(--profile-primary);
}

.contributor-role-mini {
    color: var(--profile-text-light);
    font-size: 10px;
    margin-top: 1px;
}

.member-card {
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
    text-align: center;
    padding: 20px;
}

.member-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.member-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 15px;
    border: 3px solid var(--profile-primary);
    object-fit: cover;
}

.member-name {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--profile-text);
}

.member-name a {
    color: var(--profile-text);
    text-decoration: none;
    transition: color 0.3s ease;
}

.member-name a:hover {
    color: var(--profile-primary);
}

.member-role {
    display: inline-block;
    padding: 4px 12px;
    background-color: var(--profile-secondary);
    color: #fff;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 10px;
}

.member-joined {
    color: var(--profile-text-light);
    font-size: 12px;
}

.member-joined i {
    margin-left: 5px;
}

/* No Content Message */
.no-content-message {
    text-align: center;
    padding: 40px;
    background-color: var(--profile-card);
    border-radius: var(--profile-border-radius);
    box-shadow: var(--profile-shadow);
    color: var(--profile-text-light);
}

.no-content-message i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--profile-text-light);
    opacity: 0.5;
}

.no-content-message p {
    font-size: 18px;
    margin: 0;
}

/* Animation */
.animate-fade-in {
    opacity: 0;
    animation: fadeIn 0.6s ease forwards;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-info-modern {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .avatar-container {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .profile-name {
        justify-content: center;
    }

    .social-icons {
        justify-content: center;
    }

    .members-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .chapters-grid {
        grid-template-columns: 1fr;
    }

    .contributors-mini {
        flex-direction: column;
        gap: 5px;
    }

    .contributor-mini {
        justify-content: flex-start;
    }
}
</style>

<div class="profile-modern">
    <div class="container">
        <!-- Team Header - Modern Design -->
        <div class="profile-header-modern">
            <div class="profile-cover-modern" style="<?php echo $cover_style; ?>"></div>
            <div class="profile-header-overlay"></div>

            <div class="profile-info-modern">
                <div class="avatar-container">
                    <?php if (!empty($team->logo_url)): ?>
                        <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->name); ?>" class="profile-avatar">
                    <?php else: ?>
                        <div class="profile-avatar" style="background-color: var(--profile-primary); display: flex; align-items: center; justify-content: center; font-size: 48px; color: #fff;">
                            <i class="fas fa-users"></i>
                        </div>
                    <?php endif; ?>
                    <div class="team-badge-overlay"><i class="fas fa-users"></i></div>
                </div>

                <div class="profile-details-modern">
                    <div class="profile-name-section">
                        <h1 class="profile-name"><?php echo esc_html($team->name); ?></h1>

                        <div class="profile-meta">
                            <span class="join-date">
                                <i class="far fa-calendar-alt"></i>
                                تأسس في: <?php echo date_i18n('j F Y', strtotime($team->created_at)); ?>
                            </span>
                        </div>
                    </div>

                    <div class="profile-social">
                        <?php
                        $has_social_links = array_filter($social_links);

                        if (!empty($has_social_links)):
                        ?>
                        <div class="social-icons">
                            <?php if (!empty($social_links['website'])): ?>
                                <a href="<?php echo esc_url($social_links['website']); ?>" class="social-icon website-icon" target="_blank" rel="noopener" title="الموقع الرسمي">
                                    <i class="fas fa-globe"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($social_links['facebook'])): ?>
                                <a href="<?php echo esc_url($social_links['facebook']); ?>" class="social-icon facebook-icon" target="_blank" rel="noopener" title="فيسبوك">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($social_links['twitter'])): ?>
                                <a href="<?php echo esc_url($social_links['twitter']); ?>" class="social-icon twitter-icon" target="_blank" rel="noopener" title="تويتر">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            <?php endif; ?>

                            <?php if (!empty($social_links['discord'])): ?>
                                <a href="<?php echo esc_url($social_links['discord']); ?>" class="social-icon discord-icon" target="_blank" rel="noopener" title="ديسكورد">
                                    <i class="fab fa-discord"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="profile-stats-modern">
            <div class="stat-card animate-fade-in">
                <i class="fas fa-users stat-icon"></i>
                <div class="stat-value"><?php echo count($members); ?></div>
                <div class="stat-label">أعضاء الفريق</div>
            </div>
            <div class="stat-card animate-fade-in">
                <i class="fas fa-calendar-alt stat-icon"></i>
                <div class="stat-value"><?php echo date_i18n('Y', strtotime($team->created_at)); ?></div>
                <div class="stat-label">سنة التأسيس</div>
            </div>
            <div class="stat-card animate-fade-in">
                <i class="fas fa-star stat-icon"></i>
                <div class="stat-value">نشط</div>
                <div class="stat-label">حالة الفريق</div>
            </div>
        </div>

        <!-- Content Tabs -->
        <div class="profile-tabs-modern">
            <nav class="tabs-nav-modern">
                <a href="#" class="tab-link-modern active" data-tab="members">
                    <i class="fas fa-users"></i> أعضاء الفريق
                </a>
                <a href="#" class="tab-link-modern" data-tab="chapters">
                    <i class="fas fa-book-open"></i> الفصول المنشورة
                </a>
                <a href="#" class="tab-link-modern" data-tab="about">
                    <i class="fas fa-info-circle"></i> نبذة عن الفريق
                </a>
            </nav>

            <div class="tab-content-modern">
                <!-- Members Tab -->
                <div id="members" class="tab-pane-modern active">
                    <?php if (!empty($members)): ?>
                        <div class="members-grid">
                            <?php foreach ($members as $member):
                                $user_data = get_userdata($member->ID);
                                $role_name = get_team_role_name($member->role);
                                $avatar_url = get_avatar_url($member->ID, ['size' => 80]);
                            ?>
                                <div class="member-card animate-fade-in">
                                    <a href="<?php echo esc_url(get_author_posts_url($member->ID)); ?>">
                                        <img src="<?php echo esc_url($avatar_url); ?>" alt="<?php echo esc_attr($member->display_name); ?>" class="member-avatar">
                                    </a>
                                    <h3 class="member-name">
                                        <a href="<?php echo esc_url(get_author_posts_url($member->ID)); ?>">
                                            <?php echo esc_html($member->display_name); ?>
                                        </a>
                                    </h3>
                                    <div class="member-role"><?php echo esc_html($role_name); ?></div>
                                    <div class="member-joined">
                                        <i class="far fa-calendar-alt"></i>
                                        انضم في <?php echo date_i18n('j F Y', strtotime($member->joined_at)); ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-content-message">
                            <i class="fas fa-users"></i>
                            <p>لا يوجد أعضاء في هذا الفريق حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Chapters Tab -->
                <div id="chapters" class="tab-pane-modern">
                    <?php
                    // جلب الفصول التي نشرها الفريق
                    global $wpdb;
                    $team_chapters = $wpdb->get_results($wpdb->prepare(
                        "SELECT p.*, pm.meta_value as team_members_roles
                         FROM {$wpdb->posts} p
                         INNER JOIN {$wpdb->postmeta} pm1 ON p.ID = pm1.post_id AND pm1.meta_key = '_team_id'
                         LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_team_members_roles'
                         WHERE pm1.meta_value = %d
                         AND p.post_type = 'chapter'
                         AND p.post_status = 'publish'
                         ORDER BY p.post_date DESC
                         LIMIT 20",
                        $team->id
                    ));

                    if (!empty($team_chapters)): ?>
                        <div class="chapters-grid">
                            <?php foreach ($team_chapters as $chapter):
                                $novel_id = get_post_meta($chapter->ID, '_novel_id', true) ?: wp_get_post_parent_id($chapter->ID);
                                $novel_title = get_the_title($novel_id);
                                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                $team_members_roles = maybe_unserialize($chapter->team_members_roles);
                                $chapter_date = get_the_date('j F Y', $chapter->ID);
                            ?>
                                <div class="chapter-card animate-fade-in">
                                    <div class="chapter-header">
                                        <h4 class="chapter-title">
                                            <a href="<?php echo get_permalink($chapter->ID); ?>">
                                                <?php echo esc_html($novel_title); ?> - الفصل <?php echo esc_html($chapter_number); ?>
                                            </a>
                                        </h4>
                                        <span class="chapter-date">
                                            <i class="far fa-calendar"></i> <?php echo $chapter_date; ?>
                                        </span>
                                    </div>

                                    <?php if (!empty($team_members_roles) && is_array($team_members_roles)): ?>
                                        <div class="chapter-contributors">
                                            <h5>طاقم العمل:</h5>
                                            <div class="contributors-mini">
                                                <?php foreach ($team_members_roles as $user_id => $role):
                                                    if (empty($role)) continue;
                                                    $user = get_userdata($user_id);
                                                    if (!$user) continue;
                                                    $role_name = get_team_role_name($role);
                                                    $avatar = get_avatar($user_id, 30);
                                                ?>
                                                    <div class="contributor-mini">
                                                        <div class="contributor-avatar-mini"><?php echo $avatar; ?></div>
                                                        <div class="contributor-details">
                                                            <span class="contributor-name-mini">
                                                                <a href="<?php echo get_author_posts_url($user_id); ?>">
                                                                    <?php echo esc_html($user->display_name); ?>
                                                                </a>
                                                            </span>
                                                            <span class="contributor-role-mini"><?php echo esc_html($role_name); ?></span>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-content-message">
                            <i class="fas fa-book-open"></i>
                            <p>لم ينشر هذا الفريق أي فصول حتى الآن</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- About Tab -->
                <div id="about" class="tab-pane-modern">
                    <div class="about-section">
                        <div class="bio-card" style="background-color: var(--profile-card); border-radius: var(--profile-border-radius); padding: 25px; box-shadow: var(--profile-shadow);">
                            <h3 style="margin-top: 0; border-bottom: 2px solid rgba(0,0,0,0.05); padding-bottom: 15px; margin-bottom: 20px; color: var(--profile-text); display: flex; align-items: center;">
                                <i class="fas fa-info-circle" style="margin-left: 10px; color: var(--profile-primary);"></i>
                                نبذة عن <?php echo esc_html($team->name); ?>
                            </h3>
                            <?php if (!empty($team->description)): ?>
                                <div class="bio-content" style="color: var(--profile-text); line-height: 1.8;">
                                    <?php echo wpautop(wp_kses_post($team->description)); ?>
                                </div>
                            <?php else: ?>
                                <p class="no-content" style="color: var(--profile-text-light); font-style: italic;">لا توجد معلومات إضافية عن هذا الفريق</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabLinks = document.querySelectorAll('.tab-link-modern');
    const tabPanes = document.querySelectorAll('.tab-pane-modern');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            tabLinks.forEach(l => l.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
});
</script>

<?php
// إعادة تعيين بيانات المنشور
wp_reset_postdata();

// جلب الفوتر
get_footer();
