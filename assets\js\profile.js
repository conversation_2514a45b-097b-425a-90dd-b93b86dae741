jQuery(document).ready(function($) {
    // Tab functionality
    $('.profile-tabs nav a').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        // Update active states
        $('.profile-tabs nav li').removeClass('active');
        $(this).parent().addClass('active');
        
        // Show selected content
        $('.tab-pane').removeClass('active');
        $(target).addClass('active');
    });

    // Initialize first tab
    if (!$('.tab-pane.active').length) {
        $('.profile-tabs nav li:first').addClass('active');
        $('#about').addClass('active');
    }
});
