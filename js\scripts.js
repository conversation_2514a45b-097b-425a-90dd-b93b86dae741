/**
 * Theme Scripts
 */
(function($) {
    'use strict';

    // Dark Mode Toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.documentElement.classList.toggle('dark-mode');
            const isDarkMode = document.documentElement.classList.contains('dark-mode');
            localStorage.setItem('darkMode', isDarkMode);
            
            // Update icon
            const icon = this.querySelector('i');
            icon.className = isDarkMode ? 'fas fa-sun' : 'fas fa-moon';
        });

        // Check saved preference
        if (localStorage.getItem('darkMode') === 'true' || 
            document.documentElement.classList.contains('dark-mode')) {
            document.documentElement.classList.add('dark-mode');
            darkModeToggle.querySelector('i').className = 'fas fa-sun';
        }
    }

    // Font Size Controls
    const increaseFontSize = document.getElementById('increaseFontSize');
    const decreaseFontSize = document.getElementById('decreaseFontSize');
    const chapterContent = document.querySelector('.chapter-content');

    if (chapterContent) {
        // Get saved font size or use default
        let fontSize = localStorage.getItem('chapterFontSize') || 
                      window.getComputedStyle(chapterContent).fontSize;
        fontSize = parseInt(fontSize);

        // Apply saved font size
        chapterContent.style.fontSize = fontSize + 'px';

        // Increase font size
        if (increaseFontSize) {
            increaseFontSize.addEventListener('click', function() {
                if (fontSize < 24) {
                    fontSize += 1;
                    chapterContent.style.fontSize = fontSize + 'px';
                    localStorage.setItem('chapterFontSize', fontSize);
                }
            });
        }

        // Decrease font size
        if (decreaseFontSize) {
            decreaseFontSize.addEventListener('click', function() {
                if (fontSize > 14) {
                    fontSize -= 1;
                    chapterContent.style.fontSize = fontSize + 'px';
                    localStorage.setItem('chapterFontSize', fontSize);
                }
            });
        }
    }

    // Quick Search
    const quickSearch = document.getElementById('quickSearch');
    const searchResults = document.getElementById('searchResults');
    let searchTimeout;

    if (quickSearch && searchResults) {
        quickSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                searchResults.innerHTML = '';
                searchResults.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(function() {
                $.ajax({
                    url: sekaiplus.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'sekaiplus_quick_search',
                        nonce: sekaiplus.nonce,
                        query: query
                    },
                    beforeSend: function() {
                        searchResults.innerHTML = '<div class="p-4 text-center"><div class="loading-spinner"></div></div>';
                        searchResults.style.display = 'block';
                    },
                    success: function(response) {
                        searchResults.innerHTML = response;
                    },
                    error: function() {
                        searchResults.innerHTML = '<div class="p-4 text-center text-muted">حدث خطأ أثناء البحث</div>';
                    }
                });
            }, 500);
        });

        // Close search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!quickSearch.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
    }

    // Chapter Navigation
    document.addEventListener('keydown', function(e) {
        if (!chapterContent) return;

        const prevChapter = document.querySelector('.prev-chapter');
        const nextChapter = document.querySelector('.next-chapter');

        if (e.key === 'ArrowLeft' && nextChapter) {
            window.location.href = nextChapter.href;
        } else if (e.key === 'ArrowRight' && prevChapter) {
            window.location.href = prevChapter.href;
        }
    });

    // Mark Chapter as Read
    const markRead = document.getElementById('markRead');
    if (markRead) {
        const chapterId = markRead.dataset.chapterId;
        
        $.post(sekaiplus.ajax_url, {
            action: 'sekaiplus_mark_chapter_read',
            nonce: sekaiplus.nonce,
            chapter_id: chapterId
        });
    }

    // Loading Overlay
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        $(document).ajaxStart(function() {
            loadingOverlay.classList.remove('d-none');
        }).ajaxStop(function() {
            loadingOverlay.classList.add('d-none');
        });
    }

    // Bootstrap Tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add smooth scrolling to all links
    $('a[href*="#"]:not([href="#"])').click(function() {
        if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && 
            location.hostname === this.hostname) {
            let target = $(this.hash);
            target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 60
                }, 500);
                return false;
            }
        }
    });

    // Rating System
    $('.rating-stars i').on('click', function() {
        if (!sekaiplus.isLoggedIn) {
            window.location.href = sekaiplus.login_url;
            return;
        }

        const rating = $(this).data('rating');
        const novelId = $(this).closest('.novel-card').data('novel-id');

        $.ajax({
            url: sekaiplus.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_rate_novel',
                nonce: sekaiplus.nonce,
                novel_id: novelId,
                rating: rating
            },
            success: function(response) {
                if (response.success) {
                    // Update rating display
                    $('.rating-stars').html(response.data.stars);
                    $('.rating-count').text('(' + response.data.count + ' تقييم)');
                }
            }
        });
    });

    // Bookmark System
    window.sekaiplus_toggle_bookmark = function(novelId) {
        if (!sekaiplus.isLoggedIn) {
            window.location.href = sekaiplus.login_url;
            return;
        }

        $.ajax({
            url: sekaiplus.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_toggle_bookmark',
                nonce: sekaiplus.nonce,
                novel_id: novelId
            },
            success: function(response) {
                if (response.success) {
                    const btn = $(`button[onclick="sekaiplus_toggle_bookmark(${novelId})"]`);
                    const icon = btn.find('i');
                    const text = btn.find('span');
                    const countEl = btn.next('.text-center').find('.bookmark-count');

                    if (response.data.bookmarked) {
                        btn.removeClass('btn-outline-primary').addClass('btn-primary');
                        icon.removeClass('far').addClass('fas');
                        text.text('إزالة من القائمة');
                    } else {
                        btn.removeClass('btn-primary').addClass('btn-outline-primary');
                        icon.removeClass('fas').addClass('far');
                        text.text('إضافة للقائمة');
                    }

                    if (countEl.length) {
                        countEl.text(response.data.count);
                    }
                }
            }
        });
    };

    // Volume Sections Accordion
    $('.volume-title').on('click', function() {
        const chapterList = $(this).next('.chapter-list');
        $(this).toggleClass('collapsed');
        chapterList.slideToggle();
    });

    // Initialize volume sections
    $('.chapter-list').hide();
    $('.volume-title').first().click();

})(jQuery);
