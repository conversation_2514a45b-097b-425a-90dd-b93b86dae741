<?php
/**
 * Template Name: Team Profile Fixed
 * Template Post Type: page
 */

// إعداد متغيرات ووردبريس الأساسية
global $wpdb, $post, $wp_query;

// إنشاء كائن منشور افتراضي للفريق
$team_post = new stdClass();
$team_post->ID = 0;
$team_post->post_author = 1;
$team_post->post_date = current_time('mysql');
$team_post->post_date_gmt = current_time('mysql', 1);
$team_post->post_title = 'فريق الترجمة';
$team_post->post_status = 'publish';
$team_post->comment_status = 'closed';
$team_post->ping_status = 'closed';
$team_post->post_type = 'page';
$team_post->comment_count = 0;

// جعل الكائن الافتراضي هو المنشور الحالي
$post = $team_post;
setup_postdata($post);

// الحصول على معرف الفريق من الرابط
$team_slug = get_query_var('team_slug');

// إذا لم يتم العثور على معرف الفريق من المتغير، نحاول الحصول عليه من الرابط
if (empty($team_slug)) {
    $path = trim(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH), '/');
    $path_parts = explode('/', $path);
    $team_slug = end($path_parts);
}

// جلب بيانات الفريق
$team = $wpdb->get_row($wpdb->prepare(
    "SELECT * FROM {$wpdb->prefix}teams WHERE slug = %s",
    $team_slug
));

// إذا لم يتم العثور على الفريق، نعرض صفحة 404
if (!$team) {
    status_header(404);
    nocache_headers();
    include(get_404_template());
    exit;
}

// تعيين خصائص إضافية للكائن الافتراضي
$post->post_title = $team->name;
$post->post_content = $team->description;

// جلب الهيدر
get_header();

// جلب بيانات أعضاء الفريق
$members = $wpdb->get_results($wpdb->prepare(
    "SELECT u.ID, u.display_name, u.user_email, tm.role, tm.joined_at 
     FROM {$wpdb->prefix}team_members tm
     INNER JOIN {$wpdb->users} u ON tm.user_id = u.ID
     WHERE tm.team_id = %d
     ORDER BY tm.role = 'leader' DESC, u.display_name",
    $team->id
));

// تحميل فئة الإدارة للحصول على أسماء الأدوار
$team_system = new Team_System_Admin('team-system', '1.0.0');
?>

<main id="primary" class="site-main">
    <div class="container py-5">
        <article id="team-<?php echo esc_attr($team->id); ?>" class="team-profile">
            <header class="entry-header mb-5">
                <?php if (!empty($team->cover_url)): ?>
                    <div class="team-cover mb-4" style="height: 300px; background-image: url('<?php echo esc_url($team->cover_url); ?>'); background-size: cover; background-position: center; border-radius: 8px;"></div>
                <?php endif; ?>
                
                <div class="d-flex align-items-center">
                    <?php if (!empty($team->logo_url)): ?>
                        <div class="me-4" style="width: 150px; height: 150px; flex-shrink: 0;">
                            <img src="<?php echo esc_url($team->logo_url); ?>" alt="<?php echo esc_attr($team->name); ?>" class="img-fluid rounded-circle border" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                    <?php endif; ?>
                    
                    <div>
                        <h1 class="entry-title mb-3"><?php echo esc_html($team->name); ?></h1>
                        
                        <?php if (!empty($team->description)): ?>
                            <div class="team-description lead">
                                <?php echo wpautop(wp_kses_post($team->description)); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="team-meta text-muted mt-3">
                            <span class="me-4">
                                <i class="fas fa-users me-1"></i>
                                <?php echo count($members); ?> عضو
                            </span>
                            <span>
                                <i class="far fa-calendar-alt me-1"></i>
                                <?php echo date_i18n('j F Y', strtotime($team->created_at)); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            <div class="entry-content">
                <?php if (!empty($members)): ?>
                    <section class="team-members mb-5">
                        <h2 class="mb-4 pb-2 border-bottom">أعضاء الفريق</h2>
                        
                        <div class="row g-4">
                            <?php foreach ($members as $member): 
                                $user_data = get_userdata($member->ID);
                                $role_name = $team_system->get_role_name($member->role);
                                $avatar = get_avatar($member->ID, 100, '', '', array('class' => 'rounded-circle mb-2'));
                            ?>
                                <div class="col-md-4 col-lg-3">
                                    <div class="card h-100 shadow-sm">
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                <a href="<?php echo esc_url(get_author_posts_url($member->ID)); ?>">
                                                    <?php echo $avatar; ?>
                                                </a>
                                            </div>
                                            <h3 class="h5 mb-1">
                                                <a href="<?php echo esc_url(get_author_posts_url($member->ID)); ?>" class="text-decoration-none text-dark">
                                                    <?php echo esc_html($member->display_name); ?>
                                                </a>
                                            </h3>
                                            <span class="badge bg-secondary"><?php echo esc_html($role_name); ?></span>
                                            <div class="text-muted small mt-2">
                                                <i class="far fa-calendar-alt me-1"></i>
                                                انضم في <?php echo date_i18n('j F Y', strtotime($member->joined_at)); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
                
                <?php 
                $social_links = array(
                    'website' => $team->website_url,
                    'facebook' => $team->facebook_url,
                    'twitter' => $team->twitter_url,
                    'discord' => $team->discord_url
                );
                
                $has_social_links = array_filter($social_links);
                
                if (!empty($has_social_links)): 
                ?>
                    <section class="team-links mb-5">
                        <h2 class="mb-4 pb-2 border-bottom">روابط التواصل</h2>
                        <div class="d-flex gap-3">
                            <?php if (!empty($team->website_url)): ?>
                                <a href="<?php echo esc_url($team->website_url); ?>" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-globe me-1"></i> الموقع
                                </a>
                            <?php endif; ?>
                            
                            <?php if (!empty($team->facebook_url)): ?>
                                <a href="<?php echo esc_url($team->facebook_url); ?>" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-facebook-f me-1"></i> فيسبوك
                                </a>
                            <?php endif; ?>
                            
                            <?php if (!empty($team->twitter_url)): ?>
                                <a href="<?php echo esc_url($team->twitter_url); ?>" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-twitter me-1"></i> تويتر
                                </a>
                            <?php endif; ?>
                            
                            <?php if (!empty($team->discord_url)): ?>
                                <a href="<?php echo esc_url($team->discord_url); ?>" target="_blank" class="btn btn-outline-primary">
                                    <i class="fab fa-discord me-1"></i> ديسكورد
                                </a>
                            <?php endif; ?>
                        </div>
                    </section>
                <?php endif; ?>
            </div>
        </article>
    </div>
</main>

<?php
// إعادة تعيين بيانات المنشور
wp_reset_postdata();

// جلب الفوتر
get_footer();
