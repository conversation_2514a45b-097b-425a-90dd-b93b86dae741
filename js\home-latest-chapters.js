jQuery(document).ready(function($) {
    const latestChapters = $('#latest-chapters');
    const template = document.getElementById('chapter-template');
    let loading = false;

    // Function to format date
    function timeAgo(date) {
        const seconds = Math.floor((new Date() - new Date(date)) / 1000);
        
        let interval = Math.floor(seconds / 31536000);
        if (interval > 1) return interval + ' سنوات';
        if (interval === 1) return 'سنة';
        
        interval = Math.floor(seconds / 2592000);
        if (interval > 1) return interval + ' شهور';
        if (interval === 1) return 'شهر';
        
        interval = Math.floor(seconds / 86400);
        if (interval > 1) return interval + ' أيام';
        if (interval === 1) return 'يوم';
        
        interval = Math.floor(seconds / 3600);
        if (interval > 1) return interval + ' ساعات';
        if (interval === 1) return 'ساعة';
        
        interval = Math.floor(seconds / 60);
        if (interval > 1) return interval + ' دقائق';
        if (interval === 1) return 'دقيقة';
        
        if (seconds < 10) return 'الآن';
        
        return Math.floor(seconds) + ' ثواني';
    }

    // Function to create chapter element
    function createChapterElement(chapter) {
        const clone = template.content.cloneNode(true);
        
        // Set novel details
        const novelLink = clone.querySelector('.novel-link');
        novelLink.href = chapter.novel_link;
        novelLink.textContent = chapter.novel_title;
        
        // Set novel cover if available
        if (chapter.novel_cover) {
            const coverImg = clone.querySelector('.novel-cover');
            coverImg.src = chapter.novel_cover;
            coverImg.alt = chapter.novel_title;
        } else {
            // Hide the thumbnail container if no cover
            const thumbnailContainer = clone.querySelector('.novel-thumbnail');
            if (thumbnailContainer) {
                thumbnailContainer.style.display = 'none';
            }
        }
        
        // Set chapter details
        const chapterLink = clone.querySelector('.chapter-link');
        chapterLink.href = chapter.chapter_link;
        chapterLink.textContent = chapter.chapter_title;
        
        // Set translator details
        const translatorLink = clone.querySelector('.translator-link');
        translatorLink.href = chapter.translator_link;
        translatorLink.textContent = chapter.translator_name;
        
        // Add translator avatar if available
        if (chapter.translator_avatar) {
            const translatorInfo = clone.querySelector('.translator-info');
            const translatorImg = document.createElement('img');
            translatorImg.src = chapter.translator_avatar;
            translatorImg.alt = chapter.translator_name;
            translatorImg.className = 'rounded-circle me-1';
            translatorImg.width = 20;
            translatorImg.height = 20;
            translatorInfo.prepend(translatorImg);
        }
        
        // Set time ago
        const timeElement = clone.querySelector('.time-ago');
        timeElement.textContent = timeAgo(chapter.date);
        
        return clone;
    }

    // Function to load chapters
    function loadHomeChapters() {
        if (loading) return;
        
        loading = true;
        latestChapters.html('<tr><td colspan="3" class="text-center p-4"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>جاري التحميل...</td></tr>');
        
        $.ajax({
            url: sekaiplus_home.ajax_url,
            type: 'POST',
            data: {
                action: 'sekaiplus_load_latest_chapters',
                nonce: sekaiplus_home.nonce,
                page: 1
            },
            success: function(response) {
                if (response.success && response.data) {
                    const chapters = response.data;
                    
                    if (chapters.length === 0) {
                        latestChapters.html('<tr><td colspan="3" class="text-center p-4">لا توجد فصول حالياً</td></tr>');
                        return;
                    }
                    
                    // Clear previous content
                    latestChapters.empty();
                    
                    // Add each chapter
                    chapters.forEach(chapter => {
                        latestChapters.append(createChapterElement(chapter));
                    });
                } else {
                    latestChapters.html('<tr><td colspan="3" class="text-center p-4">حدث خطأ أثناء تحميل الفصول</td></tr>');
                }
            },
            error: function() {
                latestChapters.html('<tr><td colspan="3" class="text-center p-4">حدث خطأ أثناء تحميل الفصول</td></tr>');
            },
            complete: function() {
                loading = false;
            }
        });
    }

    // Load chapters when page loads
    loadHomeChapters();
});
