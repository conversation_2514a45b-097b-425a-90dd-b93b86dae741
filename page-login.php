<?php
/**
 * Template Name: Login Page
 */

// Redirect if user is already logged in
if (is_user_logged_in()) {
    wp_safe_redirect(home_url());
    exit;
}

// Set default variables
$action = isset($_REQUEST['action']) ? $_REQUEST['action'] : 'login';
$errors = new WP_Error();
$redirect_to = isset($_REQUEST['redirect_to']) ? $_REQUEST['redirect_to'] : home_url();

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login_nonce']) && wp_verify_nonce($_POST['login_nonce'], 'login_user')) {
    $email = isset($_POST['email']) ? sanitize_email($_POST['email']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $rememberme = isset($_POST['rememberme']);
    
    // Get user by email
    $user = get_user_by('email', $email);
    
    if ($user) {
        // Attempt to authenticate
        $creds = array(
            'user_login'    => $user->user_login,
            'user_password' => $password,
            'remember'      => $rememberme
        );
        
        // Check if SSL is required
        $secure_cookie = '';
        if (!force_ssl_admin() && $user && get_user_option('use_ssl', $user->ID)) {
            $secure_cookie = true;
            force_ssl_admin(true);
        }
        
        $user_auth = wp_signon($creds, $secure_cookie);
        
        if (!is_wp_error($user_auth)) {
            // Update last login
            update_user_meta($user->ID, 'last_login', current_time('mysql'));
            
            // Apply WordPress login filters for redirection
            $requested_redirect_to = isset($_REQUEST['redirect_to']) ? $_REQUEST['redirect_to'] : '';
            $redirect_to = apply_filters('login_redirect', $redirect_to, $requested_redirect_to, $user_auth);
            
            // Check if it's time to add a redirect to the admin email confirmation screen
            if ($user_auth->has_cap('manage_options')) {
                $admin_email_lifespan = (int) get_option('admin_email_lifespan');
                $admin_email_check_interval = (int) apply_filters('admin_email_check_interval', 6 * MONTH_IN_SECONDS);
                
                if ($admin_email_check_interval > 0 && time() > $admin_email_lifespan) {
                    $redirect_to = add_query_arg(
                        array(
                            'action'  => 'confirm_admin_email',
                            'wp_lang' => get_user_locale($user_auth),
                        ),
                        wp_login_url($redirect_to)
                    );
                }
            }
            
            // Redirect based on role and capabilities
            if ((empty($redirect_to) || $redirect_to == home_url())) {
                if (is_multisite() && !get_active_blog_for_user($user_auth->ID) && !is_super_admin($user_auth->ID)) {
                    $redirect_to = user_admin_url();
                } elseif (is_multisite() && !$user_auth->has_cap('read')) {
                    $redirect_to = get_dashboard_url($user_auth->ID);
                } elseif (!$user_auth->has_cap('edit_posts')) {
                    $redirect_to = $user_auth->has_cap('read') ? admin_url('profile.php') : home_url();
                }
            }
            
            wp_safe_redirect($redirect_to);
            exit;
        } else {
            $errors->add('invalid_credentials', 'البريد الإلكتروني أو كلمة المرور غير صحيحة');
        }
    } else {
        $errors->add('invalid_email', 'البريد الإلكتروني أو كلمة المرور غير صحيحة');
    }
}

// Handle password reset request
if ($action === 'lostpassword' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['user_login']) && !empty($_POST['user_login'])) {
        $errors = retrieve_password();
        if (!is_wp_error($errors)) {
            $redirect_to = add_query_arg('checkemail', 'confirm', wp_login_url());
            wp_safe_redirect($redirect_to);
            exit;
        }
    } else {
        $errors->add('empty_username', __('Enter a username or email address.'));
    }
}

// Handle password reset confirmation
if ($action === 'checkemail' && isset($_GET['checkemail'])) {
    if ($_GET['checkemail'] === 'confirm') {
        $errors->add(
            'confirm',
            sprintf(
                __('Check your email for the confirmation link, then visit the <a href="%s">login page</a>.'),
                wp_login_url()
            ),
            'message'
        );
    } elseif ($_GET['checkemail'] === 'registered') {
        $errors->add(
            'registered',
            sprintf(
                __('Registration complete. Please check your email, then visit the <a href="%s">login page</a>.'),
                wp_login_url()
            ),
            'message'
        );
    }
}

// Handle loggedout message
if (isset($_GET['loggedout']) && $_GET['loggedout']) {
    $errors->add('loggedout', __('You are now logged out.'), 'message');
}

// Handle registration disabled message
if (isset($_GET['registration']) && 'disabled' === $_GET['registration']) {
    $errors->add('registerdisabled', __('<strong>Error:</strong> User registration is currently not allowed.'));
}

// Set a cookie now to see if they are supported by the browser.
$secure = ('https' === parse_url(wp_login_url(), PHP_URL_SCHEME));
setcookie(TEST_COOKIE, 'WP Cookie check', 0, COOKIEPATH, COOKIE_DOMAIN, $secure);

// Check for cookie errors
if (isset($_POST['testcookie']) && empty($_COOKIE[TEST_COOKIE])) {
    $errors->add(
        'test_cookie',
        sprintf(
            __('<strong>Error:</strong> Cookies are blocked or not supported by your browser. You must <a href="%s">enable cookies</a> to use WordPress.'),
            __('https://developer.wordpress.org/advanced-administration/wordpress/cookies/#enable-cookies-in-your-browser')
        )
    );
}

/**
 * Fires when the login form is initialized.
 */
do_action('login_init');

/**
 * Fires before a specified login form action.
 */
do_action("login_form_{$action}");

get_header();
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body">
                    <h2 class="text-center mb-4">تسجيل الدخول</h2>
                    
                    <?php if ($errors->has_errors()): ?>
                        <div class="alert <?php echo ($errors->get_error_data() === 'message') ? 'alert-info' : 'alert-danger'; ?>" id="<?php echo ($errors->get_error_data() === 'message') ? 'login-message' : 'login-error'; ?>">
                            <?php 
                            foreach ($errors->get_error_messages() as $error) {
                                echo '<p>' . $error . '</p>';
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['registered'])): ?>
                        <div class="alert alert-success">تم تسجيل حسابك بنجاح. يمكنك الآن تسجيل الدخول.</div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" id="loginform">
                        <?php wp_nonce_field('login_user', 'login_nonce'); ?>
                        <?php if (isset($_REQUEST['redirect_to'])): ?>
                            <input type="hidden" name="redirect_to" value="<?php echo esc_attr($_REQUEST['redirect_to']); ?>" />
                        <?php endif; ?>
                        <input type="hidden" name="testcookie" value="1" />
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>"
                                   required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberme" name="rememberme" value="forever" <?php checked(isset($_POST['rememberme'])); ?>>
                            <label class="form-check-label" for="rememberme">تذكرني</label>
                        </div>
                        
                        <?php
                        /**
                         * Fires following the 'Password' field in the login form.
                         *
                         * @since 2.1.0
                         */
                        do_action('login_form');
                        ?>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" name="wp-submit" id="wp-submit">تسجيل الدخول</button>
                        </div>
                    </form>
                    
                    <div class="text-center mt-3">
                        <?php 
                        $register_page = get_page_by_path('register');
                        $lostpassword_page = get_page_by_path('lostpassword');
                        $register_url = $register_page ? get_permalink($register_page->ID) : wp_registration_url();
                        $lostpassword_url = $lostpassword_page ? get_permalink($lostpassword_page->ID) : wp_lostpassword_url();
                        ?>
                        <p class="mb-0">
                            <?php if (get_option('users_can_register')): ?>
                                ليس لديك حساب؟ <a href="<?php echo esc_url($register_url); ?>">إنشاء حساب جديد</a>
                            <?php endif; ?>
                        </p>
                        <p class="mt-2"><a href="<?php echo esc_url($lostpassword_url); ?>">نسيت كلمة المرور؟</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Prevent form resubmission on page refresh
if (window.history.replaceState) {
    window.history.replaceState(null, null, window.location.href);
}

// Add shake effect for error messages
if (document.querySelector('.alert-danger')) {
    document.querySelector('form').classList.add('shake');
}

// Focus on appropriate field
function wp_attempt_focus() {
    setTimeout(function() {
        try {
            <?php if (isset($_POST['email'])): ?>
            d = document.getElementById('password');
            d.value = '';
            <?php else: ?>
            d = document.getElementById('email');
            <?php if ($errors->get_error_code() === 'invalid_email'): ?>
            d.value = '';
            <?php endif; ?>
            <?php endif; ?>
            d.focus();
            d.select();
        } catch(er) {}
    }, 200);
}

<?php if (apply_filters('enable_login_autofocus', true) && !isset($error)): ?>
wp_attempt_focus();
<?php endif; ?>
</script>

<?php 
// Fire the login footer action
do_action('login_footer');

get_footer(); 
?>
