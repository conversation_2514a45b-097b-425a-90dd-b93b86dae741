<?php
/**
 * ملف اختبار نظام التقييمات
 * يمكن الوصول إليه عبر: yoursite.com/wp-content/themes/Sekaiplus/test-rating.php
 */

// تحميل WordPress
require_once('../../../wp-load.php');

// التحقق من تسجيل الدخول
if (!is_user_logged_in()) {
    die('يجب تسجيل الدخول أولاً');
}

echo '<h1>اختبار نظام التقييمات</h1>';

$user_id = get_current_user_id();
$test_novel_id = 1; // ضع معرف رواية موجودة

// التحقق من وجود الرواية
$novel = get_post($test_novel_id);
if (!$novel || $novel->post_type !== 'novel') {
    die('الرواية غير موجودة. يرجى تغيير $test_novel_id في الكود.');
}

echo '<h2>معلومات الاختبار:</h2>';
echo 'الرواية: ' . get_the_title($test_novel_id) . ' (ID: ' . $test_novel_id . ')<br>';
echo 'المستخدم: ' . wp_get_current_user()->display_name . ' (ID: ' . $user_id . ')<br>';

// عرض التقييمات الحالية
echo '<h2>التقييمات الحالية:</h2>';
$user_ratings = get_post_meta($test_novel_id, '_user_ratings', true);
if (is_array($user_ratings) && !empty($user_ratings)) {
    echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th>معرف المستخدم</th><th>التقييم</th><th>التاريخ</th><th>منذ</th></tr>';
    foreach ($user_ratings as $uid => $rating_data) {
        $user_info = get_userdata($uid);
        $time_diff = time() - $rating_data['time'];
        $hours_ago = round($time_diff / 3600, 1);
        echo '<tr>';
        echo '<td>' . $uid . ' (' . ($user_info ? $user_info->display_name : 'غير معروف') . ')</td>';
        echo '<td>' . $rating_data['rating'] . ' نجوم</td>';
        echo '<td>' . date('Y-m-d H:i:s', $rating_data['time']) . '</td>';
        echo '<td>' . $hours_ago . ' ساعة</td>';
        echo '</tr>';
    }
    echo '</table>';
    
    // حساب المتوسط
    $total = 0;
    foreach ($user_ratings as $rating_data) {
        $total += $rating_data['rating'];
    }
    $average = round($total / count($user_ratings), 1);
    echo '<p><strong>متوسط التقييم:</strong> ' . $average . ' من 5 (' . count($user_ratings) . ' تقييم)</p>';
} else {
    echo 'لا توجد تقييمات حتى الآن<br>';
}

// التحقق من تقييم المستخدم الحالي
if (isset($user_ratings[$user_id])) {
    $user_rating = $user_ratings[$user_id];
    $time_since_rating = (time() - $user_rating['time']) / 3600;
    $can_rate_again = $time_since_rating >= 24;
    
    echo '<h2>تقييمك الحالي:</h2>';
    echo 'التقييم: ' . $user_rating['rating'] . ' نجوم<br>';
    echo 'تاريخ التقييم: ' . date('Y-m-d H:i:s', $user_rating['time']) . '<br>';
    echo 'منذ: ' . round($time_since_rating, 1) . ' ساعة<br>';
    
    if ($can_rate_again) {
        echo '<span style="color: green;">✅ يمكنك تغيير تقييمك الآن</span><br>';
    } else {
        $hours_remaining = ceil(24 - $time_since_rating);
        echo '<span style="color: red;">❌ لا يمكنك تغيير تقييمك إلا بعد ' . $hours_remaining . ' ساعة</span><br>';
    }
} else {
    echo '<h2>تقييمك:</h2>';
    echo '<span style="color: blue;">لم تقم بتقييم هذه الرواية بعد</span><br>';
}

// اختبار التقييم
if (isset($_GET['rate'])) {
    $rating = intval($_GET['rate']);
    if ($rating >= 1 && $rating <= 5) {
        echo '<h2>اختبار التقييم:</h2>';
        echo 'محاولة تقييم الرواية بـ ' . $rating . ' نجوم...<br>';
        
        // محاكاة AJAX request
        $_POST['novel_id'] = $test_novel_id;
        $_POST['rating'] = $rating;
        $_POST['nonce'] = wp_create_nonce('sekaiplus_ajax_nonce');
        
        // استدعاء الدالة مباشرة
        ob_start();
        sekaiplus_ajax_rate_novel();
        $output = ob_get_clean();
        
        echo 'النتيجة: ' . $output . '<br>';
    }
}

// روابط الاختبار
echo '<h2>روابط الاختبار:</h2>';
for ($i = 1; $i <= 5; $i++) {
    echo '<a href="?rate=' . $i . '" style="margin: 5px; padding: 10px; background: #007cba; color: white; text-decoration: none; border-radius: 5px;">';
    echo 'تقييم ' . $i . ' نجوم';
    echo '</a>';
}
echo '<br><br>';
echo '<a href="' . $_SERVER['PHP_SELF'] . '" style="padding: 10px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;">تحديث الصفحة</a><br>';

// اختبار JavaScript
echo '<h2>اختبار JavaScript:</h2>';
echo '<div id="rating-test">';
echo '<p>اختبار التقييم بـ JavaScript:</p>';
for ($i = 1; $i <= 5; $i++) {
    echo '<button onclick="testRating(' . $i . ')" style="margin: 5px; padding: 10px;">تقييم ' . $i . ' نجوم</button>';
}
echo '<div id="result" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>';
echo '</div>';

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testRating(rating) {
    $('#result').html('جاري التقييم...');
    
    $.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'sekaiplus_rate_novel',
            novel_id: <?php echo $test_novel_id; ?>,
            rating: rating,
            nonce: '<?php echo wp_create_nonce('sekaiplus_ajax_nonce'); ?>'
        },
        success: function(response) {
            if (response.success) {
                $('#result').html('<div style="color: green;">✅ ' + response.data.message + '<br>متوسط التقييم: ' + response.data.averageRating + '<br>عدد التقييمات: ' + response.data.ratingCount + '</div>');
            } else {
                $('#result').html('<div style="color: red;">❌ ' + response.data.message + '</div>');
            }
        },
        error: function() {
            $('#result').html('<div style="color: red;">❌ حدث خطأ في الاتصال</div>');
        }
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: right; }
th { background-color: #f0f0f0; }
</style>
