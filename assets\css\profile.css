.profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.profile-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 2rem;
    position: relative;
}

.profile-header {
    text-align: center;
    margin-bottom: 2rem;
}

.profile-cover {
    height: 300px;
    background: #f5f5f5;
    overflow: hidden;
}

.profile-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    margin: 0 auto 1rem;
    position: relative;
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.profile-info {
    padding: 60px 30px 30px;
}

.profile-stats {
    display: flex;
    justify-content: space-around;
    margin: 2rem 0;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    display: block;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.profile-tabs {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    padding: 1.5rem;
}

.nav-tabs {
    border: none;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    color: #7f8c8d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link.active {
    color: #3498db;
    background: none;
    position: relative;
}

.nav-tabs .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #3498db;
    border-radius: 3px;
}

.badge {
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 500;
    margin: 0 0.25rem;
}

.admin-badge {
    background: linear-gradient(135deg, #1a75ff 0%, #00ccff 100%);
    color: white;
}

.translator-badge {
    background: linear-gradient(135deg, #34c759 0%, #2ecc71 100%);
    color: white;
}

.reader-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ffd93d 100%);
    color: white;
}

.profile-tabs nav ul {
    list-style: none;
    display: flex;
    padding: 0;
    margin: 0;
    border-bottom: 1px solid #eee;
}

.profile-tabs nav li {
    margin-right: 20px;
}

.profile-tabs nav a {
    display: block;
    padding: 10px 0;
    color: #666;
    text-decoration: none;
    border-bottom: 2px solid transparent;
}

.profile-tabs nav li.active a {
    color: #007bff;
    border-bottom-color: #007bff;
}

.tab-content {
    padding: 20px 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.user-details .detail-item {
    margin-bottom: 15px;
}

.user-details .label {
    font-weight: bold;
    margin-right: 10px;
    color: #666;
}

.edit-profile-btn {
    display: inline-block;
    padding: 8px 20px;
    background: #007bff;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-top: 20px;
}

.user-post {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.post-meta {
    color: #666;
    font-size: 0.9em;
    margin: 5px 0;
}

/* ... existing code ... */

/* Fix for profile picture modal text visibility in dark mode */
.gravatar-modal {
    color: #333; /* Dark text color that works in both light and dark modes */
}

.gravatar-modal input[type="email"] {
    color: #333;
    background-color: #fff;
}

.gravatar-modal a {
    color: #0073aa; /* WordPress default link color */
}

/* ... existing code ... */