<?php
/**
 * وظائف اختيار الترجمات المفضلة - SekaiPLU
 *
 * يحتوي على وظائف تحديد واختيار الترجمة المناسبة بناءً على معايير مختلفة
 *
 * @package SekaiPLU
 */

if (!defined('ABSPATH')) {
    exit; // منع الوصول المباشر
}

/**
 * تحديد الترجمة المناسبة للعرض بناءً على المعايير المختلفة
 *
 * @param string $chapter_unique_id معرف الفصل الفريد
 * @param int|string $requested_author_id معرف المؤلف/الكاتب المطلوب (إذا تم تحديده)
 * @return WP_Post|null منشور الترجمة أو null إذا لم يتم العثور عليه
 */
function sekaiplu_determine_translation($chapter_unique_id, $requested_author_id = '') {
    global $wpdb;
    
    // إذا كان هناك طلب محدد لمنشور بمعرف معين (post ID)
    if (!empty($_GET['r']) && is_numeric($_GET['r'])) {
        $requested_post_id = intval($_GET['r']);
        $requested_post = get_post($requested_post_id);
        
        // تحقق من أن المنشور موجود وهو من نوع chapter
        if ($requested_post && $requested_post->post_type === 'chapter') {
            // تحقق من المعرف الفريد للترجمة
            $post_chapter_unique_id = get_post_meta($requested_post_id, '_chapter_unique_id', true);
            
            // إذا كان معرف الفصل الفريد يتطابق مع المطلوب
            if ($post_chapter_unique_id === $chapter_unique_id) {
                if (current_user_can('manage_options')) {
                    error_log("SEKAIPLU DEBUG: تم العثور على المنشور بالمعرف {$requested_post_id} | معرف الفصل الفريد: {$post_chapter_unique_id}");
                }
                return $requested_post;
            }
        }
    }
    
    // إذا كان هناك طلب لمترجم محدد
    if (!empty($requested_author_id)) {
        // البحث عن الترجمة باستخدام المعرف الفريد للترجمة
        $translation_unique_id = $chapter_unique_id . '_' . $requested_author_id;
        
        $posts = get_posts([
            'post_type' => 'chapter',
            'posts_per_page' => 1,
            'meta_query' => [
                [
                    'key' => '_translation_unique_id',
                    'value' => $translation_unique_id,
                    'compare' => '='
                ]
            ],
            'post_status' => 'publish'
        ]);
        
        if (!empty($posts)) {
            if (current_user_can('manage_options')) {
                error_log("SEKAIPLU DEBUG: تم العثور على ترجمة بواسطة المعرف الفريد للترجمة: {$translation_unique_id}");
            }
            return $posts[0];
        }
    }
    
    // تحقق من كوكيز المترجم المفضل
    $preferred_translator = '';
    if (isset($_COOKIE['preferred_translator'])) {
        $preferred_translator = sanitize_text_field($_COOKIE['preferred_translator']);
        
        // البحث عن معرف المستخدم لهذا الاسم المستعار
        $user = get_user_by('slug', $preferred_translator);
        if ($user) {
            // البحث عن الترجمة باستخدام المعرف الفريد للترجمة
            $translation_unique_id = $chapter_unique_id . '_' . $user->ID;
            
            $posts = get_posts([
                'post_type' => 'chapter',
                'posts_per_page' => 1,
                'meta_query' => [
                    [
                        'key' => '_translation_unique_id',
                        'value' => $translation_unique_id,
                        'compare' => '='
                    ]
                ],
                'post_status' => 'publish'
            ]);
            
            if (!empty($posts)) {
                if (current_user_can('manage_options')) {
                    error_log("SEKAIPLU DEBUG: تم العثور على ترجمة مفضلة بواسطة المعرف الفريد للترجمة: {$translation_unique_id}");
                }
                return $posts[0];
            }
        }
    }
    
    // إذا لم يتم العثور على ترجمة مفضلة، ابحث عن أي ترجمة لهذا الفصل
    $translations = get_posts([
        'post_type' => 'chapter',
        'posts_per_page' => -1,
        'meta_query' => [
            [
                'key' => '_chapter_unique_id',
                'value' => $chapter_unique_id,
                'compare' => '='
            ]
        ],
        'post_status' => 'publish'
    ]);
    
    if (!empty($translations)) {
        // إذا لم يتم العثور على ترجمة مفضلة، اعرض أول ترجمة متوفرة
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: تم العثور على " . count($translations) . " ترجمة لهذا الفصل باستخدام معرف الفصل الفريد");
        }
        return $translations[0];
    }
    
    return null;
}

/**
 * حفظ المترجم المفضل في الكوكيز
 *
 * @param string $translator اسم المترجم
 * @return void
 */
function sekaiplu_set_preferred_translator($translator) {
    setcookie('preferred_translator', $translator, time() + (86400 * 30), '/'); // 30 يوم
}

/**
 * الحصول على المترجم المفضل من الكوكيز
 *
 * @return string اسم المترجم المفضل أو سلسلة فارغة
 */
function sekaiplu_get_preferred_translator() {
    if (isset($_COOKIE['preferred_translator'])) {
        return sanitize_text_field($_COOKIE['preferred_translator']);
    }
    return '';
}

/**
 * دالة تحديد سبب اختيار الترجمة (للتصحيح فقط)
 */
function sekaiplu_get_translation_selection_reason($post_id, $requested_author_id = '') {
    if (!empty($requested_author_id) && get_post_field('post_author', $post_id) == $requested_author_id) {
        return 'تم تحديدها من خلال معلمة URL (r)';
    }
    
    // تحقق من كوكيز المترجم
    if (isset($_COOKIE['preferred_translator'])) {
        $preferred_translator = sanitize_text_field($_COOKIE['preferred_translator']);
        $post_author_nicename = get_the_author_meta('user_nicename', get_post_field('post_author', $post_id));
        
        if ($post_author_nicename === $preferred_translator) {
            return 'تم تحديدها من خلال كوكيز المترجم المفضل (' . $preferred_translator . ')';
        }
    }
    
    return 'تم تحديدها كترجمة افتراضية';
}
