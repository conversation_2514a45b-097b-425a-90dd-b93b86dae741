<?php
/**
 * Elite Badge functionality for users registered before March 2025
 */

// Function to check if user is eligible for Elite badge
function is_elite_user($user_id) {
    $user = get_userdata($user_id);
    if (!$user) return false;
    
    $cutoff_date = strtotime('2025-03-01 00:00:00');
    $registration_date = strtotime($user->user_registered);
    
    return $registration_date < $cutoff_date;
}

// Function to display Elite badge
function display_elite_badge($user_id) {
    if (is_elite_user($user_id)) {
        echo '<span class="badge elite-badge">الصفوة<i class="fas fa-crown"></i></span>';
    }
}

// Add Elite badge styles
function add_elite_badge_styles() {
    ?>
    <style>
        .elite-badge {
            background-color: #ffd700;
            color: #000;
            margin-left: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .elite-badge i {
            color: #b8860b;
        }
    </style>
    <?php
}
add_action('wp_head', 'add_elite_badge_styles');
add_action('admin_head', 'add_elite_badge_styles');