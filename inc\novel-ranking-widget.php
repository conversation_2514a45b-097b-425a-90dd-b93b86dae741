<?php
/**
 * Widget تصنيف الروايات
 *
 * يعرض هذا الـ Widget قائمة بالروايات الأكثر شعبية حسب المشاهدات والتقييمات
 */

// تسجيل الـ Widget
function register_novel_ranking_widget() {
    register_widget('Sekaiplus_Novel_Ranking_Widget');
}
add_action('widgets_init', 'register_novel_ranking_widget');

/**
 * فئة Widget تصنيف الروايات
 */
class Sekaiplus_Novel_Ranking_Widget extends WP_Widget {

    /**
     * إعداد الـ Widget
     */
    public function __construct() {
        parent::__construct(
            'sekaiplus_novel_ranking', // معرف فريد
            'تصنيف الروايات', // اسم العرض
            array('description' => 'عرض تصنيف الروايات الأكثر شعبية حسب الفترة الزمنية') // وصف
        );
    }

    /**
     * عرض الـ Widget في الواجهة الأمامية
     */
    public function widget($args, $instance) {
        // استخراج الإعدادات
        $title = apply_filters('widget_title', $instance['title']);
        $show_daily = isset($instance['show_daily']) ? $instance['show_daily'] : true;
        $show_weekly = isset($instance['show_weekly']) ? $instance['show_weekly'] : true;
        $show_monthly = isset($instance['show_monthly']) ? $instance['show_monthly'] : true;
        $novels_count = isset($instance['novels_count']) ? absint($instance['novels_count']) : 5;
        $dark_theme = isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true';
        
        // بداية الـ Widget
        echo $args['before_widget'];
        
        // عنوان الـ Widget
        if (!empty($title)) {
            echo $args['before_title'] . $title . $args['after_title'];
        }
        
        // تحديد الفترة النشطة افتراضيًا
        $active_period = 'daily';
        if (isset($_COOKIE['novel_ranking_period'])) {
            $active_period = $_COOKIE['novel_ranking_period'];
        }
        
        // HTML الـ Widget
        ?>
        <div class="novel-ranking-widget <?php echo $dark_theme ? 'dark-theme' : ''; ?>">
            <!-- أزرار التبديل بين الفترات -->
            <div class="ranking-tabs">
                <div class="ranking-tabs-inner">
                    <?php if ($show_daily): ?>
                    <button class="ranking-tab <?php echo $active_period == 'daily' ? 'active' : ''; ?>" data-period="daily">يومي</button>
                    <?php endif; ?>
                    
                    <?php if ($show_weekly): ?>
                    <button class="ranking-tab <?php echo $active_period == 'weekly' ? 'active' : ''; ?>" data-period="weekly">أسبوعي</button>
                    <?php endif; ?>
                    
                    <?php if ($show_monthly): ?>
                    <button class="ranking-tab <?php echo $active_period == 'monthly' ? 'active' : ''; ?>" data-period="monthly">شهري</button>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- قوائم الروايات حسب الفترة -->
            <div class="ranking-content">
                <?php 
                // عرض قائمة الروايات لكل فترة
                $periods = array();
                if ($show_daily) $periods[] = 'daily';
                if ($show_weekly) $periods[] = 'weekly';
                if ($show_monthly) $periods[] = 'monthly';
                
                foreach ($periods as $period): 
                    // الحصول على الروايات حسب الفترة
                    $novels = $this->get_top_novels($period, $novels_count);
                ?>
                <div class="ranking-list <?php echo $period; ?>-list <?php echo $active_period == $period ? 'active' : ''; ?>">
                    <?php if (!empty($novels)): ?>
                        <?php foreach ($novels as $index => $novel): ?>
                            <div class="ranking-novel-item">
                                <a href="<?php echo get_permalink($novel->ID); ?>" class="ranking-novel-link">
                                    <div class="ranking-novel-number">#<?php echo $index + 1; ?></div>
                                    <div class="ranking-novel-thumbnail">
                                        <?php if (has_post_thumbnail($novel->ID)): ?>
                                            <?php echo get_the_post_thumbnail($novel->ID, 'thumbnail', array('class' => 'novel-cover')); ?>
                                        <?php else: ?>
                                            <div class="novel-cover-placeholder"></div>
                                        <?php endif; ?>
                                        <?php 
                                        // عرض شارة الترجمة الآلية إذا كانت موجودة
                                        $is_ai_translated = get_post_meta($novel->ID, 'ai_translated', true);
                                        if ($is_ai_translated): 
                                        ?>
                                        <div class="ai-badge">AI</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ranking-novel-info">
                                        <h4 class="ranking-novel-title"><?php echo get_the_title($novel->ID); ?></h4>
                                        <div class="ranking-novel-meta">
                                            <span class="views-count">
                                                <i class="fas fa-eye"></i> <?php echo number_format($this->get_novel_views($novel->ID, $period)); ?>
                                            </span>
                                            <span class="rating">
                                                <i class="fas fa-star"></i> <?php echo $this->get_novel_rating($novel->ID); ?>
                                            </span>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        <?php endforeach; ?>
                        <div class="ranking-see-more">
                            <a href="<?php echo home_url('/novels-ranking/'); ?>" class="btn btn-outline-primary btn-sm">عرض المزيد</a>
                        </div>
                    <?php else: ?>
                        <div class="no-novels-message">لا توجد روايات متاحة حاليًا</div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // تبديل الفترات
            $('.ranking-tab').on('click', function() {
                const period = $(this).data('period');
                
                // تحديث الزر النشط
                $('.ranking-tab').removeClass('active');
                $(this).addClass('active');
                
                // تحديث القائمة النشطة
                $('.ranking-list').removeClass('active');
                $('.' + period + '-list').addClass('active');
                
                // حفظ التفضيل في كوكي
                document.cookie = 'novel_ranking_period=' + period + '; path=/';
            });
        });
        </script>
        
        <style>
        .novel-ranking-widget {
            background-color: #f8f9fa;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        }
        
        .novel-ranking-widget.dark-theme {
            background-color: #2a2d3a;
            color: #e4e6eb;
        }
        
        .ranking-tabs {
            position: relative;
            overflow-x: auto;
            white-space: nowrap;
            scrollbar-width: none;
            -ms-overflow-style: none;
            margin-bottom: 15px;
            padding: 0 15px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .novel-ranking-widget.dark-theme .ranking-tabs {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .ranking-tabs::-webkit-scrollbar {
            display: none;
        }
        
        .ranking-tabs-inner {
            display: flex;
            padding: 15px 0 0;
        }
        
        .ranking-tab {
            background: none;
            border: none;
            padding: 8px 20px;
            font-weight: 600;
            color: #6c757d;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        
        .novel-ranking-widget.dark-theme .ranking-tab {
            color: #a0a0a0;
        }
        
        .ranking-tab:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: transparent;
            transition: all 0.3s ease;
        }
        
        .ranking-tab.active {
            color: var(--bs-primary);
        }
        
        .ranking-tab.active:after {
            background-color: var(--bs-primary);
        }
        
        .ranking-content {
            padding: 15px;
        }
        
        .ranking-list {
            display: none;
        }
        
        .ranking-list.active {
            display: block;
        }
        
        .ranking-novel-item {
            margin-bottom: 15px;
            transition: transform 0.2s ease;
        }
        
        .ranking-novel-item:hover {
            transform: translateY(-2px);
        }
        
        .ranking-novel-link {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: inherit;
            background-color: rgba(255,255,255,0.6);
            border-radius: 10px;
            padding: 10px;
            transition: all 0.3s ease;
        }
        
        .novel-ranking-widget.dark-theme .ranking-novel-link {
            background-color: rgba(0,0,0,0.2);
        }
        
        .ranking-novel-link:hover {
            background-color: rgba(var(--bs-primary-rgb), 0.1);
        }
        
        .ranking-novel-number {
            font-weight: 700;
            font-size: 18px;
            width: 40px;
            text-align: center;
            color: var(--bs-primary);
        }
        
        .ranking-novel-thumbnail {
            width: 60px;
            height: 84px;
            margin-right: 15px;
            position: relative;
            overflow: hidden;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .novel-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .novel-cover-placeholder {
            width: 100%;
            height: 100%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .novel-ranking-widget.dark-theme .novel-cover-placeholder {
            background-color: #3a3d4a;
        }
        
        .ai-badge {
            position: absolute;
            bottom: 0;
            left: 0;
            background-color: rgba(0,123,255,0.8);
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 0 4px 0 0;
            font-weight: bold;
        }
        
        .ranking-novel-info {
            flex: 1;
        }
        
        .ranking-novel-title {
            font-size: 14px;
            margin: 0 0 5px;
            font-weight: 600;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .novel-ranking-widget.dark-theme .ranking-novel-title {
            color: #e4e6eb;
        }
        
        .ranking-novel-meta {
            display: flex;
            font-size: 12px;
            color: #6c757d;
            gap: 10px;
        }
        
        .novel-ranking-widget.dark-theme .ranking-novel-meta {
            color: #a0a0a0;
        }
        
        .views-count i, .rating i {
            margin-right: 3px;
            color: var(--bs-primary);
        }
        
        .ranking-see-more {
            text-align: center;
            margin-top: 15px;
        }
        
        .no-novels-message {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
        
        .novel-ranking-widget.dark-theme .no-novels-message {
            color: #a0a0a0;
        }
        </style>
        <?php
        
        // نهاية الـ Widget
        echo $args['after_widget'];
    }

    /**
     * نموذج الإعدادات في لوحة التحكم
     */
    public function form($instance) {
        // الإعدادات الافتراضية
        $defaults = array(
            'title' => 'تصنيف الروايات',
            'show_daily' => true,
            'show_weekly' => true,
            'show_monthly' => true,
            'novels_count' => 5
        );
        
        // دمج الإعدادات المحفوظة مع الافتراضية
        $instance = wp_parse_args((array) $instance, $defaults);
        ?>
        
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">العنوان:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" name="<?php echo $this->get_field_name('title'); ?>" type="text" value="<?php echo esc_attr($instance['title']); ?>">
        </p>
        
        <p>
            <input type="checkbox" id="<?php echo $this->get_field_id('show_daily'); ?>" name="<?php echo $this->get_field_name('show_daily'); ?>" <?php checked($instance['show_daily']); ?>>
            <label for="<?php echo $this->get_field_id('show_daily'); ?>">عرض التصنيف اليومي</label>
        </p>
        
        <p>
            <input type="checkbox" id="<?php echo $this->get_field_id('show_weekly'); ?>" name="<?php echo $this->get_field_name('show_weekly'); ?>" <?php checked($instance['show_weekly']); ?>>
            <label for="<?php echo $this->get_field_id('show_weekly'); ?>">عرض التصنيف الأسبوعي</label>
        </p>
        
        <p>
            <input type="checkbox" id="<?php echo $this->get_field_id('show_monthly'); ?>" name="<?php echo $this->get_field_name('show_monthly'); ?>" <?php checked($instance['show_monthly']); ?>>
            <label for="<?php echo $this->get_field_id('show_monthly'); ?>">عرض التصنيف الشهري</label>
        </p>
        
        <p>
            <label for="<?php echo $this->get_field_id('novels_count'); ?>">عدد الروايات:</label>
            <input class="tiny-text" id="<?php echo $this->get_field_id('novels_count'); ?>" name="<?php echo $this->get_field_name('novels_count'); ?>" type="number" min="1" max="10" value="<?php echo esc_attr($instance['novels_count']); ?>">
        </p>
        
        <?php
    }

    /**
     * حفظ إعدادات الـ Widget
     */
    public function update($new_instance, $old_instance) {
        $instance = $old_instance;
        
        $instance['title'] = sanitize_text_field($new_instance['title']);
        $instance['show_daily'] = isset($new_instance['show_daily']) ? 1 : 0;
        $instance['show_weekly'] = isset($new_instance['show_weekly']) ? 1 : 0;
        $instance['show_monthly'] = isset($new_instance['show_monthly']) ? 1 : 0;
        $instance['novels_count'] = absint($new_instance['novels_count']);
        
        return $instance;
    }
    
    /**
     * الحصول على الروايات الأعلى مشاهدة حسب الفترة
     */
    private function get_top_novels($period, $count) {
        $args = array(
            'post_type' => 'novel',
            'posts_per_page' => $count,
            'meta_key' => $this->get_views_meta_key($period),
            'orderby' => 'meta_value_num',
            'order' => 'DESC',
            'meta_query' => array(
                array(
                    'key' => $this->get_views_meta_key($period),
                    'compare' => 'EXISTS'
                )
            )
        );
        
        $query = new WP_Query($args);
        return $query->posts;
    }
    
    /**
     * الحصول على مفتاح البيانات الوصفية للمشاهدات حسب الفترة
     */
    private function get_views_meta_key($period) {
        switch ($period) {
            case 'daily':
                return 'novel_views_daily';
            case 'weekly':
                return 'novel_views_weekly';
            case 'monthly':
                return 'novel_views_monthly';
            default:
                return 'novel_views_total';
        }
    }
    
    /**
     * الحصول على عدد مشاهدات الرواية
     */
    private function get_novel_views($novel_id, $period) {
        $views = get_post_meta($novel_id, $this->get_views_meta_key($period), true);
        return empty($views) ? 0 : intval($views);
    }
    
    /**
     * الحصول على تقييم الرواية
     */
    private function get_novel_rating($novel_id) {
        $rating = get_post_meta($novel_id, 'novel_rating', true);
        return empty($rating) ? '0.0' : number_format(floatval($rating), 1);
    }
}
