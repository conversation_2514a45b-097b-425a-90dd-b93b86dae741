/* ===== أنماط صفحة الخطأ 404 ===== */
:root {
    --error-primary: #0056b3;
    --error-secondary: #6c757d;
    --error-accent: #f39c12;
    --error-bg: #f8f9fa;
    --error-text: #333333;
    --error-card: #ffffff;
    --error-border: #dee2e6;
    --error-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* دعم الوضع الداكن */
.dark-mode {
    --error-bg: #1a1a2e;
    --error-card: #2d2d2d;
    --error-text: #e3e3e3;
    --error-border: #404040;
    --error-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.error-page-container {
    background-color: var(--error-bg);
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    font-family: 'Cairo', '<PERSON><PERSON>', sans-serif;
}

.error-content {
    max-width: 800px;
    width: 100%;
    text-align: center;
    background-color: var(--error-card);
    padding: 3rem 2rem;
    border-radius: 15px;
    box-shadow: var(--error-shadow);
    position: relative;
    overflow: hidden;
}

.error-animation {
    position: relative;
    height: 180px;
    margin-bottom: 2rem;
}

.error-book {
    position: absolute;
    width: 100px;
    height: 140px;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    perspective: 1000px;
    transform-style: preserve-3d;
    animation: float 4s ease-in-out infinite;
}

.book-page {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: var(--error-card);
    border: 2px solid var(--error-primary);
    border-radius: 5px 15px 15px 5px;
    transform-origin: left center;
    transition: transform 0.5s ease;
}

.book-page:nth-child(1) {
    z-index: 3;
    animation: flip-page-1 5s infinite;
}

.book-page:nth-child(2) {
    z-index: 2;
    animation: flip-page-2 5s infinite;
}

.book-page:nth-child(3) {
    z-index: 1;
    animation: flip-page-3 5s infinite;
}

.error-code {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    font-weight: 800;
    color: var(--error-primary);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
    animation: pulse 2s infinite;
}

.error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--error-text);
    margin-bottom: 1rem;
}

.error-message {
    font-size: 1.2rem;
    color: var(--error-secondary);
    margin-bottom: 2rem;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.error-actions .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.error-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.error-search {
    max-width: 500px;
    margin: 0 auto;
}

.error-search .form-control {
    border-radius: 50px 0 0 50px;
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--error-border);
}

.error-search .btn {
    border-radius: 0 50px 50px 0;
    padding: 0.75rem 1.5rem;
}

.error-suggestions h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--error-text);
    margin-bottom: 1rem;
}

.suggestion-card {
    height: 200px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.suggestion-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.suggestion-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    position: relative;
}

.suggestion-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

.suggestion-title {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    color: white;
    font-weight: 600;
    z-index: 1;
}

.suggestion-link {
    display: block;
    width: 100%;
    height: 100%;
    text-decoration: none;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-15px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes flip-page-1 {
    0%, 30% {
        transform: rotateY(0);
    }
    40%, 60% {
        transform: rotateY(-120deg);
    }
    70%, 100% {
        transform: rotateY(0);
    }
}

@keyframes flip-page-2 {
    0%, 35% {
        transform: rotateY(0);
    }
    45%, 65% {
        transform: rotateY(-120deg);
    }
    75%, 100% {
        transform: rotateY(0);
    }
}

@keyframes flip-page-3 {
    0%, 40% {
        transform: rotateY(0);
    }
    50%, 70% {
        transform: rotateY(-120deg);
    }
    80%, 100% {
        transform: rotateY(0);
    }
}

/* Error Particles */
.error-particle {
    position: absolute;
    border-radius: 50%;
    background-color: var(--error-primary);
    pointer-events: none;
    z-index: 0;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .error-title {
        font-size: 2rem;
    }
    
    .error-message {
        font-size: 1rem;
    }
    
    .error-actions {
        flex-direction: column;
    }
    
    .error-code {
        font-size: 4rem;
    }
}

/* Dark Mode Adjustments */
.dark-mode .error-content {
    background-color: var(--error-card);
}

.dark-mode .book-page {
    background-color: #3a3a3a;
    border-color: var(--error-primary);
}

.dark-mode .error-title,
.dark-mode .error-suggestions h3 {
    color: var(--error-text);
}

.dark-mode .error-message {
    color: #adb5bd;
}

.dark-mode .error-search .form-control {
    background-color: #333;
    border-color: var(--error-border);
    color: #fff;
}

.dark-mode .suggestion-card {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.dark-mode .error-particle {
    background-color: rgba(102, 176, 255, 0.3);
}
