<?php
/**
 * Test file for debugging the search functionality
 */

// Load WordPress
require_once('../../../wp-load.php');

// Set headers
header('Content-Type: text/html; charset=utf-8');

// Check if this is a search request
if (isset($_POST['search_query'])) {
    // Manually call the search function
    $_POST['action'] = 'sekaiplus_quick_search';
    $_POST['nonce'] = wp_create_nonce('sekaiplus_ajax_nonce');
    $_POST['query'] = sanitize_text_field($_POST['search_query']);
    
    // Debug information
    echo '<div class="debug-info" style="background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px;">';
    echo '<h3>Debug Information</h3>';
    echo '<p><strong>Nonce:</strong> ' . $_POST['nonce'] . '</p>';
    echo '<p><strong>Query:</strong> ' . $_POST['query'] . '</p>';
    echo '<p><strong>Action:</strong> ' . $_POST['action'] . '</p>';
    echo '</div>';
    
    // Call the search function directly
    ob_start();
    do_action('wp_ajax_' . $_POST['action']);
    $search_results = ob_get_clean();
    
    // Display the results
    echo '<div class="search-results-container">';
    echo $search_results;
    echo '</div>';
    
    exit;
}

// Get the site URL
$site_url = site_url();

// HTML for the test page
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث السريع</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        h1 {
            margin-bottom: 30px;
            color: #343a40;
        }
        .search-box {
            margin-bottom: 30px;
        }
        .search-result {
            transition: background-color 0.2s ease;
        }
        .search-result:hover {
            background-color: rgba(13, 110, 253, 0.05);
        }
        .search-category {
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .debug-info {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .ajax-debug {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center">اختبار البحث السريع</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>البحث المباشر (PHP)</h3>
                <form method="post" action="">
                    <div class="search-box">
                        <div class="input-group">
                            <input type="text" name="search_query" class="form-control" placeholder="اكتب كلمة البحث هنا..." required>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <div class="col-md-6">
                <h3>البحث بالأجاكس (AJAX)</h3>
                <div class="search-box">
                    <div class="input-group">
                        <input type="text" id="ajaxSearch" class="form-control" placeholder="اكتب كلمة البحث هنا...">
                        <button type="button" id="ajaxSearchBtn" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
                
                <div id="ajaxResults" class="quick-search-results shadow-lg rounded-4 w-100 bg-white mt-2"></div>
                
                <div id="ajaxDebug" class="ajax-debug">
                    <h4>معلومات التصحيح</h4>
                    <div id="ajaxDebugInfo"></div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>روابط مفيدة</h3>
            <ul>
                <li><a href="<?php echo $site_url; ?>" target="_blank">الصفحة الرئيسية</a></li>
                <li><a href="<?php echo admin_url('admin-ajax.php'); ?>" target="_blank">عنوان AJAX</a></li>
            </ul>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            console.log('Test page loaded');
            
            // AJAX search
            $('#ajaxSearchBtn').on('click', function() {
                performAjaxSearch();
            });
            
            $('#ajaxSearch').on('keypress', function(e) {
                if (e.which === 13) {
                    e.preventDefault();
                    performAjaxSearch();
                }
            });
            
            function performAjaxSearch() {
                const query = $('#ajaxSearch').val();
                
                if (query.length < 2) {
                    $('#ajaxResults').html('<div class="p-4 text-center text-muted">يرجى إدخال حرفين على الأقل</div>');
                    $('#ajaxResults').show();
                    return;
                }
                
                // Show loading indicator
                $('#ajaxResults').html('<div class="p-4 text-center"><i class="fas fa-spinner fa-spin"></i> جاري البحث...</div>');
                $('#ajaxResults').show();
                
                // Debug info
                const nonce = '<?php echo wp_create_nonce("sekaiplus_ajax_nonce"); ?>';
                $('#ajaxDebugInfo').html(
                    '<p><strong>Nonce:</strong> ' + nonce + '</p>' +
                    '<p><strong>Query:</strong> ' + query + '</p>' +
                    '<p><strong>Action:</strong> sekaiplus_quick_search</p>' +
                    '<p><strong>AJAX URL:</strong> <?php echo admin_url("admin-ajax.php"); ?></p>'
                );
                $('#ajaxDebug').show();
                
                // Send AJAX request
                $.ajax({
                    url: '<?php echo admin_url("admin-ajax.php"); ?>',
                    type: 'POST',
                    data: {
                        action: 'sekaiplus_quick_search',
                        nonce: nonce,
                        query: query
                    },
                    success: function(response) {
                        console.log('AJAX response received');
                        $('#ajaxResults').html(response);
                        $('#ajaxDebugInfo').append('<p><strong>Status:</strong> Success</p>');
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', error);
                        $('#ajaxResults').html('<div class="p-4 text-center text-danger">حدث خطأ أثناء البحث</div>');
                        $('#ajaxDebugInfo').append(
                            '<p><strong>Status:</strong> Error</p>' +
                            '<p><strong>Error:</strong> ' + error + '</p>' +
                            '<p><strong>Response:</strong> ' + xhr.responseText + '</p>'
                        );
                    }
                });
            }
        });
    </script>
</body>
</html>
