<?php
/**
 * Theme Customizer settings
 */

function sekaiplus_customize_register($wp_customize) {
    // Add Theme Settings Panel
    $wp_customize->add_panel('sekaiplus_settings', array(
        'title' => __('إعدادات القالب', 'sekaiplus'),
        'priority' => 30,
    ));

    // General Settings Section
    $wp_customize->add_section('sekaiplus_general_settings', array(
        'title' => __('الإعدادات العامة', 'sekaiplus'),
        'panel' => 'sekaiplus_settings',
    ));

    // Dark Mode Default
    $wp_customize->add_setting('sekaiplus_dark_mode_default', array(
        'default' => false,
        'sanitize_callback' => 'sekaiplus_sanitize_checkbox',
    ));

    $wp_customize->add_control('sekaiplus_dark_mode_default', array(
        'type' => 'checkbox',
        'section' => 'sekaiplus_general_settings',
        'label' => __('تفعيل الوضع الليلي افتراضياً', 'sekaiplus'),
    ));

    // Homepage Settings Section
    $wp_customize->add_section('sekaiplus_homepage_settings', array(
        'title' => __('إعدادات الصفحة الرئيسية', 'sekaiplus'),
        'panel' => 'sekaiplus_settings',
    ));

    // Latest Novels Count
    $wp_customize->add_setting('sekaiplus_latest_novels_count', array(
        'default' => 6,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('sekaiplus_latest_novels_count', array(
        'type' => 'number',
        'section' => 'sekaiplus_homepage_settings',
        'label' => __('عدد الروايات في قسم أحدث الروايات', 'sekaiplus'),
        'input_attrs' => array(
            'min' => 3,
            'max' => 12,
            'step' => 3,
        ),
    ));

    // Latest Chapters Count
    $wp_customize->add_setting('sekaiplus_latest_chapters_count', array(
        'default' => 10,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('sekaiplus_latest_chapters_count', array(
        'type' => 'number',
        'section' => 'sekaiplus_homepage_settings',
        'label' => __('عدد الفصول في قسم أحدث الفصول', 'sekaiplus'),
        'input_attrs' => array(
            'min' => 5,
            'max' => 20,
            'step' => 5,
        ),
    ));

    // Novel Settings Section
    $wp_customize->add_section('sekaiplus_novel_settings', array(
        'title' => __('إعدادات صفحة الرواية', 'sekaiplus'),
        'panel' => 'sekaiplus_settings',
    ));

    // Chapters Per Page
    $wp_customize->add_setting('sekaiplus_chapters_per_page', array(
        'default' => 20,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('sekaiplus_chapters_per_page', array(
        'type' => 'number',
        'section' => 'sekaiplus_novel_settings',
        'label' => __('عدد الفصول في كل صفحة', 'sekaiplus'),
        'input_attrs' => array(
            'min' => 10,
            'max' => 50,
            'step' => 5,
        ),
    ));

    // Chapter Settings Section
    $wp_customize->add_section('sekaiplus_chapter_settings', array(
        'title' => __('إعدادات صفحة الفصل', 'sekaiplus'),
        'panel' => 'sekaiplus_settings',
    ));

    // Font Size
    $wp_customize->add_setting('sekaiplus_chapter_font_size', array(
        'default' => 18,
        'sanitize_callback' => 'absint',
    ));

    $wp_customize->add_control('sekaiplus_chapter_font_size', array(
        'type' => 'number',
        'section' => 'sekaiplus_chapter_settings',
        'label' => __('حجم الخط الافتراضي', 'sekaiplus'),
        'input_attrs' => array(
            'min' => 14,
            'max' => 24,
            'step' => 1,
        ),
    ));

    // Line Height
    $wp_customize->add_setting('sekaiplus_chapter_line_height', array(
        'default' => 2,
        'sanitize_callback' => 'sekaiplus_sanitize_float',
    ));

    $wp_customize->add_control('sekaiplus_chapter_line_height', array(
        'type' => 'number',
        'section' => 'sekaiplus_chapter_settings',
        'label' => __('المسافة بين السطور', 'sekaiplus'),
        'input_attrs' => array(
            'min' => 1.5,
            'max' => 3,
            'step' => 0.1,
        ),
    ));

    // Footer Settings Section
    $wp_customize->add_section('sekaiplus_footer_settings', array(
        'title' => __('إعدادات الفوتر', 'sekaiplus'),
        'panel' => 'sekaiplus_settings',
    ));

    // Footer Text
    $wp_customize->add_setting('sekaiplus_footer_text', array(
        'default' => '',
        'sanitize_callback' => 'wp_kses_post',
    ));

    $wp_customize->add_control('sekaiplus_footer_text', array(
        'type' => 'textarea',
        'section' => 'sekaiplus_footer_settings',
        'label' => __('نص الفوتر', 'sekaiplus'),
        'description' => __('يمكنك استخدام الوسوم HTML. استخدم {year} لإظهار السنة الحالية.', 'sekaiplus'),
    ));
}
add_action('customize_register', 'sekaiplus_customize_register');

/**
 * Sanitize checkbox values
 */
function sekaiplus_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Sanitize float values
 */
function sekaiplus_sanitize_float($number, $setting) {
    return (is_float($number) || is_numeric($number)) ? floatval($number) : $setting->default;
}

/**
 * Generate CSS based on customizer settings
 */
function sekaiplus_customizer_css() {
    $font_size = get_theme_mod('sekaiplus_chapter_font_size', 18);
    $line_height = get_theme_mod('sekaiplus_chapter_line_height', 2);
    ?>
    <style type="text/css">
        .chapter-content {
            font-size: <?php echo esc_attr($font_size); ?>px;
            line-height: <?php echo esc_attr($line_height); ?>;
        }
    </style>
    <?php
}
add_action('wp_head', 'sekaiplus_customizer_css');

/**
 * Process footer text
 */
function sekaiplus_get_footer_text() {
    $text = get_theme_mod('sekaiplus_footer_text');
    if (!$text) {
        return '&copy; ' . date('Y') . ' ' . get_bloginfo('name') . '. ' . __('جميع الحقوق محفوظة.', 'sekaiplus');
    }
    return str_replace('{year}', date('Y'), $text);
}
