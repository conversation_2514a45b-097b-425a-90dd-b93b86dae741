<?php
/**
 * ملف وظائف AJAX للروايات
 *
 * يوفر وظائف AJAX للحصول على عدد الفصول الدقيق والتقييمات للروايات
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * تسجيل وظائف AJAX
 */
function sekaiplus_register_novel_ajax_actions() {
    add_action('wp_ajax_get_accurate_chapter_count', 'sekaiplus_ajax_get_accurate_chapter_count');
    add_action('wp_ajax_nopriv_get_accurate_chapter_count', 'sekaiplus_ajax_get_accurate_chapter_count');
    
    add_action('wp_ajax_get_accurate_rating', 'sekaiplus_ajax_get_accurate_rating');
    add_action('wp_ajax_nopriv_get_accurate_rating', 'sekaiplus_ajax_get_accurate_rating');
}
add_action('init', 'sekaiplus_register_novel_ajax_actions');

/**
 * الحصول على العدد الدقيق للفصول الفريدة للرواية
 */
function sekaiplus_ajax_get_accurate_chapter_count() {
    // التحقق من الأمان
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekaiplus_ajax_nonce')) {
        wp_send_json_error('خطأ في التحقق من الأمان');
    }
    
    // التحقق من معرف الرواية
    if (!isset($_POST['novel_id']) || empty($_POST['novel_id'])) {
        wp_send_json_error('معرف الرواية غير صالح');
    }
    
    $novel_id = intval($_POST['novel_id']);
    
    // الحصول على عدد الفصول الفريدة
    global $wpdb;
    $unique_chapters = $wpdb->get_col($wpdb->prepare(
        "SELECT DISTINCT meta_value FROM {$wpdb->postmeta} pm 
        JOIN {$wpdb->posts} p ON p.ID = pm.post_id 
        WHERE pm.meta_key = 'chapter_unique_id' 
        AND p.post_type = 'chapter' 
        AND p.post_parent = %d",
        $novel_id
    ));
    
    $chapter_count = count($unique_chapters);
    
    // إذا لم يتم العثور على فصول فريدة، استخدم الطريقة التقليدية
    if ($chapter_count === 0) {
        $chapters = get_posts(array(
            'post_type' => 'chapter',
            'post_parent' => $novel_id,
            'posts_per_page' => -1,
            'fields' => 'ids'
        ));
        $chapter_count = count($chapters);
    }
    
    wp_send_json_success($chapter_count);
}

/**
 * الحصول على التقييم الدقيق للرواية
 */
function sekaiplus_ajax_get_accurate_rating() {
    // التحقق من الأمان
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sekaiplus_ajax_nonce')) {
        wp_send_json_error('خطأ في التحقق من الأمان');
    }
    
    // التحقق من معرف الرواية
    if (!isset($_POST['novel_id']) || empty($_POST['novel_id'])) {
        wp_send_json_error('معرف الرواية غير صالح');
    }
    
    $novel_id = intval($_POST['novel_id']);
    
    // الحصول على تقييمات المستخدمين
    $ratings = get_post_meta($novel_id, '_novel_ratings', true);
    $rating = 0;
    
    if (is_array($ratings) && !empty($ratings)) {
        $total = 0;
        $count = 0;
        foreach ($ratings as $user_rating) {
            $total += $user_rating;
            $count++;
        }
        if ($count > 0) {
            $rating = round($total / $count, 1);
        }
    } else {
        // إذا لم تكن هناك تقييمات متعددة، استخدم التقييم الأساسي
        $rating = get_post_meta($novel_id, '_novel_rating', true);
        $rating = $rating ? round($rating, 1) : 0;
    }
    
    wp_send_json_success($rating);
}
