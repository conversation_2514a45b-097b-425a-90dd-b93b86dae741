</main>

<footer class="footer mt-auto py-4">
    <div class="container-fluid px-4">
        <div class="row align-items-center gy-3">
            <div class="col-md-6">
                <div class="footer-text">
                    <?php if (get_theme_mod('sekaiplus_footer_text')): ?>
                        <?php echo get_theme_mod('sekaiplus_footer_text'); ?>
                    <?php else: ?>
                        &copy; <?php echo date('Y'); ?> <?php bloginfo('name'); ?>. جميع الحقوق محفوظة.
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-md-6">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'footer',
                    'container' => false,
                    'menu_class' => 'nav justify-content-md-end justify-content-center',
                    'fallback_cb' => '__return_false',
                    'items_wrap' => '<ul id="%1$s" class="%2$s">%3$s</ul>',
                    'depth' => 1,
                    'walker' => new Bootstrap_5_Nav_Menu_Walker()
                ));
                ?>
            </div>
        </div>
    </div>
</footer>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="position-absolute top-50 start-50 translate-middle text-white">
        <div class="loading-spinner mb-2"></div>
        <div>جارٍ التحميل...</div>
    </div>
</div>



<!-- Go to Top Button -->
<button id="goToTop" class="btn btn-primary rounded-circle position-fixed bottom-0 <?php echo is_rtl() ? 'start-0' : 'end-0'; ?> m-4" style="display: none;">
    <i class="fas fa-arrow-up"></i>
</button>

<?php wp_footer(); ?>

<script>
    // Go to Top Button
    const goToTopBtn = document.getElementById('goToTop');
    
    window.onscroll = function() {
        if (document.body.scrollTop > 500 || document.documentElement.scrollTop > 500) {
            goToTopBtn.style.display = "block";
        } else {
            goToTopBtn.style.display = "none";
        }
    };
    
    goToTopBtn.onclick = function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    };
    function showSekaiplusToast(msg, timeout = 3500) {
    var toast = document.getElementById('sekaiplus-toast');
    var msgSpan = document.getElementById('sekaiplus-toast-msg');
    var closeBtn = document.getElementById('sekaiplus-toast-close');
    msgSpan.textContent = msg;
    toast.classList.add('show');
    toast.style.display = 'flex';
    // إغلاق يدوي
    closeBtn.onclick = function() {
        toast.classList.remove('show');
        setTimeout(() => { toast.style.display = 'none'; }, 300);
    };
    // إغلاق تلقائي
    if (timeout > 0) {
        setTimeout(function() {
            toast.classList.remove('show');
            setTimeout(() => { toast.style.display = 'none'; }, 300);
        }, timeout);
    }
}
</script>
<div id="sekaiplus-toast" class="sekaiplus-toast" style="display:none;">
    <span id="sekaiplus-toast-msg"></span>
    <button id="sekaiplus-toast-close" aria-label="إغلاق">&times;</button>
</div>
</body>
</html>
