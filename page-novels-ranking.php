<?php
/**
 * قالب صفحة تصنيف الروايات
 *
 * Template Name: صفحة تصنيف الروايات
 */

get_header();

// تحقق مما إذا كان الوضع الداكن مفعل
$dark_mode = isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true';

// تحديد الفترة النشطة افتراضياً
$active_period = 'daily';
if (isset($_GET['period']) && in_array($_GET['period'], array('daily', 'weekly', 'monthly'))) {
    $active_period = $_GET['period'];
}

// عدد الروايات للعرض
$novels_count = 20;
?>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="novels-ranking-page <?php echo $dark_mode ? 'dark-theme' : ''; ?>">
                <div class="section-header text-center mb-5">
                    <h1 class="display-5 fw-bold mb-3"><?php the_title(); ?></h1>
                    <p class="lead text-muted">اكتشف أكثر الروايات شعبية ومشاهدة على موقعنا</p>
                </div>
                
                <!-- أزرار التبديل بين الفترات -->
                <div class="ranking-tabs mb-4">
                    <div class="ranking-tabs-inner">
                        <a href="?period=daily" class="ranking-tab <?php echo $active_period == 'daily' ? 'active' : ''; ?>">يومي</a>
                        <a href="?period=weekly" class="ranking-tab <?php echo $active_period == 'weekly' ? 'active' : ''; ?>">أسبوعي</a>
                        <a href="?period=monthly" class="ranking-tab <?php echo $active_period == 'monthly' ? 'active' : ''; ?>">شهري</a>
                    </div>
                </div>
                
                <!-- قائمة الروايات -->
                <div class="ranking-list">
                    <?php
                    // الحصول على الروايات حسب الفترة
                    $meta_key = '';
                    switch ($active_period) {
                        case 'daily':
                            $meta_key = 'novel_views_daily';
                            break;
                        case 'weekly':
                            $meta_key = 'novel_views_weekly';
                            break;
                        case 'monthly':
                            $meta_key = 'novel_views_monthly';
                            break;
                    }
                    
                    $args = array(
                        'post_type' => 'novel',
                        'posts_per_page' => $novels_count,
                        'meta_key' => $meta_key,
                        'orderby' => 'meta_value_num',
                        'order' => 'DESC',
                        'meta_query' => array(
                            array(
                                'key' => $meta_key,
                                'compare' => 'EXISTS'
                            )
                        )
                    );
                    
                    $novels_query = new WP_Query($args);
                    
                    if ($novels_query->have_posts()):
                        $rank = 1;
                        while ($novels_query->have_posts()): $novels_query->the_post();
                            $novel_id = get_the_ID();
                            $views = get_post_meta($novel_id, $meta_key, true);
                            $views = empty($views) ? 0 : intval($views);
                            $rating = get_post_meta($novel_id, 'novel_rating', true);
                            $rating = empty($rating) ? '0.0' : number_format(floatval($rating), 1);
                            $is_ai_translated = get_post_meta($novel_id, 'ai_translated', true);
                    ?>
                    <div class="ranking-novel-item">
                        <div class="ranking-novel-card">
                            <div class="ranking-number"><?php echo $rank; ?></div>
                            <div class="ranking-novel-thumbnail">
                                <a href="<?php the_permalink(); ?>">
                                    <?php if (has_post_thumbnail()): ?>
                                        <?php the_post_thumbnail('medium', array('class' => 'novel-cover')); ?>
                                    <?php else: ?>
                                        <div class="novel-cover-placeholder"></div>
                                    <?php endif; ?>
                                    <?php if ($is_ai_translated): ?>
                                    <div class="ai-badge">AI</div>
                                    <?php endif; ?>
                                </a>
                            </div>
                            <div class="ranking-novel-info">
                                <h3 class="ranking-novel-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h3>
                                
                                <?php
                                // الحصول على العناوين البديلة
                                $romaji_title = get_post_meta($novel_id, 'romaji_title', true);
                                $japanese_title = get_post_meta($novel_id, 'japanese_title', true);
                                $english_title = get_post_meta($novel_id, 'english_title', true);
                                
                                if (!empty($romaji_title) || !empty($japanese_title) || !empty($english_title)):
                                ?>
                                <div class="alternative-titles">
                                    <?php if (!empty($romaji_title)): ?>
                                    <div class="alt-title"><span class="alt-title-label">العنوان الروماجي:</span> <?php echo esc_html($romaji_title); ?></div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($japanese_title)): ?>
                                    <div class="alt-title"><span class="alt-title-label">العنوان الياباني:</span> <?php echo esc_html($japanese_title); ?></div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($english_title)): ?>
                                    <div class="alt-title"><span class="alt-title-label">العنوان الإنجليزي:</span> <?php echo esc_html($english_title); ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                                
                                <div class="ranking-novel-meta">
                                    <div class="views-count">
                                        <i class="fas fa-eye"></i> <?php echo number_format($views); ?> مشاهدة
                                    </div>
                                    <div class="rating">
                                        <i class="fas fa-star"></i> <?php echo $rating; ?>
                                    </div>
                                </div>
                                
                                <div class="ranking-novel-genres">
                                    <?php
                                    $genres = get_the_terms($novel_id, 'genre');
                                    if ($genres && !is_wp_error($genres)):
                                        foreach ($genres as $genre):
                                    ?>
                                    <a href="<?php echo get_term_link($genre); ?>" class="genre-badge"><?php echo $genre->name; ?></a>
                                    <?php
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                                
                                <div class="ranking-novel-excerpt">
                                    <?php echo wp_trim_words(get_the_excerpt(), 30, '...'); ?>
                                </div>
                                
                                <?php
                                // عرض المترجمين للرواية
                                $translators = get_post_meta($novel_id, 'novel_translators', true);
                                if (!empty($translators) && is_array($translators)):
                                ?>
                                <div class="novel-translators">
                                    <span class="translators-label">المترجمين:</span>
                                    <div class="translators-list">
                                        <?php foreach ($translators as $translator_id): 
                                            $translator = get_userdata($translator_id);
                                            if ($translator):
                                        ?>
                                        <a href="<?php echo get_author_posts_url($translator_id); ?>" class="translator-link">
                                            <?php echo esc_html($translator->display_name); ?>
                                            <?php if (is_elite_user($translator_id)): ?>
                                            <span class="badge elite-badge">النخبة <i class="fas fa-crown"></i></span>
                                            <?php endif; ?>
                                        </a>
                                        <?php 
                                            endif;
                                        endforeach; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                        $rank++;
                        endwhile;
                        wp_reset_postdata();
                    else:
                    ?>
                    <div class="no-novels-message">لا توجد روايات متاحة حالياً</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* تنسيقات صفحة تصنيف الروايات */
.novels-ranking-page {
    background-color: #fff;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.novels-ranking-page.dark-theme {
    background-color: #2a2d3a;
    color: #e4e6eb;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.ranking-tabs {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.novels-ranking-page.dark-theme .ranking-tabs {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.ranking-tabs-inner {
    display: flex;
    gap: 10px;
}

.ranking-tab {
    padding: 10px 25px;
    font-weight: 600;
    color: #6c757d;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    font-size: 18px;
}

.novels-ranking-page.dark-theme .ranking-tab {
    color: #a0a0a0;
}

.ranking-tab:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: transparent;
    transition: all 0.3s ease;
}

.ranking-tab.active {
    color: var(--bs-primary);
}

.ranking-tab.active:after {
    background-color: var(--bs-primary);
}

.ranking-novel-item {
    margin-bottom: 25px;
}

.ranking-novel-card {
    display: flex;
    background-color: rgba(0,0,0,0.02);
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.novels-ranking-page.dark-theme .ranking-novel-card {
    background-color: rgba(255,255,255,0.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ranking-novel-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.ranking-number {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background-color: var(--bs-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.ranking-novel-thumbnail {
    width: 120px;
    min-width: 120px;
    height: 180px;
    position: relative;
    overflow: hidden;
}

.novel-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.ranking-novel-card:hover .novel-cover {
    transform: scale(1.05);
}

.novel-cover-placeholder {
    width: 100%;
    height: 100%;
    background-color: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
}

.novels-ranking-page.dark-theme .novel-cover-placeholder {
    background-color: #3a3d4a;
}

.ai-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: rgba(0,123,255,0.8);
    color: white;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 0 4px 0 0;
    font-weight: bold;
}

.ranking-novel-info {
    padding: 15px;
    flex: 1;
}

.ranking-novel-title {
    font-size: 18px;
    margin: 0 0 10px;
    font-weight: 700;
    line-height: 1.3;
}

.ranking-novel-title a {
    color: inherit;
    text-decoration: none;
}

.novels-ranking-page.dark-theme .ranking-novel-title a {
    color: #e4e6eb;
}

.alternative-titles {
    margin-bottom: 10px;
    font-size: 13px;
    color: #6c757d;
}

.novels-ranking-page.dark-theme .alternative-titles {
    color: #a0a0a0;
}

.alt-title {
    margin-bottom: 3px;
}

.alt-title-label {
    font-weight: 600;
}

.ranking-novel-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 14px;
    color: #6c757d;
}

.novels-ranking-page.dark-theme .ranking-novel-meta {
    color: #a0a0a0;
}

.views-count i, .rating i {
    color: var(--bs-primary);
    margin-right: 3px;
}

.ranking-novel-genres {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.genre-badge {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    color: var(--bs-primary);
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.genre-badge:hover {
    background-color: var(--bs-primary);
    color: white;
}

.ranking-novel-excerpt {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.5;
    margin-top: 10px;
    margin-bottom: 10px;
}

.novels-ranking-page.dark-theme .ranking-novel-excerpt {
    color: #a0a0a0;
}

.novel-translators {
    margin-top: 10px;
    font-size: 13px;
}

.translators-label {
    font-weight: 600;
    margin-right: 5px;
    color: #6c757d;
}

.novels-ranking-page.dark-theme .translators-label {
    color: #a0a0a0;
}

.translators-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.translator-link {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    text-decoration: none;
    color: var(--bs-primary);
}

.translator-link:hover {
    text-decoration: underline;
}

.no-novels-message {
    text-align: center;
    padding: 50px 0;
    color: #6c757d;
    font-size: 18px;
}

.novels-ranking-page.dark-theme .no-novels-message {
    color: #a0a0a0;
}

/* تنسيقات للموبايل */
@media (max-width: 768px) {
    .novels-ranking-page {
        padding: 20px 15px;
    }
    
    .ranking-novel-card {
        flex-direction: column;
    }
    
    .ranking-novel-thumbnail {
        width: 100%;
        height: 200px;
    }
    
    .ranking-number {
        top: 10px;
        right: 10px;
        left: auto;
    }
    
    .ranking-novel-title {
        font-size: 16px;
        margin-top: 5px;
    }
    
    .ranking-tab {
        padding: 8px 15px;
        font-size: 16px;
    }
    
    .translators-list {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<?php get_footer(); ?>
