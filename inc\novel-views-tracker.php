<?php
/**
 * نظام تتبع مشاهدات الروايات وتحديث التصنيفات
 */

/**
 * تسجيل مشاهدة للرواية
 */
function sekaiplus_track_novel_view($novel_id) {
    // تجاهل المشاهدات من المسؤولين والمحررين
    if (current_user_can('edit_posts')) {
        return;
    }
    
    // التحقق من أن المشاهدة لم تتم تسجيلها بالفعل في نفس الجلسة
    $viewed_novels = isset($_COOKIE['viewed_novels']) ? explode(',', $_COOKIE['viewed_novels']) : array();
    
    if (in_array($novel_id, $viewed_novels)) {
        return;
    }
    
    // تحديث المشاهدات الإجمالية
    $total_views = get_post_meta($novel_id, 'novel_views_total', true);
    $total_views = empty($total_views) ? 1 : intval($total_views) + 1;
    update_post_meta($novel_id, 'novel_views_total', $total_views);
    
    // تحديث المشاهدات اليومية
    $daily_views = get_post_meta($novel_id, 'novel_views_daily', true);
    $daily_views = empty($daily_views) ? 1 : intval($daily_views) + 1;
    update_post_meta($novel_id, 'novel_views_daily', $daily_views);
    
    // تحديث المشاهدات الأسبوعية
    $weekly_views = get_post_meta($novel_id, 'novel_views_weekly', true);
    $weekly_views = empty($weekly_views) ? 1 : intval($weekly_views) + 1;
    update_post_meta($novel_id, 'novel_views_weekly', $weekly_views);
    
    // تحديث المشاهدات الشهرية
    $monthly_views = get_post_meta($novel_id, 'novel_views_monthly', true);
    $monthly_views = empty($monthly_views) ? 1 : intval($monthly_views) + 1;
    update_post_meta($novel_id, 'novel_views_monthly', $monthly_views);
    
    // تسجيل الرواية كمشاهَدة في هذه الجلسة
    $viewed_novels[] = $novel_id;
    setcookie('viewed_novels', implode(',', $viewed_novels), time() + 86400, '/');
}

/**
 * تسجيل مشاهدة عند زيارة صفحة الرواية
 */
function sekaiplus_track_novel_view_on_visit() {
    if (is_singular('novel')) {
        sekaiplus_track_novel_view(get_the_ID());
    }
}
add_action('wp', 'sekaiplus_track_novel_view_on_visit');

/**
 * إعادة تعيين المشاهدات اليومية في منتصف الليل
 */
function sekaiplus_schedule_daily_views_reset() {
    if (!wp_next_scheduled('sekaiplus_reset_daily_views')) {
        wp_schedule_event(strtotime('today 23:59:59'), 'daily', 'sekaiplus_reset_daily_views');
    }
}
add_action('wp', 'sekaiplus_schedule_daily_views_reset');

/**
 * إعادة تعيين المشاهدات الأسبوعية يوم الأحد
 */
function sekaiplus_schedule_weekly_views_reset() {
    if (!wp_next_scheduled('sekaiplus_reset_weekly_views')) {
        wp_schedule_event(strtotime('sunday 23:59:59'), 'weekly', 'sekaiplus_reset_weekly_views');
    }
}
add_action('wp', 'sekaiplus_schedule_weekly_views_reset');

/**
 * إعادة تعيين المشاهدات الشهرية في نهاية الشهر
 */
function sekaiplus_schedule_monthly_views_reset() {
    if (!wp_next_scheduled('sekaiplus_reset_monthly_views')) {
        wp_schedule_event(strtotime('last day of this month 23:59:59'), 'monthly', 'sekaiplus_reset_monthly_views');
    }
}
add_action('wp', 'sekaiplus_schedule_monthly_views_reset');

/**
 * إعادة تعيين المشاهدات اليومية
 */
function sekaiplus_reset_daily_views() {
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    foreach ($novels as $novel_id) {
        update_post_meta($novel_id, 'novel_views_daily', 0);
    }
}
add_action('sekaiplus_reset_daily_views', 'sekaiplus_reset_daily_views');

/**
 * إعادة تعيين المشاهدات الأسبوعية
 */
function sekaiplus_reset_weekly_views() {
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    foreach ($novels as $novel_id) {
        update_post_meta($novel_id, 'novel_views_weekly', 0);
    }
}
add_action('sekaiplus_reset_weekly_views', 'sekaiplus_reset_weekly_views');

/**
 * إعادة تعيين المشاهدات الشهرية
 */
function sekaiplus_reset_monthly_views() {
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    foreach ($novels as $novel_id) {
        update_post_meta($novel_id, 'novel_views_monthly', 0);
    }
}
add_action('sekaiplus_reset_monthly_views', 'sekaiplus_reset_monthly_views');

/**
 * إضافة فترة زمنية شهرية للجدولة
 */
function sekaiplus_add_monthly_cron_schedule($schedules) {
    $schedules['monthly'] = array(
        'interval' => 2592000, // 30 يوم
        'display' => __('شهرياً', 'sekaiplus')
    );
    return $schedules;
}
add_filter('cron_schedules', 'sekaiplus_add_monthly_cron_schedule');

// تم حذف دالة التقييم المتضاربة - الدالة الصحيحة موجودة في functions.php
