/**
 * u0645u0644u0641 u062cu0627u0641u0627u0633u0643u0631u064au0628u062a u0644u062au062du062fu064au062b u0639u062fu062f u0627u0644u0641u0635u0648u0644 u0648u0627u0644u062au0642u064au064au0645u0627u062a u0644u0644u0631u0648u0627u064au0627u062a
 */
jQuery(document).ready(function($) {
    // u062au062du062fu064au062b u0639u062fu062f u0627u0644u0641u0635u0648u0644 u0648u0627u0644u062au0642u064au064au0645u0627u062a u0644u0643u0644 u0628u0637u0627u0642u0629 u0631u0648u0627u064au0629
    function updateNovelCards() {
        $('.novel-card').each(function() {
            var card = $(this);
            var novelId = card.data('novel-id');
            
            if (novelId) {
                // u062au062du062fu064au062b u0639u062fu062f u0627u0644u0641u0635u0648u0644
                updateChapterCount(novelId, card);
                
                // u062au062du062fu064au062b u0627u0644u062au0642u064au064au0645
                updateRating(novelId, card);
            }
        });
    }
    
    // u062au062du062fu064au062b u0639u062fu062f u0627u0644u0641u0635u0648u0644
    function updateChapterCount(novelId, card) {
        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_accurate_chapter_count',
                novel_id: novelId,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success && response.data) {
                    // u062au062du062fu064au062b u0627u0644u0639u0646u0635u0631 u0627u0644u0645u0646u0627u0633u0628 u0641u064a u0627u0644u0628u0637u0627u0642u0629
                    card.find('.chapter-count').text(response.data + ' u0641u0635u0644');
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating chapter count:', error);
            }
        });
    }
    
    // u062au062du062fu064au062b u0627u0644u062au0642u064au064au0645
    function updateRating(novelId, card) {
        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_accurate_rating',
                novel_id: novelId,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    var rating = parseFloat(response.data);
                    var ratingContainer = card.find('.rating-container');
                    var ratingValue = card.find('.rating-value');
                    
                    // u062au062du062fu064au062b u0642u064au0645u0629 u0627u0644u062au0642u064au064au0645
                    if (rating > 0) {
                        ratingValue.text(rating.toFixed(1));
                        ratingContainer.show();
                    } else {
                        ratingContainer.hide();
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating rating:', error);
            }
        });
    }
    
    // u062au0634u063au064au0644 u0627u0644u062au062du062fu064au062b u0628u0639u062f u062au062du0645u064au0644 u0627u0644u0635u0641u062du0629
    $(window).on('load', function() {
        // u062au0623u062eu064au0631 u0628u0633u064au0637 u0644u0644u062au0623u0643u062f u0645u0646 u062au062du0645u064au0644 u062cu0645u064au0639 u0627u0644u0639u0646u0627u0635u0631
        setTimeout(function() {
            updateNovelCards();
        }, 500);
    });
});
