/* تصميم صفحة سياسة الخصوصية */
:root {
    --privacy-primary: #3498db;
    --privacy-secondary: #2ecc71;
    --privacy-accent: #f39c12;
    --privacy-dark: #343a40;
    --privacy-light: #ecf0f1;
    --privacy-bg: #f8f9fa;
    --privacy-card: #ffffff;
    --privacy-text: #2c3e50;
    --privacy-text-light: #7f8c8d;
    --privacy-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    --privacy-border-radius: 15px;
}

/* دعم الوضع الداكن */
.dark-mode {
    --privacy-bg: #1a1a2e;
    --privacy-card: #343a40;
    --privacy-text: #e3e3e3;
    --privacy-text-light: #a8a8a8;
    --privacy-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.dark-mode .privacy-policy-header {
    background: linear-gradient(135deg, #2c3e50, #1a1a2e);
}

.dark-mode .privacy-section h2 {
    color: #3498db;
}

.dark-mode .privacy-section h3 {
    color: #2ecc71;
}

.dark-mode .privacy-section li:before {
    color: #f39c12;
}

.dark-mode .lang-btn {
    background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .lang-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dark-mode .lang-btn.active {
    background-color: #3498db;
    color: white;
}

.privacy-policy-container {
    background-color: var(--privacy-bg);
    color: var(--privacy-text);
    font-family: 'Cairo', sans-serif;
    padding-bottom: 50px;
}

.privacy-policy-header {
    background: linear-gradient(135deg, var(--privacy-primary), var(--privacy-secondary));
    color: white;
    padding: 60px 0 40px;
    margin-bottom: 40px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--privacy-shadow);
}

.privacy-policy-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 Z" fill="rgba(255,255,255,0.1)"/></svg>');
    background-size: 100% 100%;
}

.privacy-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
}

.language-switcher {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    position: relative;
}

.lang-btn {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.lang-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.4s ease;
    z-index: -1;
}

.lang-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.lang-btn:hover::before {
    transform: translateX(0);
}

.lang-btn.active {
    background-color: white;
    color: var(--privacy-primary);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.lang-btn.active::before {
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.2), rgba(255,255,255,0));
    transform: translateX(100%);
    transition: transform 1.5s ease;
}

.privacy-policy-content {
    background-color: var(--privacy-card);
    border-radius: var(--privacy-border-radius);
    padding: 40px;
    box-shadow: var(--privacy-shadow);
    margin-bottom: 30px;
    position: relative;
}

.privacy-section {
    margin-bottom: 40px;
    animation: fadeIn 0.5s ease-in-out;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.privacy-section:last-child {
    margin-bottom: 0;
}

.privacy-section:hover h2 {
    transform: translateY(-2px);
    text-shadow: 0 2px 10px rgba(52, 152, 219, 0.3);
}

.privacy-section h2 {
    color: var(--privacy-primary);
    font-size: 1.8rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--privacy-accent);
    display: inline-block;
}

.privacy-section h3 {
    color: var(--privacy-secondary);
    font-size: 1.4rem;
    margin: 25px 0 15px;
}

.privacy-section p {
    margin-bottom: 15px;
    line-height: 1.7;
}

.privacy-section ul {
    padding-right: 20px;
    margin-bottom: 20px;
}

.privacy-section li {
    margin-bottom: 10px;
    position: relative;
    padding-right: 25px;
}

.privacy-section li:before {
    content: '•';
    color: var(--privacy-accent);
    font-weight: bold;
    position: absolute;
    right: 0;
}

/* قواعد اتجاه النص للغات المختلفة */
.privacy-content-ar {
    display: none;
    direction: rtl;
    text-align: right;
}

.privacy-content-en, .privacy-content-ja {
    display: none;
    direction: ltr;
    text-align: left;
}

.privacy-content-ar.active, .privacy-content-en.active, .privacy-content-ja.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

/* تعديل اتجاه العناصر حسب اللغة */
.privacy-content-ar h2, .privacy-content-ar h3, .privacy-content-ar p, .privacy-content-ar ul {
    text-align: right;
}

.privacy-content-en h2, .privacy-content-en h3, .privacy-content-en p, .privacy-content-en ul,
.privacy-content-ja h2, .privacy-content-ja h3, .privacy-content-ja p, .privacy-content-ja ul {
    text-align: left;
}

/* تعديل اتجاه النقاط في القوائم */
.privacy-content-ar .privacy-section li:before {
    right: 0;
}

.privacy-content-en .privacy-section li:before,
.privacy-content-ja .privacy-section li:before {
    left: 0;
    right: auto;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .privacy-policy-header {
        padding: 40px 0 30px;
    }

    .privacy-title {
        font-size: 2rem;
    }

    .privacy-policy-content {
        padding: 25px;
    }

    .privacy-section h2 {
        font-size: 1.5rem;
    }

    .privacy-section h3 {
        font-size: 1.2rem;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    .language-switcher {
        flex-direction: column;
        align-items: center;
    }

    .lang-btn {
        width: 100%;
        max-width: 200px;
    }
}
